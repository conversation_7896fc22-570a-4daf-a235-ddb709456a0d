# frozen_string_literal: true

namespace :user do
  desc "Update confirmation fields and seed email templates"
  task update_confirmation_and_seed: :environment do
    Spree::User.find_each do |user|
      user.update_columns(
        confirmation_sent_at: user.created_at,
        confirmed_at: user.created_at,
      )
    end
    # Seed email templates
    AxelSpree::Spree::Seeds::EmailTemplate.call
  end
end
