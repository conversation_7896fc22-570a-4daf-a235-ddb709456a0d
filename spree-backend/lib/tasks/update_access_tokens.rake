# frozen_string_literal: true

namespace :oauth do
  desc "Update all spree_oauth_access_tokens with admin scopes and resource_owner_id = 1"
  task update_tokens: :environment do
    # Update all records in spree_oauth_access_tokens
    affected_rows = Spree::OauthAccessToken.update_all(scopes: 'admin', resource_owner_id: 1)

    puts "#{affected_rows} spree_oauth_access_tokens records have been updated with 'admin' scopes and resource_owner_id = 1."
  end
end
