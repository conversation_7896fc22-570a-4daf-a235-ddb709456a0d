# frozen_string_literal: true

namespace :update_weight_field do
  desc "Update Weight Field Into lbs and oz In Product Model"
  task into_lbs_to_oz: :environment do
    products = Spree::Product.all
    products.each do |product|
      weight = product.weight
      if product.listings&.size.to_i > 0
        integer_part, decimal_part = product.weight.divmod(1)
        if decimal_part >= 0.1 && decimal_part <= 0.15
          decimal_part *= 10
        end
        weight = integer_part + decimal_part * 10 / 16
      end

      next if weight.zero?

      weight_pounds, weight_ounces = weight.divmod(1)
      product.update(lbs: weight_pounds, oz: weight_ounces * 16)
    end
  end
end
