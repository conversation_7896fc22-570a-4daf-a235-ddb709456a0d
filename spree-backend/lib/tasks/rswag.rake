# frozen_string_literal: true

if ["production", "staging"].exclude?(ENV.fetch("RAILS_ENV", "development"))
  namespace :rswag do
    namespace :specs do
      desc "Generate Swagger JSON files from integration specs"
      RSpec::Core::RakeTask.new("swaggerize") do |t|
        t.pattern = ENV.fetch(
          "PATTERN",
          "spec/integration/**/*_spec.rb",
        )

        t.rspec_opts = ["--format Rswag::Specs::SwaggerFormatter", "--order defined"]
      end
    end
  end
end
