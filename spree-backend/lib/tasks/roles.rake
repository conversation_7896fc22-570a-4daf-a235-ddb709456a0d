# frozen_string_literal: true

namespace :spree do
  desc "Create roles and permissions"
  task create_roles_and_permissions: :environment do
    Spree::Role.where(name: "admin").first_or_create!
    Spree::Role.where(name: "store_owner").first_or_create!
    Spree::Role.where(name: "store_employee").first_or_create!
    Spree::Role.where(name: "store_customer").first_or_create!

    create_permission_for_roles
  end

  def create_permission_for_roles
    permission_columns = Spree::Permission.column_names
    permission_columns -= ["id"]

    roles = {
      admin: "admin",
      store_owner: "store_owner",
      store_employee: "store_employee",
      store_customer: "store_customer",
    }

    roles.each do |role_name, role| # rubocop:disable Lint/UnusedBlockArgument
      role_instance = Spree::Role.find_by(name: role)
      next unless role_instance

      permission_params = permission_columns.map { |column| [column.to_sym, true] }.to_h
      existing_permissions = role_instance.permission

      unless existing_permissions.present? # rubocop:disable Rails/Blank,Style/InvertibleUnlessCondition
        role_instance.create_permission(permission_params)
      end
    end
  end
end
