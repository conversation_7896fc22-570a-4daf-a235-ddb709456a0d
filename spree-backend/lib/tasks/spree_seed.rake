namespace :spree_sample do
  desc "Safely load all Spree sample data one by one, skipping errors"
  task :safe_load => :environment do
    spree_sample_path = Gem.loaded_specs["spree_sample"].full_gem_path
    samples_dir = File.join(spree_sample_path, "db", "samples")

    samples = Dir.entries(samples_dir)
                 .select { |f| File.extname(f) == ".rb" }
                 .map { |f| File.basename(f, ".rb") }

    puts "Found samples: #{samples.join(', ')}"

    samples.each do |sample|
      begin
        puts "Loading sample: #{sample}"
        Spree::Sample.load_sample(sample)
        puts "Successfully loaded: #{sample}"
      rescue => e
        puts "Skipping sample: #{sample} due to error: #{e.message}"
      end
    end
  end
end
