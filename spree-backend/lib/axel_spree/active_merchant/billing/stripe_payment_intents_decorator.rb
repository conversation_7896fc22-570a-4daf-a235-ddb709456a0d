# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module ActiveMerchant
    module Billing
      module StripePaymentIntentsDecorator
        def create_intent(money, payment_method, options = {})
          require "stripe"
          Stripe.api_key = payment_method.get_preference(:secret_key)
          Stripe.api_version = '2022-11-15'

          order = ::Spree::Order.find_by(number: options[:order_id].split("-")[0])

          # create customer on stripe
          result = Stripe::Customer.create({
            name: order&.name,
            email: options[:email],
          })

          if order.line_items.any?(&:subscription_enabled)
            # setup intent on stripe for future payments
            Stripe::SetupIntent.create({
              payment_method_types: ["card"],
              customer: result[:id],
            })
          end

          setup_future_usage = order.line_items.any?(&:subscription_enabled) ? "off_session" : "on_session"

          shipping_address = options[:shipping_address]
          response = Stripe::PaymentIntent.create(
            customer: result[:id],
            setup_future_usage: setup_future_usage,
            amount: money,
            currency: options[:currency],
            automatic_payment_methods: {
              enabled: true,
            },
            # payment_method_types: %w(us_bank_account, link),
            shipping: if shipping_address
                        {
                          address: {
                            line1: options[:shipping_address][:address1],
                            city: options[:shipping_address][:city],
                            country: options[:shipping_address][:country],
                            line2: options[:shipping_address][:address2],
                            postal_code: options[:shipping_address][:zip],
                            state: options[:shipping_address][:state],
                          },
                          name: options[:shipping_address][:name],
                          phone: options[:shipping_address][:phone],
                        }
                      end,
          )

          success = success_from(response, options)

          card = card_from_response(response)
          gateway = ::ActiveMerchant::Billing::StripePaymentIntentsGateway
          avs_code = gateway::AVS_CODE_TRANSLATOR["line1: #{card["address_line1_check"]}, zip: #{card["address_zip_check"]}"]
          cvc_code = gateway::CVC_CODE_TRANSLATOR[card["cvc_check"]]
          ::ActiveMerchant::Billing::Response.new(
            success,
            message_from(success, response),
            JSON.parse(response),
            test: !response[:livemode],
            authorization: response[:id],
            avs_result: { code: avs_code },
            cvv_result: cvc_code,
            emv_authorization: emv_authorization_from_response(response),
            error_code: success ? nil : error_code_from(response),
          )
        end

        def void(intent_id, options = {})
          post = {}
          payment = ::Spree::Payment.find_by(response_code: intent_id)
          refund_reason_id = ::Spree::RefundReason.last.id
          payment.refunds.create!(amount: payment.credit_allowed, refund_reason_id: refund_reason_id)
          # post[:cancellation_reason] = options[:cancellation_reason] if
          #   ALLOWED_CANCELLATION_REASONS.include?(options[:cancellation_reason])
          commit(:post, "payment_intents/#{intent_id}/cancel", post, options)
        end

        def success_from(response, options)
          if response["status"] == "requires_action" && !options[:execute_threed]
            response["error"] = {}
            response["error"]["message"] =
              "Received unexpected 3DS authentication response. Use the execute_threed option to initiate a proper 3DS flow."
            return false
          end

          response["status"] != "failed"
        end

        def card_from_response(response)
          response = response.as_json if response.respond_to?(:as_json)
          response["card"] || response["active_card"] || response["source"] ||
          (response["charges"]["data"][0]["payment_method_details"]["card"]["checks"] rescue nil) ||
          (response["latest_attempt"]["payment_method_details"]["card"]["checks"] rescue nil) || {}
        end
      end
    end
  end
end

ActiveMerchant::Billing::StripePaymentIntentsGateway.prepend(AxelSpree::ActiveMerchant::Billing::StripePaymentIntentsDecorator)
