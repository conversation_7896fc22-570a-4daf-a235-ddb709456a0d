# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module SpreeTaxjar
    module CategoriesDecorator
      def self.prepended(base)
        def base.update(tax_categories = [], store)
          tax_categories = default_tax_categories if tax_categories.empty?
          tax_categories.each do |tax_category|
            tax_category_attributes = convert_to_hash(tax_category)
            tax_category_in_db = ::Spree::TaxCategory.where(name: name(tax_category_attributes), store: store).first
            if tax_category_in_db.present?
              Rails.logger.debug { "TaxCategory:: Update:: #{tax_category_in_db.inspect}" }
              tax_category_in_db.update!(transform(tax_category_attributes))
            else
              transformed_tax_category = transform(tax_category_attributes)
              transformed_tax_category[:store] = store
              new_tax_category = ::Spree::TaxCategory.create!(transformed_tax_category)
              Rails.logger.debug { "TaxCategory:: Create:: #{new_tax_category.inspect}" }
            end
          end
        end

        def base.refresh(store)
          return unless store.taxjar_setting

          Rails.logger.debug("Taxjar:: Categories:: API Call started !!")
          client = ::Taxjar::Client.new(
            api_key: store.taxjar_setting.taxjar_api_key,
            api_url: api_url(store.taxjar_setting.taxjar_sandbox_environment_enabled),
          )
          tax_categories = client.categories
          Rails.logger.debug("Taxjar:: Categories:: Update Started")
          update(tax_categories, store)
        end

        def base.api_url(taxjar_sandbox_environment_enabled)
          taxjar_sandbox_environment_enabled ? ::Taxjar::API::Request::SANDBOX_API_URL : ::Taxjar::API::Request::DEFAULT_API_URL
        end
      end
    end
  end
end

SpreeTaxjar::Categories.prepend(AxelSpree::SpreeTaxjar::CategoriesDecorator)
