# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Net
    module HttpGenericRequestDecorator
      def initialize(m, reqbody, resbody, uri_or_path, initheader = nil)
        @use_default_content_type = true
        super
      end

      def disable_default_content_type
        @use_default_content_type = false
      end

      def supply_default_content_type
        return unless @use_default_content_type

        super
      end
    end
  end
end

Net::HTTPGenericRequest.prepend(AxelSpree::Net::HttpGenericRequestDecorator)
