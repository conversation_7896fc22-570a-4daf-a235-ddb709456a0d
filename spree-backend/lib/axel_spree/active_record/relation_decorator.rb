# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module ActiveRecord
    module RelationDecorator
      include ::Spree::Admin::ElasticsearchHelper
      def self.prepended(base)
        base.attr_reader(:elasticsearch)
      end

      def initialize(klass, table: klass.arel_table, predicate_builder: klass.predicate_builder, values: {})
        @klass = klass
        @table = table
        @values = values
        @loaded = false
        @predicate_builder = predicate_builder
        @delegate_to_klass = false
        @elasticsearch = false
        @es = nil
        @base_collection = nil
      end

      def use_elasticsearch(es, collection = nil)
        @elasticsearch = true
        @es = es
        @base_collection = collection
      end

      def unuse_elasticsearch
        @elasticsearch = false
        @base_collection = nil
      end

      def count(...)
        @elasticsearch ? @es.count : super(...)
      end

      def any?(...)
        @elasticsearch ? @es.any? : super(...)
      end

      def load(...)
        if @elasticsearch && loaded? != true
          opt = {
            from: offset_value.nil? ? 0 : offset_value,
            size: limit_value.nil? ? 25 : limit_value,
          }
          ids = @es.from(opt[:from]).size(opt[:size]).ids

          collection = @base_collection.nil? ? clone : @base_collection
          new_scope = collection.offset(0).limit(values[:limit]).where(id: ids)
          new_scope.unuse_elasticsearch

          ret = new_scope.reload
          load_records(ret.records)
          self
        else
          super(...)
        end
      end
    end
  end
end

ActiveRecord::Relation.prepend(AxelSpree::ActiveRecord::RelationDecorator)
