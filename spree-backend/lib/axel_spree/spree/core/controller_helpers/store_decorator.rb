# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Core
      module Controller<PERSON>elpers
        module StoreDecorator
          def current_store
            @current_store ||= current_store_finder.new(url: request.headers["X-SPREE-STORE-HOST"] || request.env["SERVER_NAME"]).execute
          end
        end
      end
    end
  end
end

Spree::Core::ControllerHelpers::Store.prepend(AxelSpree::Spree::Core::ControllerHelpers::StoreDecorator)
