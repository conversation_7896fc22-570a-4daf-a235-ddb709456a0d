# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Checkout
      module UpdateDecorator
        def call(order:, params:, permitted_attributes:, request_env:)
          # May need to permit parameter for selected_shipping_method
          if order.local_pickup.present? && params[:order][:selected_shipping_method].present? &&
              params[:order][:payments_attributes].blank?
            order.update(selected_shipping_method: params[:order][:selected_shipping_method][:id])
            response = if selected_shipping_method(order)
              local_pickup_checkout_process(order)
            else
              failure(
                order,
                I18n.t("spree.shipping_method_error"),
              )
            end
            return response
          end
          if order.local_pickup.present? && selected_shipping_method(order) && params[:order][:payments_attributes].blank?
            if params[:order][:bill_address_attributes].present?
              replace_country_iso_with_id(params[:order][:bill_address_attributes])
              # rubocop:disable Rails/SkipsModelValidations
              if order.update_from_params(
                params,
                permitted_attributes,
                request_env,
              ) && order.update_columns(state: "payment")
                order.log_state_changes(state_name: "order", old_state: "delivery", new_state: "payment")
                success(order)
              else
                failure(order)
              end
              # rubocop:enable Rails/SkipsModelValidations
            end
          else
            normal_shipping_checkout_process(order:, params:, permitted_attributes:, request_env:)
          end
        end

        private

        def normal_shipping_checkout_process(order:, params:, permitted_attributes:, request_env:)
          ship_changed = address_with_country_iso_present?(params, "ship")
          bill_changed = address_with_country_iso_present?(params, "bill")
          params[:order][:ship_address_attributes] =
            replace_country_iso_with_id(params[:order][:ship_address_attributes]) if ship_changed
          params[:order][:bill_address_attributes] =
            replace_country_iso_with_id(params[:order][:bill_address_attributes]) if bill_changed
          # order.state = 'address' if (ship_changed || bill_changed) && order.has_checkout_step?('address')
          order.state = "address" if ship_changed && order.has_checkout_step?("address") # only change to address state when ship_changed
          order.state = "delivery" if selected_shipping_rate_present?(params) && order.has_checkout_step?("delivery")
          return success(order) if order.update_from_params(params, permitted_attributes, request_env)

          failure(order)
        end

        def local_pickup_checkout_process(order)
          email = "#{order.token}@mail.co"
          order.update(email: email) if order.email.nil?
          order.log_state_changes(
            state_name: "order",
            old_state: "cart",
            new_state: "address",
          ) if order.has_checkout_step?("address") && order.update(state: "address")

          shipping_method = ::Spree::ShippingMethod.find(order.selected_shipping_method)
          pickup_address = ::Spree::Address.new(
            firstname: "local",
            lastname: "pickup",
            address1: shipping_method.address_details,
            city: ::Spree::State.find(shipping_method.pickup_state).name,
            zipcode: shipping_method.pickup_zip,
            state_id: shipping_method.pickup_state,
            country_id: shipping_method.pickup_country,
          )
          pickup_address.validate_phone = false
          pickup_address.save

          order.ship_address = pickup_address
          order.save

          order.create_proposed_shipments
          order.set_shipments_cost
          order.create_tax_charge!
          order.apply_free_shipping_promotions

          shipping_rates = ::Spree::ShippingRate.find_by(
            shipment_id: order.shipment_ids,
            shipping_method_id: order.selected_shipping_method,
          )
          if shipping_rates.blank?
            order.shipments.take.shipping_methods << ::Spree::ShippingMethod.find(order.selected_shipping_method)
          end
          order.log_state_changes(
            state_name: "order",
            old_state: "address",
            new_state: "delivery",
          ) if order.has_checkout_step?("delivery") && order.shipments.take.shipping_rates.take.update(
            selected: true, buyer_selected: true,
          ) && order.update(state: "delivery")

          success(order)
        end

        def selected_shipping_method(order)
          ::Spree::ShippingMethod.find_by(id: order.selected_shipping_method)&.local_pickup.present?
        end
      end
    end
  end
end

Spree::Checkout::Update.prepend(AxelSpree::Spree::Checkout::UpdateDecorator)
