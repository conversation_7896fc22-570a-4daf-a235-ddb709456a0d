# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module ConfigurationDecora<PERSON>
        def self.prepended(base)
          base.preference(:api_v3_collection_cache_namespace, :string, default: "api_v3_collection_cache")
          base.preference(:api_v3_collection_cache_ttl, :integer, default: "3600") # 1 hour in seconds
        end
      end
    end
  end
end

::Spree::Api::Configuration.prepend(AxelSpree::Spree::Api::ConfigurationDecorator)
