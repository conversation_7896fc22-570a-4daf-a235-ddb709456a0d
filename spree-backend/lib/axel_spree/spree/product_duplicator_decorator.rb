# frozen_string_literal: true

module Axel<PERSON>pree
  module Spree
    module ProductDuplicatorDecorator
      def api_duplicate(params)
        new_product = api_duplicate_product(params)
        # don't dup the actual variants, just the characterising types
        new_product.option_types = product.option_types if product.has_variants?
        # allow site to do some customization
        new_product.send(:duplicate_extra, product) if new_product.respond_to?(:duplicate_extra)
        new_product.save!
        new_product
      end

      protected

      def api_duplicate_product(params)
        product.dup.tap do |new_product|
          new_product.translations.each do |t|
            t.name = "COPY OF #{t.name}"
            t.slug = nil
          end
          new_product.taxons = product.taxons
          new_product.stores = product.stores
          new_product.created_at = nil
          new_product.deleted_at = nil
          new_product.updated_at = nil
          new_product.product_properties = reset_properties
          new_product.master = api_duplicate_master(params)
          if params[:product].present? && params[:product][:clone_variants].present?
            new_product.variants = product.variants.map { |variant| api_duplicate_variant(variant,params) }
          else
            # do nothing
          end
          if params[:product].present? && params[:product][:clone_description].present?
            new_product.description = product.description
          else
            new_product.description = ""
          end
        end
      end

      def api_duplicate_master(params)
        master = product.master
        master.dup.tap do |new_master|
          if params[:product].present? && params[:product][:clone_sku].present?
            new_master.sku = sku_generator(master.sku)
          else
            new_master.sku = ""
          end
          new_master.deleted_at = nil
          if params[:product].present? && params[:product][:clone_images].present?
            new_master.images = master.images.map { |image| duplicate_image image }
          else
            # do nothing
          end
          new_master.price = master.price
          new_master.currency = master.currency
        end
      end

      def api_duplicate_variant(variant, params)
        new_variant = variant.dup
        if params[:product].present? && params[:product][:clone_sku].present?
          new_variant.sku = sku_generator(new_variant.sku)
        else
          new_variant.sku = ""
        end

        if params[:product].present? && params[:product][:clone_images].present?
          new_variant.images = variant.images.map { |image| duplicate_image image }
        else
          # do nothing
        end

        new_variant.deleted_at = nil
        new_variant.option_values = variant.option_values.map { |option_value| option_value }
        new_variant
      end

      def sku_generator(sku)
        if sku.present?
          "COPY OF #{::Spree::Variant.unscoped.where('sku like ?', "%#{sku}").order(:created_at).last.sku}"
        else
          ""
        end
      end
    end
  end
end

Spree::ProductDuplicator.prepend(AxelSpree::Spree::ProductDuplicatorDecorator)
