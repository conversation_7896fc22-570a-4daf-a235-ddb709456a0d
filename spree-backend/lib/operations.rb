# frozen_string_literal: true

# Small foundation for all business operations classes.
#
# Typical definition of operation class will go like this (subject to change!):
#
# ```ruby
# class Model::DoSomething < Operations::Base
#   subject :subject_name
#   param :param1, default: 5, &:to_i
#   param(:param2) { |val| convert_somehow(val) }
#   param :param3 # no converter block, just passed as is
#
#   # this DSL will auto-define initialize looking like this:
#   #
#   #   def initialize(performer, subject_name:, param1: 5, param2: nil, param3: nil)
#   #
#   # ..and will run the converting blocks where they are defined
#
#   private
#
#   def allowed?
#     performer.admin? || performer.something_else? # if this will not be matched, ValidationError is raise
#   end
#
#   def validate!
#     invalid(param1: 'Explanation') if something
#   end
#
#   def _execute
#     # do the real work
#   end
# end
#
# # usage of this class:
# Model::DoSomething.new(current_user, subject_name: something, param1_name: something, param2_name: something).execute
# # or
# Model::DoSomething.new(current_user, params.to_unsafe_hash.symbolize_keys).execute
# ```
#
# **Performer** of operation should be "current user" where it makes sense, or `Operations.system`
# (currently it is just a synonym for `nil` performer, but better use this synonym) if the operation
# is called by internal logic.
#
module Operations
  class NotImplementedError < ::StandardError; end

  # Permissions error
  class NotAllowed < RuntimeError
    def initialize(user)
      @user = user
      super("Operation is not allowed for #{user}")
    end
  end

  # Thrown when mandatory parameter, described with param: DSL is not passed.
  class MissingParameter < ArgumentError
    attr_reader :param

    def initialize(param, message = nil)
      @param = param
      super(message || "Missing parameter: #{param}")
    end
  end

  # Validation error, handles message i18n (which it probably should not)
  class ValidationError < ArgumentError
    attr_reader :record, :errors

    def self.from_active_record(record)
      # new(
      #   record: record,
      #   **record.errors.messages.map { |field, errs| [field, errs.map { |e| "#{field.capitalize} #{e}" }.join(', ')] }.to_h
      # )
    end

    def initialize(errors)
      # @record = record
      @errors = errors.transform_values { |msg| msg.is_a?(Symbol) ? I18n.t("errors.#{msg}") : msg }
      super(@errors.values.join('. '))
    end
  end

  # Small logger class for operations, adds class name in the beginning of log line
  class TaggedLogger < ActiveSupport::Logger
    attr_reader :logger, :tag

    def initialize(tag, logger)
      super(nil, nil)
      @tag = tag
      @logger = logger
    end

    def add(severity, msg = nil, progname = nil)
      logger.tagged(tag) { logger.add severity, msg, progname }
    end
  end

  class Errors < Hash
    def add(key, value, _opts = {})
      self[key] ||= []
      self[key] << value
      self[key].uniq!
    end

    def add_multiple_errors(errors_hash)
      # errors_hash.each do |key, values|
      #   SimpleCommand::Utils.array_wrap(values).each { |value| add key, value }
      # end
    end

    def each
      each_key do |field|
        self[field].each { |message| yield field, message }
      end
    end

    def full_messages
      map { |attribute, message| full_message(attribute, message) }
    end

    private

    def full_message(attribute, message)
      return message if attribute == :base
      attr_name = attribute.to_s.tr('.', '_').capitalize
      "%s %s" % [attr_name, message]
    end
  end

  def self.system
    nil # for now! in future, it could be special object
  end

  # Base for all the operations. See {Operations} module docs for usage.
  class Base
    extend Memoist
    
    ABSENT = Object.new.freeze

    class_attribute :_subject, instance_accessor: false
    class_attribute :_params, instance_accessor: false
    # Rails 5.1 doen't have :default options for class_attribute method
    self._subject = nil
    self._params = {}

    class << self
      # DSL for specify subject and params and automatically declare attr accessors and assign params to it

      def subject(name)
        self._subject = name.to_sym
        attr_reader name.to_sym
      end

      def param(name, **options, &converter)
        options[:converter] = converter if block_given?

        attr_reader name.to_sym

        # Replace _params because we need to invoke class attribute setter
        # If we are use .update we will have one params instance for all child classes.
        self._params = _params.merge(name.to_sym => options)
      end

      def execute(*args, **kwargs)
        new(*args, **kwargs).execute
      end
    end

    attr_reader :performer

    def initialize(performer = Operations.system, **attrs)
      @performer = performer

      initialize_logger(attrs)
      initialize_subject(attrs)
      initialize_attrs(attrs)
    end

    def subject
      fail 'No subject declared' unless self.class._subject

      instance_variable_get "@#{self.class._subject}"
    end

    def execute
      check_rights!
      validate!
      _execute
    rescue ActiveRecord::RecordInvalid => e
      raise Operations::ValidationError.from_active_record(e.record)
    end

    def errors
      @errors ||= Errors.new
    end

    private

    def _subject
      self.class._subject
    end

    def initialize_subject(attrs)
      return if _subject.blank?

      value = attrs.fetch(_subject) { fail ArgumentError, "#{_subject} is missing" }
      fail ArgumentError, "#{_subject} attrs is not present" if value.blank?

      instance_variable_set "@#{_subject}", value
    end

    def initialize_attrs(attrs)
      self.class._params.each do |name, definition|
        initialize_attr(attrs, name, **definition)
      end
    end

    # Notice that:
    # * default could be proc, in this case it is evaluated
    # * converter is performed only on NON-DEFAULT value. This allows to have
    #
    #     param :foo, default: nil, &JSON.method(:parse)
    #
    #   -- e.g., "it could be absent, but if not, should be parsed"
    def initialize_attr(attrs, name, default: ABSENT, converter: nil, **)
      if !attrs.key?(name) && default == ABSENT
        fail MissingParameter.new(name, "Operation parameter :#{name} missing, passed params: #{attrs.keys}")
      end

      value =
        if attrs.key?(name)
          attrs[name].then { |val| converter ? instance_exec(val, &converter) : val }
        else
          default.then { |val| val.is_a?(Proc) ? instance_eval(&val) : val }
        end
      instance_variable_set "@#{name}", value
    end

    def initialize_logger(attrs)
      return unless attrs.key?(:logger)

      instance_variable_set('@logger', attrs[:logger])
    end

    memoize def logger
      instance_variable_defined?('@logger') && instance_variable_get('@logger') || Rails.logger
    end

    def check_rights!
      fail NotAllowed, performer unless allowed?
    end

    def validate!; end

    def allowed?
      true
    end

    def system?
      performer.nil?
    end

    def _execute
      fail NotImplementedError
    end

    def invalid(**errors)
      fail ValidationError, **errors
    end
  end
end
