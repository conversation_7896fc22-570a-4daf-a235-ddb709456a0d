# frozen_string_literal: true

require "apartment/elevators/generic"
require "public_suffix"

module Apartment
  module Elevators
    #   Provides a rack based tenant switching solution based on subdomains
    #   Assumes that tenant name should match subdomain
    #
    class CustomStoreDomain < Generic
      def self.excluded_subdomains
        @excluded_subdomains ||= []
      end

      class << self
        attr_writer :excluded_subdomains
      end

      def parse_tenant_name(request)
        return "public" if request.host.match?(/ngrok-free\.app$/i)

        base_url = "#{request.scheme}://#{request.host_with_port}"
        Rails.logger.info "@@@@@@@@@@@@@Base URL: #{base_url}"
        if base_url == 'https://admin-preprod.axel.market'
          base_url = 'https://axel.market' # for amazon_hook
        end
        organisation = Spree::Organisation.find_by(custom_domain: base_url)
        request_subdomain = if organisation.blank?
          Rails.logger.info "@@@@@@@@@@@@@ Tenant found when organization was blank #{subdomain(request.host)}"
          subdomain(request.host)
        else
          Rails.logger.info "@@@@@@@@@@@@@ Tenant found from organization: #{organisation.subdomain}"
          organisation.subdomain
        end

        return 'public' if self.class.excluded_subdomains.include?(request_subdomain)

        RedisStore.get(request_subdomain).presence || request_subdomain
      end

      protected

      def subdomain(host)
        subdomains(host).first
      end

      def subdomains(host)
        host_valid?(host) ? parse_host(host) : []
      end

      def host_valid?(host)
        !ip_host?(host) && domain_valid?(host)
      end

      def ip_host?(host)
        !/^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/.match(host).nil?
      end

      def domain_valid?(host)
        PublicSuffix.valid?(host, ignore_private: true)
      end

      def parse_host(host)
        (PublicSuffix.parse(host, ignore_private: true).trd || "").split(".")
      end
    end
  end
end
