# frozen_string_literal: true

require "apartment/elevators/generic"
require "public_suffix"

module Apartment
  module Elevators
    #   Provides a rack based tenant switching solution based on subdomains
    #   Assumes that tenant name should match subdomain
    #
    class CustomStoreSubdomain < Generic
      def self.excluded_subdomains
        @excluded_subdomains ||= []
      end

      class << self
        attr_writer :excluded_subdomains
      end

      def parse_tenant_name(request)
        return "public" if request.host.match?(/ngrok-free\.app$/i)

        request_subdomain = subdomain(request.host)
        return "public" if self.class.excluded_subdomains.include?(request_subdomain)

        RedisStore.get(request_subdomain).presence || request_subdomain
      end

      protected

      def subdomain(host)
        subdomains(host).first
      end

      def subdomains(host)
        host_valid?(host) ? parse_host(host) : []
      end

      def host_valid?(host)
        !ip_host?(host) && domain_valid?(host)
      end

      def ip_host?(host)
        !/^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/.match(host).nil?
      end

      def domain_valid?(host)
        PublicSuffix.valid?(host, ignore_private: true)
      end

      def parse_host(host)
        (PublicSuffix.parse(host, ignore_private: true).trd || "").split(".")
      end
    end
  end
end
