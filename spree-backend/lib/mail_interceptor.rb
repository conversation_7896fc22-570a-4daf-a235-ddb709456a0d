# frozen_string_literal: true

class MailInterceptor
  SKIPPED_ACTIONS = [
    "reset_password_instructions",
    "notify_me_email",
    "send_confirmation_code",
    "seller_welcome_email",
    "store_employee_invite",
    "send_campaign_email",
  ].freeze

  def self.delivering_email(message)
    if message.respond_to?(:mailer_action) &&
        message.mailer_action.present? &&
        SKIPPED_ACTIONS.exclude?(message.mailer_action)

      email_template = Spree::EmailTemplate.find_by(
        template_name: message.mailer_action,
        store_id: message.store_id,
      )

      message.perform_deliveries = false if email_template.nil? || !email_template.active
    end
  end
end
