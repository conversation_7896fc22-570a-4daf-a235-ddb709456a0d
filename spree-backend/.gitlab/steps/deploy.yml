.template:
  stage: deploy
  needs:
    - build_amd64
  before_script:
    - docker login -u "$CI_REGISTRY_PULL_USER" -p "$CI_REGISTRY_PULL_PASSWORD" $CI_REGISTRY_PULL
  script:
    - chown -R gitlab-runner:gitlab-runner /home/<USER>
    - !-1 2>output1
    - source variables
    - docker pull ${IMAGE_PULL}@${IMAGE_HASH}
    - docker rm -f spree-backend-web spree-backend-worker
    - |
      docker run -d --name ${CI_PROJECT_NAME}-web \
        --restart unless-stopped \
        --memory $MEMORY_LIMIT --memory-swap $MEMORY_SWAP_LIMIT \
        --net=host \
        -p 4000:4000 \
        -v /opt/spree/storage:/app/storage \
        --env SENTRY_DSN=https://<EMAIL>/4506868748713984 \
        --env RAILS_ENV="${RAILS_ENV}" --env REDIS_URL="redis://127.0.0.1:6379/0" \
        --env DATABASE_URL="postgres://postgres:postgres@127.0.0.1/spree_starter" \
        --env DISABLE_SPRING="1" \
        --env SECRET_KEY_BASE="22d9b7aa31a1327bcc4b21e4638a25b0b13b3947a59f0009bda8f754bc95de606abe3f80242f1fb864984a71e1fc94c18a422b1094a223cd1defe3d4147bc995" \
        --env SIDEKIQ_USERNAME=admin \
        --env SIDEKIQ_PASSWORD=password \
        --env ELASTICSEARCH_HOST="${ELASTICSEARCH_HOST}" \
        --env DD_AGENT_HOST="localhost" \
        --env DD_TRACE_AGENT_PORT="8126" \
        --env DD_VERSION="${CI_COMMIT_TAG}" \
        --env DD_SERVICE="spree-backend" \
        --env AXEL_STRIPE_API_KEY="${AXEL_STRIPE_API_KEY}" \
        --env AXEL_STRIPE_WEBHOOK_SECRET="${AXEL_STRIPE_WEBHOOK_SECRET}" \
        ${IMAGE_PULL}@${IMAGE_HASH}
    - |
      docker run -d --name ${CI_PROJECT_NAME}-worker \
        --restart unless-stopped \
        --memory $MEMORY_LIMIT --memory-swap $MEMORY_SWAP_LIMIT \
        --net=host \
        -v /opt/spree/storage:/app/storage \
        --env SENTRY_DSN=https://<EMAIL>/4506868748713984 \
        --env RAILS_ENV="${RAILS_ENV}" \
        --env REDIS_URL="redis://127.0.0.1:6379/0" \
        --env DATABASE_URL="postgres://postgres:postgres@127.0.0.1/spree_starter" \
        --env DISABLE_SPRING="1" \
        --env SECRET_KEY_BASE="22d9b7aa31a1327bcc4b21e4638a25b0b13b3947a59f0009bda8f754bc95de606abe3f80242f1fb864984a71e1fc94c18a422b1094a223cd1defe3d4147bc995" \
        --env ELASTICSEARCH_HOST="${ELASTICSEARCH_HOST}" \
        --env DD_AGENT_HOST="localhost" \
        --env DD_TRACE_AGENT_PORT="8126" \
        --env DD_VERSION="${CI_COMMIT_TAG}" \
        --env DD_SERVICE="spree-backend-worker" \
        ${IMAGE_PULL}@${IMAGE_HASH} bash -c "bundle exec sidekiq -C config/sidekiq.yml"
  artifacts:
    expire_in: 3 mos
    paths:
      - output1

.template_aws:
  stage: deploy
  needs:
    - build_amd64
  before_script:
    - docker login --username AWS -p $(aws ecr get-login-password) ${AWS_ECR}
  script:
    - chown -R gitlab-runner:gitlab-runner /home/<USER>
    - !-1 2>output1
    - source variables
    - docker pull ${AWS_ECR}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}@${IMAGE_HASH}
    - docker rm -f spree-backend-web spree-backend-worker
    - |
      docker run -d --name ${CI_PROJECT_NAME}-web \
        --restart unless-stopped \
        --memory $MEMORY_LIMIT --memory-swap $MEMORY_SWAP_LIMIT \
        --net=host \
        -p 4000:4000 \
        -v /opt/spree/storage:/app/storage \
        --env SENTRY_DSN=https://<EMAIL>/4506868748713984 \
        --env RAILS_ENV="${RAILS_ENV}" --env REDIS_URL="redis://127.0.0.1:6379/0" \
        --env DATABASE_URL="postgres://postgres:postgres@127.0.0.1/spree_starter" \
        --env DISABLE_SPRING="1" \
        --env SECRET_KEY_BASE="22d9b7aa31a1327bcc4b21e4638a25b0b13b3947a59f0009bda8f754bc95de606abe3f80242f1fb864984a71e1fc94c18a422b1094a223cd1defe3d4147bc995" \
        --env SIDEKIQ_USERNAME=admin \
        --env SIDEKIQ_PASSWORD=password \
        --env ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST} \
        --env DD_AGENT_HOST="localhost" \
        --env DD_TRACE_AGENT_PORT="8126" \
        --env DD_VERSION="${CI_COMMIT_TAG}" \
        --env DD_SERVICE="spree-backend" \
        --env AXEL_STRIPE_API_KEY="${AXEL_STRIPE_API_KEY}" \
        --env AXEL_STRIPE_WEBHOOK_SECRET="${AXEL_STRIPE_WEBHOOK_SECRET}" \
        ${AWS_ECR}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}@${IMAGE_HASH}
    - |
      docker run -d --name ${CI_PROJECT_NAME}-worker \
        --restart unless-stopped \
        --memory $MEMORY_LIMIT --memory-swap $MEMORY_SWAP_LIMIT \
        --net=host \
        -v /opt/spree/storage:/app/storage \
        --env SENTRY_DSN=https://<EMAIL>/4506868748713984 \
        --env RAILS_ENV="${RAILS_ENV}" \
        --env REDIS_URL="redis://127.0.0.1:6379/0" \
        --env DATABASE_URL="postgres://postgres:postgres@127.0.0.1/spree_starter" \
        --env DISABLE_SPRING="1" \
        --env SECRET_KEY_BASE="22d9b7aa31a1327bcc4b21e4638a25b0b13b3947a59f0009bda8f754bc95de606abe3f80242f1fb864984a71e1fc94c18a422b1094a223cd1defe3d4147bc995" \
        --env ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST} \
        --env DD_AGENT_HOST="localhost" \
        --env DD_TRACE_AGENT_PORT="8126" \
        --env DD_VERSION="${CI_COMMIT_TAG}" \
        --env DD_SERVICE="spree-backend-worker" \
        ${AWS_ECR}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}@${IMAGE_HASH} bash -c "bundle exec sidekiq -C config/sidekiq.yml"
  artifacts:
    expire_in: 3 mos
    paths:
      - output1


.dev_deploy:
  extends: .template
  when: manual

.dev_deploy_aws:
  extends: .template_aws
  when: manual

.prod_deploy:
  extends: .template_aws
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*$/
      when: manual
    - when: never


market1.axel.market:
  extends: .prod_deploy
  variables:
    RAILS_ENV: 'production'
    SERVER_URL: https://axel.market
    ELASTICSEARCH_HOST: ${ELASTICSEARCH_HOST_MARKET1}
    AXEL_STRIPE_API_KEY: ${AXEL_STRIPE_API_KEY_MARKET1}
    AXEL_STRIPE_WEBHOOK_SECRET: ${AXEL_STRIPE_WEBHOOK_SECRET_MARKET1}
    MEMORY_LIMIT: 14g
    MEMORY_SWAP_LIMIT: 16g
  tags:
    - market1r2

cipher.axel.network:
  extends: .dev_deploy_aws
  variables:
    RAILS_ENV: 'staging'
    SERVER_URL: https://cipher.axel.network
    ELASTICSEARCH_HOST: ${ELASTICSEARCH_HOST_CIPHER}
    AXEL_STRIPE_API_KEY: ${AXEL_STRIPE_API_KEY_CIPHER}
    AXEL_STRIPE_WEBHOOK_SECRET: ${AXEL_STRIPE_WEBHOOK_SECRET_CIPHER}
    MEMORY_LIMIT: 6g
    MEMORY_SWAP_LIMIT: 10g    
  tags:
    - cipher_deploy2

cipher2.zencoo.com:
  extends: .dev_deploy
  variables:
    RAILS_ENV: 'staging'
    SERVER_URL: https://cipher2.zencoo.com
    ELASTICSEARCH_HOST: ${ELASTICSEARCH_HOST_CIPHER2}
    AXEL_STRIPE_API_KEY: ${AXEL_STRIPE_API_KEY_CIPHER2}
    AXEL_STRIPE_WEBHOOK_SECRET: ${AXEL_STRIPE_WEBHOOK_SECRET_CIPHER2}
    MEMORY_LIMIT: 3g
    MEMORY_SWAP_LIMIT: 10g
  tags:
    - cipher2_deploy2

dev-market-2.axel.network:
  extends: .dev_deploy
  variables:
    RAILS_ENV: 'staging'
    SERVER_URL: https://dev-market-2.axel.network
    ELASTICSEARCH_HOST: ${ELASTICSEARCH_HOST_DEV2}
    AXEL_STRIPE_API_KEY: ${AXEL_STRIPE_API_KEY_DEV2}
    AXEL_STRIPE_WEBHOOK_SECRET: ${AXEL_STRIPE_WEBHOOK_SECRET_DEV2}
    MEMORY_LIMIT: 3g
    MEMORY_SWAP_LIMIT: 10g    
  tags:
    - dev_market_2_deploy2

dev-market-3.axel.network:
  extends: .dev_deploy
  variables:
    RAILS_ENV: 'staging'
    SERVER_URL: https://dev-market-3.axel.network
    ELASTICSEARCH_HOST: ${ELASTICSEARCH_HOST_DEV3}
    AXEL_STRIPE_API_KEY: ${AXEL_STRIPE_API_KEY_DEV3}
    AXEL_STRIPE_WEBHOOK_SECRET: ${AXEL_STRIPE_WEBHOOK_SECRET_DEV3}
    MEMORY_LIMIT: 3g
    MEMORY_SWAP_LIMIT: 10g        
  tags:
    - dev_market_3_deploy2

