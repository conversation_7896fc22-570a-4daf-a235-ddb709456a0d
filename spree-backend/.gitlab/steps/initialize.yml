initialize:
  stage: initialize
  before_script:
    - docker login -u "$CI_REGISTRY_PUSH_USER" -p "$CI_REGISTRY_PUSH_PASSWORD" $CI_REGISTRY_PUSH
    - docker login -u "$CI_REGISTRY_PULL_USER" -p "$CI_REGISTRY_PULL_PASSWORD" $CI_REGISTRY_PULL
  script:
    - docker buildx create --use --name build || true
    - |
      docker buildx build -t $CI_IMAGE_PUSH \
        --output=type=docker \
        --cache-to type=registry,ref=${CI_IMAGE_PUSH}-cache,mode=max \
        --cache-from type=registry,ref=${CI_IMAGE_PULL}-cache \
        -f .gitlab/Dockerfile .
    - export CI_IMAGE_HASH=`docker push $CI_IMAGE_PUSH | tail -1 | grep digest | cut -d ' ' -f 3`
    - |
      if [ ! -z "${CI_IMAGE_HASH}" ]; then
        echo "export CI_IMAGE_HASH="${CI_IMAGE_HASH};
      else
        echo "Image build failed" && exit 100;
      fi;
    - echo "CI_IMAGE_HASH=${CI_IMAGE_HASH}" > build.env
  artifacts:
    reports:
      dotenv: build.env
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*$/
      when: never
    - when: on_success