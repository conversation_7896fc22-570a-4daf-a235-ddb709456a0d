.build:
  stage: build
  image:
    name: ${CI_REGISTRY_PULL}/aws-cli-docker-cli:renew
    entrypoint: [""]
  services:
    - name: docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
  before_script:
    - until docker info; do sleep 1; done
    - docker login -u "$CI_REGISTRY_PUSH_USER" -p "$CI_REGISTRY_PUSH_PASSWORD" $CI_REGISTRY_PUSH
    - docker login -u "$CI_REGISTRY_PULL_USER" -p "$CI_REGISTRY_PULL_PASSWORD" $CI_REGISTRY_PULL
  artifacts:
    expire_in: 3 mos
    paths:
      - variables

build_amd64:
  extends:
    - .build
  script:
    - docker buildx create --use --name build || true
    - |
      docker buildx build -t ${IMAGE_PUSH} \
        --output=type=docker \
        --cache-to type=registry,ref=${CACHE_IMAGE_PUSH},mode=max \
        --cache-from type=registry,ref=${CACHE_IMAGE_PULL} \
        --build-arg COMMIT_SHA="${CI_COMMIT_SHA}" \
        --build-arg COMMIT_REF_NAME="${CI_COMMIT_REF_NAME}" \
        -f Dockerfile .
    - export IMAGE_HASH=`docker push ${IMAGE_PUSH} | tail -1 | grep digest | cut -d ' ' -f 3`
    - |
      if [ ! -z "${IMAGE_HASH}" ]; then
        echo "export IMAGE_HASH="${IMAGE_HASH};
      else
        echo "Image build failed" && exit 100;
      fi;
    - echo "export IMAGE_HASH="${IMAGE_HASH} > variables
    ## push AWS ECR
    - export IMAGE_AWS_ECR=${AWS_ECR}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}
    - echo "Push to AWS ECR the image ${IMAGE_AWS_ECR}"
    - docker login --username AWS -p $(aws ecr get-login-password) ${AWS_ECR}
    - docker tag $IMAGE_PUSH ${IMAGE_AWS_ECR}
    - docker push ${IMAGE_AWS_ECR}


build_arm64:
  extends:
    - .build
  image:
    name: ${CI_REGISTRY_PULL}/aws-cli-docker-cli:multi
  tags:
    - arm64
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  script:
    - docker context create tls-environment
    - docker buildx create --use tls-environment --name build || true
    - export IMAGE_PUSH="${IMAGE_PUSH}_arm64"
    - |
      docker buildx build -t ${IMAGE_PUSH} \
        --platform linux/arm64 \
        --cache-to type=registry,ref=${CACHE_IMAGE_PUSH}_arm64,mode=max \
        --cache-from type=registry,ref=${CACHE_IMAGE_PULL}_arm64 \
        --push -f Dockerfile .
    - export IMAGE_HASH=$(docker buildx imagetools inspect $IMAGE_PUSH | grep Digest | awk '{print $2}')
    - |
      if [ ! -z "${IMAGE_HASH}" ]; then
        echo "export IMAGE_HASH="${IMAGE_HASH};
      else
        echo "Image build failed" && exit 100;
      fi;
    - echo "export IMAGE_HASH="${IMAGE_HASH} > variables
    ## push AWS ECR
    - export IMAGE_AWS_ECR=${AWS_ECR}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}_arm64
    - echo "Push to AWS ECR the image ${IMAGE_AWS_ECR}"
    - docker login --username AWS -p $(aws ecr get-login-password) ${AWS_ECR}
    - docker pull --platform linux/arm64 $IMAGE_PUSH
    - docker tag $IMAGE_PUSH ${IMAGE_AWS_ECR}
    - docker push ${IMAGE_AWS_ECR}
  cache:
    paths:
      - .yarn/
  when: manual