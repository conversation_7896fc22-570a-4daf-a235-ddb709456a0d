# -*- encoding: utf-8 -*-
# stub: spree_volume_pricing 3.3.1 ruby lib

Gem::Specification.new do |s|
  s.name = "spree_volume_pricing".freeze
  s.version = "3.3.1"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze]
  s.date = "2024-04-24"
  s.description = "Allow prices to be configured in quantity ranges for each variant".freeze
  s.email = "<EMAIL>".freeze
  s.files = [".gitignore".freeze, ".hound.yml".freeze, ".rspec".freeze, ".rubocop.yml".freeze, ".travis.yml".freeze, "Appraisals".freeze, "CONTRIBUTING.md".freeze, "Gemfile".freeze, "Guardfile".freeze, "LICENSE.md".freeze, "README.md".freeze, "Rakefile".freeze, "app/assets/javascripts/spree/backend/spree_volume_pricing.js".freeze, "app/assets/javascripts/spree/frontend/spree_volume_pricing.js".freeze, "app/assets/stylesheets/spree/backend/spree_volume_pricing.css".freeze, "app/assets/stylesheets/spree/frontend/spree_volume_pricing.css".freeze, "app/controllers/spree/admin/variants_controller_decorator.rb".freeze, "app/controllers/spree/admin/volume_price_models_controller.rb".freeze, "app/controllers/spree/admin/volume_prices_controller.rb".freeze, "app/controllers/spree/base_controller_decorator.rb".freeze, "app/helpers/spree_volume_pricing/base_helper.rb".freeze, "app/models/spree/line_item_decorator.rb".freeze, "app/models/spree/user_decorator.rb".freeze, "app/models/spree/variant_decorator.rb".freeze, "app/models/spree/volume_price.rb".freeze, "app/models/spree/volume_price_model.rb".freeze, "app/overrides/views_decorator.rb".freeze, "app/services/spree/cart/add_item_decorator.rb".freeze, "app/views/spree/admin/shared/_vp_product_tab.html.erb".freeze, "app/views/spree/admin/variants/_edit_fields.html.erb".freeze, "app/views/spree/admin/variants/volume_prices.html.erb".freeze, "app/views/spree/admin/volume_price_models/_form.html.erb".freeze, "app/views/spree/admin/volume_price_models/_list.html.erb".freeze, "app/views/spree/admin/volume_price_models/edit.html.erb".freeze, "app/views/spree/admin/volume_price_models/index.html.erb".freeze, "app/views/spree/admin/volume_price_models/new.html.erb".freeze, "app/views/spree/admin/volume_prices/_edit_fields.html.erb".freeze, "app/views/spree/admin/volume_prices/_volume_price_fields.html.erb".freeze, "app/views/spree/products/_volume_pricing.html.erb".freeze, "bin/rails".freeze, "config/locales/de.yml".freeze, "config/locales/en.yml".freeze, "config/locales/pt.yml".freeze, "config/locales/ru.yml".freeze, "config/locales/sv.yml".freeze, "config/locales/tr.yml".freeze, "config/routes.rb".freeze, "db/migrate/20081119145604_create_volume_prices.rb".freeze, "db/migrate/20110203174010_change_display_name_for_volume_prices.rb".freeze, "db/migrate/20111206173307_prefix_volume_pricing_table_names.rb".freeze, "db/migrate/20121115043422_add_discount_type_column.rb".freeze, "db/migrate/20150513200904_add_role_to_volume_price.rb".freeze, "db/migrate/20150603143015_create_spree_volume_price_models.rb".freeze, "db/migrate/20210729055206_change_id_columns_types_for_spree_volume_prices.rb".freeze, "gemfiles/spree_3_7.gemfile".freeze, "gemfiles/spree_4_0.gemfile".freeze, "gemfiles/spree_4_1.gemfile".freeze, "gemfiles/spree_master.gemfile".freeze, "lib/generators/spree_volume_pricing/install/install_generator.rb".freeze, "lib/spree_volume_pricing.rb".freeze, "lib/spree_volume_pricing/engine.rb".freeze, "lib/spree_volume_pricing/version.rb".freeze, "spec/controllers/spree/admin/variants_controller_spec.rb".freeze, "spec/factories/volume_price_factory.rb".freeze, "spec/helpers/base_helper_spec.rb".freeze, "spec/models/spree/line_item_spec.rb".freeze, "spec/models/spree/order_spec.rb".freeze, "spec/models/spree/variant_spec.rb".freeze, "spec/models/spree/volume_price_spec.rb".freeze, "spec/spec_helper.rb".freeze, "spec/support/capybara.rb".freeze, "spec/support/database_cleaner.rb".freeze, "spec/support/factory_girl.rb".freeze, "spec/support/spree.rb".freeze, "spree_volume_pricing.gemspec".freeze]
  s.homepage = "https://github.com/spree-contrib/spree_volume_pricing".freeze
  s.licenses = ["BSD-3".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.2.3".freeze)
  s.requirements = ["none".freeze]
  s.rubygems_version = "3.3.26".freeze
  s.summary = "Allow prices to be configured in quantity ranges for each variant".freeze
  s.test_files = ["spec/controllers/spree/admin/variants_controller_spec.rb".freeze, "spec/factories/volume_price_factory.rb".freeze, "spec/helpers/base_helper_spec.rb".freeze, "spec/models/spree/line_item_spec.rb".freeze, "spec/models/spree/order_spec.rb".freeze, "spec/models/spree/variant_spec.rb".freeze, "spec/models/spree/volume_price_spec.rb".freeze, "spec/spec_helper.rb".freeze, "spec/support/capybara.rb".freeze, "spec/support/database_cleaner.rb".freeze, "spec/support/factory_girl.rb".freeze, "spec/support/spree.rb".freeze]

  s.installed_by_version = "3.3.26" if s.respond_to? :installed_by_version

  if s.respond_to? :specification_version then
    s.specification_version = 4
  end

  if s.respond_to? :add_runtime_dependency then
    s.add_runtime_dependency(%q<spree_core>.freeze, [">= 3.1.0", "< 5.0"])
    s.add_runtime_dependency(%q<spree_extension>.freeze, [">= 0"])
    s.add_runtime_dependency(%q<deface>.freeze, ["~> 1.0"])
    s.add_development_dependency(%q<shoulda-matchers>.freeze, [">= 0"])
    s.add_development_dependency(%q<spree_dev_tools>.freeze, [">= 0"])
  else
    s.add_dependency(%q<spree_core>.freeze, [">= 3.1.0", "< 5.0"])
    s.add_dependency(%q<spree_extension>.freeze, [">= 0"])
    s.add_dependency(%q<deface>.freeze, ["~> 1.0"])
    s.add_dependency(%q<shoulda-matchers>.freeze, [">= 0"])
    s.add_dependency(%q<spree_dev_tools>.freeze, [">= 0"])
  end
end
