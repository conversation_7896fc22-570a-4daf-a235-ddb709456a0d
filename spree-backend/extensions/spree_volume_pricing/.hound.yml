---
# Too picky.
Metrics/LineLength:
  Enabled: false

Metrics/AbcSize:
  Enabled: false

# This should truly be on for well documented gems.
Style/Documentation:
  Enabled: false

# Neatly aligned code is too swell.
Style/SingleSpaceBeforeFirstArg:
  Enabled: false

# Don't mess with RSpec DSL.
Style/BlockDelimiters:
  Exclude:
    - 'spec/**/*'

# We really like the readability with newline in beginning of classes.
Style/EmptyLinesAroundBlockBody:
  Enabled: false

Style/EmptyLinesAroundClassBody:
  Enabled: false

# It say we should use fail over raise.
Style/SignalException:
  Enabled: false

# Use nested module/class definitions instead of compact style.
# Too high git impact to change this now.
Style/ClassAndModuleChildren:
  Enabled: false

# Avoid contradictory style rules by enforce single quotes.
Style/StringLiterals:
  EnforcedStyle: single_quotes
