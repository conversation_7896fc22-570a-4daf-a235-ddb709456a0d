os: linux
dist: bionic

addons:
  apt:
    sources:
      - google-chrome
    packages:
      - google-chrome-stable

services:
  - mysql
  - postgresql

language: ruby

rvm:
  - 2.5
  - 2.6

env:
  - DB=mysql
  - DB=postgres

gemfile:
  - gemfiles/spree_3_7.gemfile
  - gemfiles/spree_4_0.gemfile
  - gemfiles/spree_4_1.gemfile
  - gemfiles/spree_master.gemfile

jobs:
  allow_failures:
    - gemfile: gemfiles/spree_master.gemfile

before_install:
  - mysql -u root -e "GRANT ALL ON *.* TO 'travis'@'%';"

before_script:
  - CHROME_MAIN_VERSION=`google-chrome-stable --version | sed -E 's/(^Google Chrome |\.[0-9]+ )//g'`
  - CHROMEDRIVER_VERSION=`curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_MAIN_VERSION"`
  - curl "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip" -O
  - unzip chromedriver_linux64.zip -d ~/bin
  - nvm install 14

script:
  - bundle exec rake test_app
  - bundle exec rake spec
