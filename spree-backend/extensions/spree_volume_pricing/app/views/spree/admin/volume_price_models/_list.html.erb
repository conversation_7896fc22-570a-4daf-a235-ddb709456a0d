<table class="table sortable" id='listing_volume_price_models' data-hook data-sortable-link="<%#= update_positions_admin_volume_price_models_url %>">
  <thead>
    <tr data-hook="volume_price_models_header">
      <th class="no-border"></th>
      <th><%= Spree.t(:name) %></th>
      <th class="actions"></th>
    </tr>
  </thead>
  <tbody>
    <% @volume_price_models.each do |price_model| %>
      <tr id="<%= spree_dom_id price_model %>" data-hook="volume_price_models_row">
        <td class="move-handle">
          <span class="icon icon-sort handle"></span>
        </td>
        <td><%= price_model.name %></td>
        <td class="actions actions-2 text-right">
          <%= link_to_edit price_model.id, no_text: true %>
          <%= link_to_delete price_model, no_text: true %>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>
