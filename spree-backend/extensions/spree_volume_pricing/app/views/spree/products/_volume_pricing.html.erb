<% if @product.price > 0 and @product.master.volume_prices.present? %>
  <div id="bulk-discount">
    <h6><%= Spree.t(:bulk_discount) %></h6>
    <table class="table">
      <% @product.master.volume_prices.each do |v| %>
        <%= content_tag(:tr) do %>
          <%= content_tag :td, v.name %>
          <% case v.discount_type %>
            <% when 'percent' %>
              <%= content_tag(:td,Spree.t(:save) + ' ' + '%i%%' % ((v.amount * @product.master.price)).round) %>
            <% when 'price' %>
              <%= content_tag :td, Spree::Money.new(v.amount).to_s + ' ' + Spree.t(:each) %>
            <% when 'dollar' %>
            <%= content_tag :td, Spree.t(:save) + ' ' + Spree::Money.new(v.amount).to_s %>
          <% end %>
        <% end %>
      <% end %>
    </table>
  </div>
<% end %>
