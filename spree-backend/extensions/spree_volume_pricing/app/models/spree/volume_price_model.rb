class Spree::VolumePriceModel < ActiveRecord::Base
  has_many :variants
  has_many :volume_prices, -> { where(variant_id: nil, listing_id: nil).order(position: :asc) }, dependent: :destroy
  after_save :update_volume_price_model_for_listings

  accepts_nested_attributes_for :volume_prices, allow_destroy: true,
    reject_if: proc { |volume_price|
      volume_price[:amount].blank? && volume_price[:range].blank?
    }

  def update_volume_price_model_for_listings
    model_id = self&.id
    if model_id > 0
      # found all listing id and variant id and model id is not null
      all_volume_Prices = ::Spree::VolumePrice.where(volume_price_model_id: model_id).where.not(listing_id: nil, variant_id: nil)
      all_volume_Prices_arr = []
      all_volume_Prices&.each do | vp_o |
        all_volume_Prices_arr.push({
          id: vp_o&.id,
          listing_id: vp_o&.listing_id,
          variant_id: vp_o&.variant_id,
          volume_price_model_id: vp_o&.volume_price_model_id,
        })
      end
      model = ::Spree::VolumePriceModel.find(model_id)
      # delete previous volume_price_model_id
      ::Spree::VolumePrice.where(volume_price_model_id: model_id).where.not(listing_id: nil, variant_id: nil)&.delete_all
      model&.volume_prices&.each do |vp|
        all_volume_Prices_arr&.each do |vp_oa |
          new_name = vp&.name #temporarily using old name
          record = ::Spree::VolumePrice.find_or_initialize_by(listing_id: vp_oa[:listing_id] ,variant_id: vp_oa[:variant_id], volume_price_model_id: model_id, name: new_name)
          record.update(
            name: new_name,
            range: vp&.range,
            amount: vp&.amount,
            position: vp&.position,
            discount_type: vp&.discount_type,
            role_id: vp&.role_id
          )
           # record.save
        end
      end
    end
  end
end
