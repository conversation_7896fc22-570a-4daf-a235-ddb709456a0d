module Spree
  module Admin
    class StripeTaxSettingsController < Spree::Admin::BaseController
      def edit
        @settings = StripeTaxSettings.for_store(current_store).first_or_initialize
      end

      def update
        begin
          settings = StripeTaxSettings.where(id: params['stripe_tax_settings']['id'], store: current_store).first_or_initialize
          settings.update!(stripe_tax_settings_params)
          flash[:success] = ::Spree.t(:stripe_tax_setting_updated)
          redirect_to edit_admin_stripe_tax_settings_path
        rescue => e
          flash[:error] = 'stripe tax setting updated failed.'
          render :edit, status: :unprocessable_entity
        end
      end

      def sync
        ::SpreeStripeTax::TaxCategoriesService.sync(current_store)
        flash[:success] = 'Tax categories synced from stripe tax codes.'
        redirect_to edit_admin_stripe_tax_settings_path
      end

      private

      def stripe_tax_settings_params
        params.require(:stripe_tax_settings).permit(:stripe_tax_enabled, :stripe_client_secret)
      end
    end
  end
end
