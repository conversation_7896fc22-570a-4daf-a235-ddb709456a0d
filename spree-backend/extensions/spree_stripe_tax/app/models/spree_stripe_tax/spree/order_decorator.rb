module SpreeStripeTax
  module Spree
    module OrderDecorator
      def self.prepended(base)
        base.include Taxable

        base.state_machine.after_transition to: :complete, do: :capture_stripe_tax
        base.state_machine.after_transition to: :canceled, do: :delete_stripe_tax_transaction
        #base.state_machine.after_transition to: :resumed, from: :canceled, do: :capture_stripe_tax
        base.has_one :stripe_tax_calculation
        base.has_many :stripe_tax_transactions, class_name: '::Spree::StripeTaxTransactions'
      end

      private

      def delete_stripe_tax_transaction
        return unless stripe_tax_applicable?(self)
        SpreeStripeTax::CalculationsService.delete_transaction(self)
      end

      def capture_stripe_tax
        #puts "this line was reached by #{caller.join("\n")}"
        return unless stripe_tax_applicable?(self)
        SpreeStripeTax::CalculationsService.create_transaction(self)
      end
    end
  end
end

Spree::Order.prepend(SpreeStripeTax::Spree::OrderDecorator)
