module SpreeStripeTax
  module Spree
    module ReimbursementDecora<PERSON>
      def self.prepended(base)
        base.include Taxable

        state_machine = base.state_machines[:reimbursement_status]
        state_machine.after_transition to: [:reimbursed], do: :remove_tax_for_returned_items
      end

      def remove_tax_for_returned_items
        return unless stripe_tax_applicable?(order)
        SpreeStripeTax::CalculationsService.create_refund_transaction(order, self)
      end
    end
  end
end

Spree::Reimbursement.prepend(SpreeStripeTax::Spree::ReimbursementDecorator)
