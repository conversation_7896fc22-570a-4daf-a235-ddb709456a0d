require_dependency 'spree/calculator'

module Spree
  class Calculator::StripeTaxCalculator < Calculator
    include VatPriceCalculation

    CACHE_EXPIRATION_DURATION = 10.minutes

    def self.description
      Spree.t(:stripe_tax_calculator_description)
    end

    def compute_order(order)
      raise "Calculate tax for line_item and shipment and not order"
    end

    def compute_line_item(item)
      #SpreeTaxjar::Logger.log(__method__, {line_item: {order: {id: item.order.id, number: item.order.number}}}) if SpreeTaxjar::Logger.logger_enabled?
      return 0 unless item.order.store.stripe_tax_setting.enabled
      if rate.included_in_price
        0
      else
        round_to_two_places(tax_for_item(item))
      end
    end

    def compute_shipment(shipment)
      #SpreeTaxjar::Logger.log(__method__, {shipment: {order: {id: shipment.order.id, number: shipment.order.number}}}) if SpreeTaxjar::Logger.logger_enabled?
      return 0 unless shipment.order.store.stripe_tax_setting.enabled
      tax_for_shipment(shipment)
    end

    def compute_shipping_rate(shipping_rate)
      return 0
      #return 0 unless Spree::Config[:taxjar_enabled]
      #if rate.included_in_price
      #  raise Spree.t(:shipping_rate_exception_message)
      #else
      #  0
      #end
    end

    private
      def rate
        calculable
      end

      def tax_for_shipment(shipment)
        order = shipment.order
        return 0 unless order.shipment_total > 0
        return 0 unless tax_address = order.tax_address

        #p 'stripe tax for shipment', shipment, caller.join('\n')
        rails_cache_key = cache_key(order, shipment, tax_address)

        #SpreeTaxjar::Logger.log(__method__, {shipment: {order: {id: shipment.order.id, number: shipment.order.number}}, cache_key: rails_cache_key}) if SpreeTaxjar::Logger.logger_enabled?

        Rails.cache.fetch(rails_cache_key, expires_in: CACHE_EXPIRATION_DURATION) do
          calculation = SpreeStripeTax::CalculationsService.calculate_tax(order)
          return 0 unless calculation
          cache_response(calculation, order, tax_address, nil, shipment)
        end
      rescue Exception
        Rails.logger.error "tax_for_shipment error #{$!.backtrace.join("\n\t")} \n#{$!.message}"
      end

      def tax_for_item(item)
        order = item.order
        return 0 unless tax_address = order.tax_address

        rails_cache_key = cache_key(order, item, tax_address)
        #p 'stripe tax for item', item, 'cache key', rails_cache_key, caller.join('\n')

        #SpreeTaxjar::Logger.log(__method__, {line_item: {order: {id: item.order.id, number: item.order.number}}, cache_key: rails_cache_key}) if SpreeTaxjar::Logger.logger_enabled?

        ## Test when caching enabled that only 1 API call is sent for an order
        ## should avoid N calls for N line_items
        Rails.cache.fetch(rails_cache_key, expires_in: CACHE_EXPIRATION_DURATION) do
          calculation = SpreeStripeTax::CalculationsService.calculate_tax(order)
          return 0 unless calculation
          tax_for_current_item = cache_response(calculation, order, tax_address, item)
          tax_for_current_item
        end
      end

      def cache_response(calculation, order, address, item = nil, shipment_item = nil)
        #SpreeTaxjar::Logger.log(__method__, {order: {id: order.id, number: order.number}, taxjar_api_advanced_res: taxjar_response}) if SpreeTaxjar::Logger.logger_enabled?
        #SpreeTaxjar::Logger.log(__method__, {order: {id: order.id, number: order.number}, taxjar_api_advanced_res: taxjar_response.breakdown.line_items}) if SpreeTaxjar::Logger.logger_enabled?
        ## res is set to faciliate testing as to return computed result from API
        ## for given line_item
        ## better to use Rails.cache.fetch for order and wrapping lookup based on line_item id
        breakdown_line_items = SpreeStripeTax::CalculationsService.line_items_for_calculation(calculation[:id], order)
        return nil unless breakdown_line_items
        res = nil
        breakdown_line_items.each do |line_item|
          item_from_db = Spree::LineItem.includes(:adjustments).find_by(id: line_item[:reference])
          amount_tax = line_item[:amount_tax] / 100.0.to_d
          if item && item_from_db.id == item.id
            res = amount_tax
          end
          Rails.cache.write(cache_key(order, item_from_db, address), amount_tax, expires_in: CACHE_EXPIRATION_DURATION)
        end
        if order.shipments.any? && calculation[:shipping_cost]
          tax_amount = calculation[:shipping_cost][:amount_tax] / 100.0.to_d
          order.shipments.each do |shipment|
            if shipment_item && shipment_item.id == shipment.id
              res = tax_amount
            end
            Rails.cache.write(cache_key(order, shipment, address), tax_amount, expires_in: CACHE_EXPIRATION_DURATION)
          end
        end
        res
      end

      def cache_key(order, item, address)
        if item.is_a?(Spree::LineItem)
          [Spree::LineItem.to_s, order.id, item.id, address.state_id, address.zipcode, item.taxable_amount, :amount_to_collect]
        else
          [Spree::Shipment.to_s, order.id, item.id, address.state_id, address.zipcode, item.cost, item.adjustments.select { |adjustment| adjustment.source_type != Spree::TaxRate.to_s }.map(&:amount).sum.to_f, :amount_to_collect]
        end
      end
  end
end
