<%= render 'spree/admin/shared/sub_menu/configuration' %>
<% content_for :page_title do %>
  <%= Spree.t(:stripe_tax_settings) %>
<% end %>

<% content_for :page_actions do %>
  <%= form_tag admin_stripe_tax_settings_sync_path, method: :post do %>
    <%= button Spree.t('sync_stripe_tax_categories'), 'resume.svg', 'submit', {class: 'btn-success', data: { disable_with: "#{ Spree.t('syncing') }...", action: "sync" } } %>
  <% end %>
<% end if can?(:create, Spree::TaxCategory) && @settings.stripe_tax_enabled && @settings.stripe_client_secret %>

<%= form_with model: @settings, url: admin_stripe_tax_settings_path, method: :put do |form| %>
  <div id="stripe-tax-settings" data-hook>
    <div class="row">
      <div class="col-md-6">
        <fieldset class="no-border-bottom">
          <%= form.hidden_field :id, value: @settings.id %>
          <div class="form-group" id="stripe-tax-toggle">
            <%= form.check_box :stripe_tax_enabled %>
            <%= form.label :stripe_tax_enabled, Spree.t(:stripe_tax_enabled), class:'mb-2' %>
          </div>
          <div class="form-group" id="stripe-client-secret-item">
            <%= form.label :stripe_client_secret, Spree.t(:stripe_client_secret) %>
            <%= form.text_field :stripe_client_secret, class: 'form-control', disabled: !@settings.stripe_tax_enabled %>
          </div>
        </fieldset>
      </div>
    </div>
    <%= render partial: 'spree/admin/shared/edit_resource_links', locals: {collection_url: admin_orders_url} %>
  </div>
  <script>
    $('#stripe-tax-toggle input[type=checkbox]').change(function () {
      const toggleEnabled = $(this).is(':checked')
      $('#stripe-client-secret-item input').prop('disabled', !toggleEnabled)
    })
  </script>
<% end %>
