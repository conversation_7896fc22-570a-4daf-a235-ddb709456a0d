module SpreeStripeTax
  class TaxCategoriesService
    def self.sync(store)
      require 'stripe'
      Stripe.api_key = store.stripe_tax_setting.stripe_client_secret
      has_more = true
      tax_categories = []
      count = 0
      start_after = nil
      while has_more do
        tax_code_res = Stripe::TaxCode.list({limit: 50, starting_after: start_after})
        #p 'tax code res', tax_code_res
        has_more = tax_code_res[:has_more]
        tax_categories += tax_code_res[:data]
        start_after = tax_code_res[:data].last[:id]
      end

      update(tax_categories, store)
    rescue Exception
      Rails.logger.info "Exception update stripe tax codes into tax categories: \n#{$!.message}\n\t#{$!.backtrace.join("\n\t")}"
    end

    def self.update(tax_categories = [], store)
      tax_categories.each do |tax_category|
        tax_category_attr = transform(tax_category)
        tax_category_in_db = ::Spree::TaxCategory.where(name: tax_category_attr[:name], store: store).first
        if tax_category_in_db.present?
          #p "TaxCategory:: Update:: #{tax_category_in_db.inspect}"
          tax_category_in_db.update!(tax_category_attr)
        else
          tax_category_attr[:store] = store
          new_tax_category = ::Spree::TaxCategory.create!(tax_category_attr)
          #p "TaxCategory:: Create:: #{new_tax_category.inspect}"
        end
      end
    end

    private
      def self.transform(tax_category)
        {name: tax_category[:name], tax_code: tax_category[:id], description: tax_category[:description]}
      end
  end
end
