module SpreeStripeTax
  class CalculationsService
    def self.calculate_tax(order)
      require 'stripe'
      Stripe.api_key = order.store.stripe_tax_setting&.stripe_client_secret
      Stripe.api_version = '2022-11-15; tax_calc_beta=v3'
      calc_response = Stripe::APIResource.request(
        :post,
        '/v1/tax/calculations',
        tax_params(order)
      )
      order.stripe_tax_calculation&.destroy
      calc_data = calc_response[0].data
      ::Spree::StripeTaxCalculation.create({
        calculation_id: calc_data[:id],
        order: order,
        data: JSON.dump(calc_data),
      })
      calc_data
    rescue Exception
      Rails.logger.info "Exception sending calculations request to stripe: \n#{$!.message}\n\t#{$!.backtrace.join("\n\t")}"
      nil
    end

    def self.line_items_for_calculation(calculation_id, order)
      require 'stripe'
      Stripe.api_key = order.store.stripe_tax_setting&.stripe_client_secret
      Stripe.api_version = "2022-11-15; tax_calc_beta=v3;tax_txns_beta=v2"
      line_items_response = Stripe::APIResource.request(
        :get,
        "/v1/tax/calculations/#{calculation_id}/line_items",
        {limit: order.line_items.length}
      )
      li_data = line_items_response[0].data[:data]
      order.line_items.each do |item|
        item.stripe_tax_line_item&.destroy
        tax_li = li_data.find { |a| a[:reference] == item.id.to_s }
        ::Spree::StripeTaxLineItem.create({
          li_id: tax_li[:id],
          calculation_id: calculation_id,
          line_item: item,
          data: JSON.dump(tax_li),
        })
      end
      li_data
    rescue Exception
      Rails.logger.info "Exception sending line_items request to stripe: \n#{$!.message}\n\t#{$!.backtrace.join("\n\t")}"
      nil
    end

    def self.create_refund_transaction(order, reimbursement)
      origin_transaction = order.stripe_tax_transactions.where(state: 'open', action_type: 'confirm').first
      return unless origin_transaction
      require 'stripe'
      Stripe.api_key = order.store.stripe_tax_setting&.stripe_client_secret
      Stripe.api_version = '2022-11-15; tax_calc_beta=v3;tax_txns_beta=v2'
      refund_transaction_response = Stripe::APIResource.request(
        :post,
        '/v1/tax/transactions/create_reversal',
        {
          mode: "partial",
          original_transaction: origin_transaction.transaction_id,
          reference: reimbursement.id,
          line_items: return_items_params(reimbursement),
        }
      )
      trans_data = refund_transaction_response[0].data
      ::Spree::StripeTaxTransactions.create({
        transaction_id: trans_data[:id],
        order: order,
        action_type: 'refund',
        state: 'closed',
        data: JSON.dump(trans_data),
      })
    rescue Exception
      Rails.logger.info "Exception sending transaction request to stripe: \n#{$!.message}\n\t#{$!.backtrace.join("\n\t")}"
    end

    def self.create_transaction(order)
      return unless order.stripe_tax_transactions.where(action_type: 'confirm').empty?
      require 'stripe'
      Stripe.api_key = order.store.stripe_tax_setting&.stripe_client_secret
      Stripe.api_version = '2022-11-15; tax_calc_beta=v2;tax_txns_beta=v1'
      transaction_response = Stripe::APIResource.request(
        :post,
        '/v1/tax/transactions/create_from_calculation',
        {calculation: order.stripe_tax_calculation&.calculation_id, reference: order.number + '-' + Time.now.to_f.to_s}
      )
      trans_data = transaction_response[0].data
      ::Spree::StripeTaxTransactions.create({
        transaction_id: trans_data[:id],
        order: order,
        action_type: 'confirm',
        state: 'open',
        data: JSON.dump(trans_data),
      })
    rescue Exception
      Rails.logger.info "Exception sending transaction request to stripe: \n#{$!.message}\n\t#{$!.backtrace.join("\n\t")}"
    end

    def self.delete_transaction(order)
      origin_transaction = order.stripe_tax_transactions.where(state: 'open', action_type: 'confirm').first
      return unless origin_transaction
      require 'stripe'
      Stripe.api_key = order.store.stripe_tax_setting&.stripe_client_secret
      Stripe.api_version = '2022-11-15; tax_calc_beta=v3;tax_txns_beta=v2'
      reverse_transaction_response = Stripe::APIResource.request(
        :post,
        '/v1/tax/transactions/create_reversal',
        {
          mode: "full",
          original_transaction: origin_transaction.transaction_id,
          reference: order.number + '-' + Time.now.to_f.to_s,
        }
      )
      trans_data = reverse_transaction_response[0].data
      order.stripe_tax_transactions.update_all(state: 'closed', action_type: 'confirm')
      ::Spree::StripeTaxTransactions.create({
        transaction_id: trans_data[:id],
        order: order,
        action_type: 'cancel',
        state: 'closed',
        data: JSON.dump(trans_data),
      })
    rescue Exception
      Rails.logger.info "Exception sending transaction request to stripe: \n#{$!.message}\n\t#{$!.backtrace.join("\n\t")}"
    end

    private
    def self.tax_params(order)
      {
        currency: order.currency,
        customer_details: {
          address: {
            country: order.tax_address.country&.iso,
            city: order.tax_address&.city,
            line1: order.tax_address&.address1,
            #line2:
            postal_code: order.tax_address&.zipcode,
            state: order.tax_address&.state&.abbr,
          },
          address_source: ::Spree::Config[:tax_using_ship_address] ? 'shipping' : 'billing',
        },
        line_items: taxable_line_items_params(order),
        shipping_cost: taxable_shipping_params(order),
      }
    end

    def self.taxable_line_items_params(order)
      order.line_items.map do |item|
        {
          reference: item.id,
          quantity: item.quantity,
          amount: ::Spree::Money.new(item.taxable_amount.to_f, currency: item.currency).amount_in_cents,
          tax_behavior: 'exclusive',
          tax_code: item.tax_category.try(:tax_code) || 'txcd_99999999',
        }
      end
    end

    def self.taxable_shipping_params(order)
      amount = order.shipment_total + adjustments_total(order.shipment_adjustments)
      return nil unless amount > 0
      return {
        amount: ::Spree::Money.new(amount.to_f, currency: order.currency).amount_in_cents,
        tax_behavior: 'exclusive',
        tax_code: 'txcd_92010001',
      }
    end

    def self.adjustments_total(adjustments)
      adjustments.select { |adjustment| adjustment.source_type != ::Spree::TaxRate.to_s }.map(&:amount).sum.to_f
    end

    def self.return_items_params(reimbursement)
      reimbursement.return_items.map do |item|
        {
          reference: item.id,
          original_line_item: item.inventory_unit.line_item.stripe_tax_line_item.li_id,
          amount: -::Spree::Money.new(item.pre_tax_amount.to_f, currency: item.inventory_unit.line_item.currency).amount_in_cents,
          amount_tax: -::Spree::Money.new(item.additional_tax_total.to_f, currency: item.inventory_unit.line_item.currency).amount_in_cents,
        }
      end
    end
  end
end
