# encoding: UTF-8
lib = File.expand_path('../lib/', __FILE__)
$LOAD_PATH.unshift lib unless $LOAD_PATH.include?(lib)

require 'spree_stripe_tax/version'

Gem::Specification.new do |s|
  s.platform    = Gem::Platform::RUBY
  s.name        = 'spree_stripe_tax'
  s.version     = SpreeStripeTax.version
  s.summary     = 'spree stripe tax calculation integration'
  s.description = 'intergate stripe tax api'
  s.required_ruby_version = '>= 2.5'

  s.author    = 'The Axel Team'
  s.email     = '<EMAIL>'
  s.homepage  = 'https://github.com/spree-backend/spree_stripe_tax'
  s.license = 'BSD-3-Clause'

  s.files       = `git ls-files`.split("\n").reject { |f| f.match(/^spec/) && !f.match(/^spec\/fixtures/) }
  s.require_path = 'lib'
  s.requirements << 'none'

  s.add_dependency 'spree', '>= 4.4.0'
  s.add_dependency 'spree_extension'
  s.add_dependency 'stripe'

  s.add_development_dependency 'spree_dev_tools'
end
