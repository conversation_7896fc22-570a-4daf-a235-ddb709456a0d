import json
import sys
import http.client
import pdb

def main():
  headers_json = sys.argv[1]
  body_json = sys.argv[2]
  headers = json.loads(headers_json)
  body = json.loads(body_json)

  body_str = json.dumps(body)

  conn = http.client.HTTPSConnection("marketplace.walmartapis.com")

  formatted_headers = {key: str(value) for key, value in headers.items()}

  conn.request("POST", "/v3/webhooks/subscriptions", body_str, formatted_headers)

  response = conn.getresponse()

  response_data = response.read()
  conn.close()
  parsed_response = json.loads(response_data)
  print(json.dumps(parsed_response, indent=2))

if __name__ == "__main__":
  main()
