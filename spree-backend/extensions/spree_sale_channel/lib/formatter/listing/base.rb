module Formatter
  module Listing
    class Base
      def initialize(seller_data, sale_channel, product = nil)
        @seller_data = seller_data
        @product = nil
        @variant_exists = false
        @sale_channel = sale_channel
        @sale_channel_brand = @sale_channel.brand.to_s.capitalize
        @product = product

        # Dynamically determine the formatter class
        formatter_class = "Formatter::Listing::#{@sale_channel_brand}Formatter".safe_constantize

        if formatter_class
          @response_formatter = formatter_class.new(@seller_data, @sale_channel, product)
        else
          raise ArgumentError, "Unsupported sale channel: #{@sale_channel_brand}"
        end
      end
    end
  end
end
