module Formatter
  module Listing
    class EbayFormatter
      attr_reader :seller_data, :sale_channel, :item_listings, :store_id, :product

      def initialize(seller_data, sale_channel, product = nil)
        @seller_data = seller_data
        @sale_channel = sale_channel
        @store_id = @sale_channel.store_id
        # @item_listings = seller_data.dig(:GetSellerListResponse, :ItemArray, :Item)
        @product = product.present? ? product : format_product
        @oauth_application = sale_channel.oauth_application
      end

      private

      def format_product
        listing = Spree::Listing.find_by(item_id: seller_data[:ItemID])
        product = listing&.product || Spree::Variant.find_by(sku: format_sku)&.product
        product
      end

      def format_sku
        seller_data[:SKU]
      end

      def format_title
        seller_data[:Title]
      end

      def format_quantity
        seller_data[:Quantity]
      end

      def format_currency
        seller_data[:Currency]
      end

      def format_item_id
        seller_data[:ItemID]
      end

      def format_product_id
        product&.id
      end

      def format_store_id
        store_id
      end

      def format_stock_item_id
        return nil if product.nil?
        variant_id = product.master.id
        stock_items = Spree::StockItem.joins(:stock_location).where(stock_location: { active:true }).where(variant_id: variant_id)
        return stock_items.order(count_on_hand: :desc).first.id if stock_items.any?
      end

      def format_item_url
        seller_data.dig(:ListingDetails, :ViewItemURL)
      end

      def format_sale_channel_id
        sale_channel.id
      end

      def format_description
        seller_data[:Description]
      end
      
      def format_minimum_offer_price
        seller_data.dig(:ListingDetails, :MinimumBestOfferPrice)
      end

      def format_autoaccept_offer_price
        seller_data.dig(:ListingDetails, :BestOfferAutoAcceptPrice)
      end

      def format_allow_offer
        seller_data.dig(:BestOfferDetails, :BestOfferEnabled)
      end

      def format_start_time
        seller_data.dig(:ListingDetails, :StartTime)
      end

      def format_end_time
        seller_data.dig(:ListingDetails, :EndTime)
      end

      def format_category_id
        seller_data.dig(:PrimaryCategory, :CategoryID)
      end

      def format_status
        return "Unlinked" if product.nil?

        seller_data.dig(:SellingStatus, :ListingStatus)
      end

      def format_shipping_id
        seller_data.dig(:SellerProfiles, :SellerShippingProfile, :ShippingProfileID)
      end

      def format_return_id
        seller_data.dig(:SellerProfiles, :SellerReturnProfile, :ReturnProfileID)
      end

      def format_payment_id
        seller_data.dig(:SellerProfiles, :SellerPaymentProfile, :PaymentProfileID)
      end

      def format_category_name
        seller_data[:PrimaryCategory][:CategoryName]
      end

      def format_images
        seller_data[:PictureDetails]
      end

      def format_inventory
        {
          avail_to_sell_qty: ((seller_data[:Quantity].to_i - seller_data.dig(:SellingStatus, :QuantitySold).to_i) || 0),
          price: (seller_data.dig(:SellingStatus, :CurrentPrice) || 0),
          sold_quantity: (seller_data.dig(:SellingStatus, :QuantitySold) || 0)
        }
      end

      def format_variant_stock_items_data
        return nil if product.nil?

        variant_stock_items_data = {}

        if seller_data.dig(:Variations)
          variant_array = seller_data.dig(:Variations).kind_of?(Array) ? seller_data.dig(:Variations) : [seller_data.dig(:Variations)]
          variant_array.each do |variation|
            vs = variation.dig(:Variation).kind_of?(Array) ? variation.dig(:Variation) : [variation.dig(:Variation)]
            vs.each do |v|
              variant = find_variant(v.dig(:VariationSpecifics))
              variant_stock_items_data[variant&.id] = {
                'stock_item_id' => find_variant_stock_item(variant, seller_data),
                'quantity' => v.dig(:Quantity),
                'sold_quantity' => v.dig(:SellingStatus, :QuantitySold),
              }
            end
          end
        else
          variant_stock_items_data[product&.master&.id] = {
            'stock_item_id' => find_variant_stock_item(product.master, seller_data),
            'quantity' => seller_data.dig(:Quantity),
            'sold_quantity' => seller_data.dig(:SellingStatus, :QuantitySold),
          }
        end

        variant_stock_items_data
      end

      def find_variant(variation_specifics)
        return nil if variation_specifics.blank?
        return nil unless product.variants.present?
        ebay_option_value = []

        option_type_value_array = variation_specifics.dig(:NameValueList).kind_of?(Array) ? variation_specifics.dig(:NameValueList) : [variation_specifics.dig(:NameValueList)]
        option_type_value_array.each do |name_value_list|
          ebay_option_value = ebay_option_value + [Spree::OptionValue.find_by(name: name_value_list.dig(:Value))&.id]
        end
        product.variants.each do |variant|
         return variant if variant.option_values && variant.option_values.to_a.pluck(:id) == ebay_option_value
        end
        return nil
      end

      def find_variant_stock_item(variant, seller_data)
        return nil unless variant.present?
        stock_location = Spree::StockLocation.find_by(zipcode: seller_data.dig(:PostalCode))
        stock_item = variant.stock_items.find_by(stock_location_id: stock_location&.id) if variant&.stock_items
        return stock_item&.id
      end

      def format_auction_pricing
        return [] unless seller_data.dig(:ListingType) == "Chinese"

        auction_pricing = {}
        auction_pricing[product&.master&.id] = {
          'quantity' => seller_data.dig(:Quantity),
          'auction_duration' => seller_data.dig(:ListingDuration),
          'pricing_format' => 'Auction',
          'starting_bid' => seller_data.dig(:ListingDetails, :ConvertedStartPrice),
          'buy_it_now' => seller_data.dig(:BuyItNowPrice),
          'reserve_price' => seller_data.dig(:ListingDetails, :ConvertedReservePrice)
        }

        listing_data[:auction_pricing] = auction_pricing
        listing_data
      end

      def format_sale_channel_hash
        nil
      end

      def format_sale_channel_metadata
        return unless product.nil?

        seller_data
      end

      def format_ebay_shipping_data
        { ShippingPackageDetails: seller_data.dig(:ShippingPackageDetails) }
      end

      def format_variations
        return [] if seller_data[:SKU].blank? || seller_data.dig(:Variations, :Variation).nil?

        variations = []
        raw_variation = seller_data.dig(:Variations, :Variation)
        variation_list = raw_variation.is_a?(Array) ? raw_variation : [raw_variation]

        variation_list.each do |variant|
          variation_data = {
            variant: {
              sku: variant[:SKU],
              price: variant[:StartPrice],
              quantity: variant[:Quantity],
              cost_currency: format_currency,
              currency: format_currency,
              lbs: 0.0,
              oz: 0.0,
              track_inventory: true,
              sold_quantity: variant.dig(:SellingStatus, :QuantitySold)
            },
            options: format_option_values(variant.dig(:VariationSpecifics)),
            inventory_data: {
              available_inventory_quantity: (variant[:Quantity].to_i - variant.dig(:SellingStatus, :QuantitySold).to_i),
              price: variant.dig(:StartPrice)
            },
            additional_data: {
              variation_specifics: variant.dig(:VariationSpecifics, :NameValueList)
            }
          }

          variations << variation_data
        end

        variations
      end

      def format_option_values(variation_specific)
        return [] unless variation_specific

        option_type_array = [variation_specific[:NameValueList]]

        option_type_array.map do |option_specific|
          {
            option_type: option_specific[:Name],
            option_value: option_specific[:Value]
          }
        end
      end

      def format_product_status
        "active"
      end

      def format_price
        seller_data.dig(:price, :amount)
      end

      def format_lbs
        0.0
      end

      def format_oz
        0.0
      end

      def format_available_on
        Date.today.beginning_of_day
      end

      def format_condition_list
        return [] unless seller_data[:ConditionID]

        {
          name: seller_data[:ConditionDisplayName],
          condition_id: seller_data[:ConditionID],
          store_id: store_id
        }
      end

    end
  end
end


