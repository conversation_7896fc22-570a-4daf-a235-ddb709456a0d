module Formatter
  module Listing
    class WalmartFormatter
      include ::WalmartHelper

      attr_reader :seller_data, :sale_channel, :store_id, :product, :oauth_application, :item_quantity_hash

      def initialize(seller_data, sale_channel, product = nil)
        @seller_data = seller_data
        @sale_channel = sale_channel
        @store_id = sale_channel.store.id
        @product = product.present? ? product : format_product
        @oauth_application = sale_channel.oauth_application
        @item_quantity_hash = {}
      end

      private

      def format_product
        @listing = get_walmart_listing(sale_channel.id, [format_sku], format_variant_group_id, format_item_id)
        product = Spree::ProductProperty.find_by(value: seller_data[:sku])&.product || @listing&.product || Spree::Variant.find_by(sku: format_sku)&.product
        product
      end

      def format_title
        seller_data[:productName]
      end

      def format_sku
        seller_data[:sku]
      end

      def format_quantity
        inventory_count(seller_data).dig(:inputQty)
      end

      def format_currency
        seller_data.dig(:price, :currency)
      end

      def format_item_id
        seller_data[:wpid]
      end

      def format_product_id
        product&.id
      end

      def format_store_id
        store_id
      end

      def format_stock_item_id
        return nil if product.nil?
        variant_id = product.master.id
        stock_items = Spree::StockItem.joins(:stock_location).where(stock_location: { active: true }).where(variant_id: variant_id)
        return stock_items.order(count_on_hand: :desc).first.id if stock_items.any?
      end

      def format_item_url
        seller_data.dig(:ListingDetails, :ViewItemURL)
      end

      def format_sale_channel_id
        sale_channel.id
      end

      def format_description
        seller_data[:Description]
      end

      def format_minimum_offer_price
        seller_data.dig(:ListingDetails, :MinimumBestOfferPrice)
      end

      def format_autoaccept_offer_price
        seller_data.dig(:ListingDetails, :BestOfferAutoAcceptPrice)
      end

      def format_allow_offer
        seller_data.dig(:BestOfferDetails, :BestOfferEnabled)
      end

      def format_start_time
        seller_data.dig(:ListingDetails, :StartTime)
      end

      def format_end_time
        seller_data.dig(:ListingDetails, :EndTime)
      end

      def format_category_id
        seller_data.dig(:PrimaryCategory, :CategoryID)
      end

      def format_status
        lifecycle_status = seller_data.dig(:lifecycleStatus)

        return "Unlinked" if product.nil?

        if "ACTIVE" == lifecycle_status
          published_status = seller_data.dig(:publishedStatus)
          case published_status
          when "PUBLISHED"
            return "Active"
          when "UNPUBLISHED"
            return "draft"
          when "READY_TO_PUBLISH", "IN_PROGRESS", "STAGE"
            return "InProgress"
          else
            return "Inactive"
          end
        else
          return "Inactive"
        end
      end

      def format_shipping_id
        seller_data.dig(:SellerProfiles, :SellerShippingProfile, :ShippingProfileID)
      end

      def format_return_id
        seller_data.dig(:SellerProfiles, :SellerReturnProfile, :ReturnProfileID)
      end

      def format_payment_id
        seller_data.dig(:SellerProfiles, :SellerPaymentProfile, :PaymentProfileID)
      end

      def format_category_name
        seller_data.dig(:PrimaryCategory, :CategoryName)
      end

      def format_images
        nil
      end

      def format_variant_group_id
        seller_data.dig(:variantGroupId)
      end

      def format_inventory
        {
          avail_to_sell_qty: inventory_count(seller_data).dig(:availToSellQty),
          sold_quantity: inventory_count(seller_data).dig(:soldQty) || 0,
          price: (seller_data.dig(:price, :amount) || 0),
        }
      end

      def format_variant_stock_items_data
        return nil if product.nil?

        variant_stock_items_data = {}

        if seller_data.dig(:Variations)
          variant_array = seller_data.dig(:Variations).kind_of?(Array) ? seller_data.dig(:Variations) : [seller_data.dig(:Variations)]
          variant_array.each do |variation|
            variant = Spree::Variant.find_by(sku: variation.dig(:sku))
            variant_stock_items_data[variant&.id] = {
              "quantity" => inventory_count(variation).dig(:inputQty) || 0,
              "sold_quantity" => inventory_count(variation).dig(:soldQty) || 0,
            }
          end
        else
          variant_stock_items_data[product&.master&.id] = {
            "quantity" => inventory_count(seller_data).dig(:inputQty) || 0,
            "sold_quantity" => inventory_count(seller_data).dig(:soldQty) || 0,
          }
        end

        variant_stock_items_data
      end

      def format_auction_pricing
        nil
      end

      def format_sale_channel_metadata
        return unless product.nil?

        seller_data
      end

      def format_sale_channel_hash
        return nil if product.nil?

        sale_channel_hash = { variants: {} }
        product_type = ""

        master_variant = product.master
        variations = seller_data.dig(:Variations)
        sale_channel_hash[:primary_variant_id] = master_variant.id if variations.present?

        if variations.present? && product.variants.any?
          variations.each do |variation|
            variant = RecordInserter::Listing::VariantFinder.new(product, variation.dig(:variantGroupInfo, :groupingAttributes)).call
            sale_channel_hash[:variants][variant.id] = response_to_content(variation) if variant.present?
            product_type = variation.dig(:productType) if product_type.blank?
          end
        else
          product_type = seller_data.dig(:productType)
          sale_channel_hash[:variants][master_variant.id] = response_to_content(seller_data)
        end

        product_type_in_db = Spree::WalmartProductType.where(name: product_type).first if product_type.present?

        if product_type.present? && product_type_in_db.present?
          sale_channel_hash[:product_type] = product_type_in_db.id
          sale_channel_hash[:product_group] = product_type_in_db.group.id
          sale_channel_hash[:category] = product_type_in_db.category.id
        end

        sale_channel_hash
      end

      def response_to_content(seller_data)
        specifics = {}
        specifics[:response_from_apis] = seller_data

        specifics[:item_content] = item_response_to_item_content(seller_data)
        specifics[:price_content] = { "sku": seller_data[:sku], "price": seller_data.dig(:price, :amount) }
        specifics[:inventory_content] = iventory_response_to_iventory_content(seller_data)
        specifics
      end

      def iventory_response_to_iventory_content(seller_data)
        nodes = []

        if seller_data.dig(:inventory, :nodes).present?
          seller_data.dig(:inventory, :nodes).each do |node|
            nodes << {
              "shipNode": node.with_indifferent_access[:shipNode],
              "quantity": {
                "inputQty": node.with_indifferent_access.dig(:inputQty, :amount),
                "availToSellQty": node.with_indifferent_access.dig(:availToSellQty, :amount),
                "reservedQty": node.with_indifferent_access.dig(:reservedQty, :amount),
              },
            }
          end
        end
        {
          "sku": seller_data[:sku],
          "shipNodes": nodes,
        }
      end

      def inventory_count(seller_data)
        return {} if seller_data.blank? || seller_data.dig(:sku).blank?
        sku = seller_data.dig(:sku)
        return item_quantity_hash.dig(sku) if item_quantity_hash.dig(sku).present?

        input_qty = 0
        avail_to_sell_qty = 0

        if seller_data.dig(:inventory, :nodes).present?
          seller_data.dig(:inventory, :nodes).each do |node|
            input_qty += node.with_indifferent_access.dig(:inputQty, :amount).to_i
            avail_to_sell_qty += node.with_indifferent_access.dig(:availToSellQty, :amount).to_i
          end
        end
        item_quantity_hash[sku] = {
          "inputQty": input_qty,
          "availToSellQty": avail_to_sell_qty,
          "soldQty": input_qty - avail_to_sell_qty,
        }
        item_quantity_hash[sku]
      end

      def item_response_to_item_content(seller_data)
        # https://developer.walmart.com/api/us/mp/items#operation/getAllItems
        # Item response fields:
        # mart
        # sku        should be set in orderable
        # condition  should be set in visible
        # availability  string
        # wpid    string
        # upc   string   should be set in orderable
        # gtin    string should be set in orderable
        # productName   string should be set in visible
        # shelf string
        # productType  string
        # price object    should be set in orderable
        # publishedStatus string
        # additionalAttributes  object
        # unpublishedReasons  object
        # lifecycleStatus string
        # variantGroupId  string should be set in visible
        # variantGroupInfo  object should be set in visible
        # isDuplicate boolean
        # duplicateItemInfo object

        item_content = { Orderable: {}, Visible: {} }
        fill_keys(seller_data, [:mart, :productType, :sku], item_content)
        fill_keys(seller_data, [:sku], item_content[:Orderable])
        mart = seller_data.dig(:mart)
        product_type = seller_data.dig(:productType)
        sku = seller_data.dig(:sku)
        gtin = seller_data.dig(:gtin)
        upc = seller_data.dig(:upc)
        if gtin.present?
          item_content[:Orderable][:productIdentifiers] = {
            productIdType: "GTIN",
            productId: gtin,
          }
        elsif upc.present?
          item_content[:Orderable][:productIdentifiers] = {
            productIdType: "UPC",
            productId: upc,
          }
        end
        item_content[:Orderable][:price] = seller_data.dig(:price, :amount) if seller_data.dig(:price).present?

        # visible
        fill_keys(seller_data, [:condition, :productName, :variantGroupId], item_content[:Visible])
        # variantGroupInfo (in seller_data) Example:
        # "variantGroupInfo" => { "isPrimary" => false, "groupingAttributes" => [{ "name" => "multipack_quantity", "value" => "100" }] } }
        if seller_data.dig(:variantGroupInfo)
          isPrimaryVariant = seller_data.dig(:variantGroupInfo, :isPrimary)
          names = seller_data.dig(:variantGroupInfo, :groupingAttributes).map do |a|
            item_content[:Visible][a[:name]] = a[:value]
            a[:name]
          end
          item_content[:Visible][:variantAttributeNames] = names
          item_content[:Visible][:isPrimaryVariant] = isPrimaryVariant ? "Yes" : "No"
        end
        item_content
      end

      def fill_keys(src, keys, dst)
        return unless keys.is_a?(Array)
        return unless src.is_a?(Hash) && dst.is_a?(Hash)

        keys.each do |key|
          dst[key] = src[key] if src[key].present?
        end
      end

      def format_variations
        return [] if seller_data[:sku].blank? || seller_data[:variantGroupId].nil?

        variations = []
        variant_group_id = seller_data.dig(:variantGroupId)

        if variant_group_id.present?
          seller_data.dig(:Variations).each do |variant|

            # Find the existing variant based on the groupingAttributes
            existing_variant = RecordInserter::Listing::VariantFinder.new(product, variant.dig(:variantGroupInfo, :groupingAttributes)).call

            variation_data = {
              variant: {
                sku: variant[:sku],
                price: variant.dig(:price, :amount),
                cost_currency: format_currency,
                currency: format_currency,
                lbs: 0.0,
                oz: 0.0,
                track_inventory: true,
              },
              options: format_option_values(variant[:variantGroupInfo]),
              inventory_data: {
                input_inventory_quantity: inventory_count(variant).dig(:inputQty).to_i || 0,
                available_inventory_quantity: inventory_count(variant).dig(:availToSellQty).to_i || 0,
                sold_inventory_quantity: inventory_count(variant).dig(:soldQty).to_i || 0,
                price: variant.dig(:price, :amount),
                variant_id: existing_variant&.id,
              },
              additional_data: {
                variation_specifics: variant.dig(:variantGroupInfo, :groupingAttributes),
              },
            }

            variations << variation_data
          end
        end

        variations
      end

      def format_option_values(variation_specific)
        return [] unless variation_specific

        option_type_array = Array(variation_specific[:groupingAttributes])

        option_type_array.map do |option_specific|
          {
            option_type: option_specific[:name],
            option_value: option_specific[:value],
          }
        end
      end

      # Product Methods

      def format_product_status
        "active"
      end

      def format_price
        seller_data.dig(:price, :amount)
      end

      def format_lbs
        0.0
      end

      def format_oz
        0.0
      end

      def format_currency
        seller_data.dig(:price, :currency)
      end

      def format_available_on
        Date.today.beginning_of_day
      end

      def format_condition_list
        nil
      end
    end
  end
end
