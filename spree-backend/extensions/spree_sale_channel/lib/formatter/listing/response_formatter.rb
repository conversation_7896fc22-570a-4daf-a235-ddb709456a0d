# app/formatters/listing/response_formatter.rb
module Formatter
  module Listing
    class ResponseFormatter < Base
      def format
        response = {
          listing: {
            title: format_value(:title),
            sku: format_value(:sku),
            quantity: format_value(:quantity),
            currency: format_value(:currency),
            item_id: format_value(:item_id),
            product_id: format_value(:product_id),
            store_id: format_value(:store_id),
            stock_item_id: format_value(:stock_item_id),
            item_url: format_value(:item_url),
            sale_channel_id: format_value(:sale_channel_id),
            description: format_value(:description),
            minimum_offer_price: format_value(:minimum_offer_price),
            autoaccept_offer_price: format_value(:autoaccept_offer_price),
            allow_offer: format_value(:allow_offer),
            start_time: format_value(:start_time),
            end_time: format_value(:end_time),
            category_id: format_value(:category_id),
            status: format_value(:status),
            shipping_id: format_value(:shipping_id),
            return_id: format_value(:return_id),
            payment_id: format_value(:payment_id),
            category_name: format_value(:category_name),
            variant_stock_items_data: format_value(:variant_stock_items_data),
            auction_pricing: format_value(:auction_pricing),
            sale_channel_hash: format_value(:sale_channel_hash),
            sale_channel_metadata: format_value(:sale_channel_metadata)
          }
        }

        response[:images] = format_value(:images)

        response[:inventory] = format_value(:inventory)

        response[:product] = format_product if format_value(:product).present?

        response[:variations] = format_value(:variations)
        response[:condition_list] = format_value(:condition_list)

        format_ebay_related_fileds(response)

        response
      end

      private

      def format_product
        {
          name: format_value(:title),
          description: format_value(:description),
          price: format_value(:price),
          status: format_value(:product_status),
          lbs: format_value(:lbs),
          oz: format_value(:oz),
          available_on: format_value(:available_on)
        }
      end

      def format_value(attribute)
        @response_formatter.send("format_#{attribute}")
      end

      def include_product?
        format_value(:category_id).present? && format_value(:status) == "active"
      end

      def format_ebay_related_fileds(response)
        if @sale_channel.is_ebay_channel?
          response[:listing][:ebay_shipping_data] = format_value(:ebay_shipping_data)
        end
      end
    end
  end
end
