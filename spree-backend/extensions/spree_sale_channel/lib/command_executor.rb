require 'json'
require 'open3'

module CommandExecutor
  def self.execute(command, *args)
    stdout, stderr, status = Open3.capture3(command, *args)
    if status.success?
      JSON.parse(stdout)
    else
      log_error(stderr)
      { error: stderr.strip }
    end
  rescue JSON::ParserError => e
    log_error("JSON Parsing Error: #{e.message}")
    { error: 'Failed to parse response' }
  end

  def self.log_error(message)
    # Log error or create an announcement, for example
    Rails.logger.error("Command Execution Error: #{message}")
  end
end
