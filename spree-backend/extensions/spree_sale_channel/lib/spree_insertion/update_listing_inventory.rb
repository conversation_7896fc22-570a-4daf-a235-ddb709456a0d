module SpreeInsertion
  class UpdateListingInventory < SpreeInsertion::Base
    def execute(optional)
      # Lising record is present in spree_response
      listing_inventory = spree_response.listing_inventories.find_by(variant_id: optional.first[0])
      variant = Spree::Variant.find_by(id: optional.first[0])
      if variant.total_available < listing_inventory.total_listed_qty
        listing_inventory.update(total_listed_qty: listing_inventory.total_sold_qty + optional.first[1].dig(:available_quantity))
      else
        listing_inventory.update(total_listed_qty: listing_inventory.total_listed_qty - optional.first[1].dig(:line_item_qty))
      end
    end
  end
end
