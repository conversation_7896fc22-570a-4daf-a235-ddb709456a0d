module SpreeInsertion
  class EbayShipping < SpreeInsertion::Base
    attr_accessor :listing

    def format_item_shipping_date(listing)
      return unless listing.is_ebay_channel?

      @listing = Spree::Listing.find_by(id: listing.id)
      if spree_response.present?
        # spree_response = spree_response.with_indifferent_access
        ebay_shipping_data = if spree_response.dig(:listing, :length) || spree_response.dig(:listing, :package_dimension).present? || spree_response.dig("listing", "length") || spree_response.dig("listing", "package_dimension").present?
            {
              "ShippingPackageDetails": {
                "PackageDepth" => get_field_value(:height),
                "PackageLength" => get_field_value(:length),
                "PackageWidth" => get_field_value(:width),
                # "ShippingIrregular" => "false",
                # "ShippingPackage" => "PackageThickEnvelope",
                "WeightMajor" => get_field_value(:weight_lbs),
                "WeightMinor" => get_field_value(:weight_oz),
              },
            }
          else
            {}
          end
        puts("jay_debug EbayShipping ------------ format_item_shipping_date ------------ #{ebay_shipping_data}")
        puts("jay_debug EbayShipping ------------ format_item_shipping_date ------------ listing.id: #{listing.id}")
        listing.update!(ebay_shipping_data: ebay_shipping_data)
      end
    end

    private
    def get_symbol_field_value(key)
      key_symbol = key.to_sym
      spree_response.dig(key_symbol) || spree_response.dig(:listing, key_symbol) || spree_response.dig(:listing, :package_dimension, key_symbol)
    end

    def get_string_field_value(key)
      key_string = key.to_s
      spree_response.dig(key_string) || spree_response.dig("listing", key_string) || spree_response.dig("listing", "package_dimension", key_string)
    end

    def get_field_value(key)
      get_symbol_field_value(key) || get_string_field_value(key)
    end
  end
end
