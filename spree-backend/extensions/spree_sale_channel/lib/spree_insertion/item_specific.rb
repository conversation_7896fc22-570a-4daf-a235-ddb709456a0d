module SpreeInsertion
  class ItemSpecific < SpreeInsertion::Base
    attr_accessor :listing

    def format_item_specific_record_response(listing)
      @listing = Spree::Listing.find_by(id: listing.id)
      if spree_response.present?
        filtered_hash = spree_response.reject { |_, value| value.nil? || value.empty? }
        listing.update!(ebay_item_specifics: filtered_hash)
      end
    end
  end
end
