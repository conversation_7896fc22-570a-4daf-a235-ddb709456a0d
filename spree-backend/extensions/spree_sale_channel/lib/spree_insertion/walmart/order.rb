class ProductNotCreatedError < ::StandardError
end

module SpreeInsertion
  module Walmart
    class Order < SpreeInsertion::Base
      include ::Spree::Admin::SubscribersHelper
      include ::Spree::Admin::LineItemsExtHelper
      include Rails.application.routes.url_helpers

      attr_accessor :order_data, :store, :import_log, :order, :address, :product, :listing, :sale_channel_id

      def initialize(order_data, store, import_log, sale_channel_id, walmart_update_order_hook)
        @store = store
        @order_data = order_data
        @import_log = import_log
        @sale_channel_id = sale_channel_id
        @walmart_update_order_hook = walmart_update_order_hook
      end

      def make
        begin
          number = order_data.dig(:purchaseOrderId)
          @order = Spree::Order.find_or_create_by(number: number)
          order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : walmart Order has been successfully synced")
          order_email = order_data.dig(:customerEmailId)
          previous_state = order.state
          order.update!(item_total: get_item_total_price, shipment_total: get_price('deliveryCost'), total: get_total_price, email: order_email, state:
          order_state, shipment_state: get_order_shipment_state, currency: 'USD', last_modified_date: Time.now, sale_channel_metadata:
          order_data, completed_at: completed_at(order_data), channel: 'walmart', walmart_fees: walmart_fees(number))

          handle_cancel_order if order_state == 'canceled'
          listing_sku = order_data.dig(:orderLines, :orderLine).first.dig(:item, :sku)
          @listing, @product = find_listing(listing_sku)
          order.update(sale_channel_id: sale_channel_id)
          create_and_assign_address
          create_and_assign_line_items
          create_and_assign_payments
          update_and_assign_shipments

          # create subscriber for email campaign
          find_or_create_subscriber

          check_line_items_ext if previous_state != 'complete' && order_state == 'complete'

          order.update_columns(payment_state: get_payment_state, shipment_state: get_order_shipment_state)
          # handle_refunded_order if order_data.dig(:orderLines, :orderLine).first.dig(:refund).present?
        rescue StandardError => e
          @walmart_update_order_hook.update(error_details: e.message)
          update_failed_import_log_counter(import_log)
          write_import_logs(import_log, e, order.number)
        else
          update_successful_import_log_counter(import_log)
          # GetTransactionsJob.perform_later(store.id, oauth_application.id, order.number) if oauth_application
        end
      end

      def find_or_create_subscriber
        subscriber = Spree::Subscriber.find_or_create_by(email: order.email)
        process_subscriber_action(subscriber, 'placed order', order) if subscriber
      end

      def check_line_items_ext
        if order.line_items.present?
          order.line_items.each do |item|
            line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
            process_line_items_ext(line_items_ext, item, order) if line_items_ext
          end
        end
      end

      def completed_at(order_data)
        order_date = order_data[:orderDate]
        timestamp = order_date / 1000
        time = Time.at(timestamp)
        time.strftime('%Y-%m-%d %H:%M:%S.%6N %z')
      end

      def update_and_assign_shipments
        order.shipments.reload
        walmart_line_items = order_data.dig('orderLines', 'orderLine')
        return unless walmart_line_items.present?
        Rails.logger.debug("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ Apartment Name:")
        Rails.logger.debug { "Apartment: #{Apartment::Tenant.current}" }


        zipped_result = order.shipments.map do |shipment|
          line_item =  shipment.line_items&.first
          sku = line_item ? line_item.sku_in_walmart_listing(line_item.variant_id) : ''
          Rails.logger.debug("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ Listing Sale Channel Hash for #{order.number}:")
          Rails.logger.debug { "Sale Channel Hash: #{ line_item&.listing&.sale_channel_hash }" }

          matching_item = walmart_line_items.find { |item| item.dig(:item, :sku) == sku }
          [shipment, matching_item] if matching_item
        end.compact

        zipped_result.each do |shipment, line_item|
          tracking_number = line_item.dig(:orderLineStatuses, :orderLineStatus).first.dig(:trackingInfo, :trackingNumber)
          carrier_name = line_item.dig(:orderLineStatuses, :orderLineStatus).first.dig(:trackingInfo, :carrierName, :carrier)
          oauth_application_id = listing.sale_channel.oauth_application.id

          # Download the shipping label PDF and update the shipment details
          unless order_state == 'canceled'
            unless !tracking_number.present?
              begin
                response = ::Walmart::ShippingApis::DownloadLabel.new(order&.store&.id, oauth_application_id).call(tracking_number, carrier_name) unless shipment.shipping_label.present?
                attach_pdf_to_shipment(shipment, response.body) if response.present?
              rescue => e
                order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : walmart download label from walmart exception for shipment (#{shipment&.number}) and ignore it")
              end
            end
          end
          shipment.reload
          shipment.update_columns(tracking: tracking_number) if tracking_number.present?
          state = get_shipment_state(line_item, shipment)
          shipment.update_columns(
            state: state,
            stock_location_id: get_stock_location(shipment)
          )

          append_free_shipping_method(shipment) if shipment.order_package.nil? || shipment.order_package.tracking_label.nil?

          shipment.reload
          order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : walmart Received tracking details from Walmart and updating shipment (#{shipment.number})")
          next unless shipment.shipped?

          order_package = shipment.order_package || order.order_packages.first_or_create

          order_package.update_columns(state: 'shipped', tracking_label: shipment.tracking_label, tracking: shipment.tracking)
          # order_package.update_columns(selected_shipping_rate_id: shipment.selected_shipping_rate&.id) if shipment.shipping_rates
          order_package.selected_shipping_rate = shipment.selected_shipping_rate

          shipment.update_columns(order_package_id: order_package&.id)
          shipment.stock_item_units.update(state: 'shipped')
          shipment.inventory_units.update(state: 'shipped')
        end
      end

      def attach_pdf_to_shipment(shipment, pdf_data)
        # Attach the downloaded PDF to the shipment
        shipment.shipping_label.attach(io: StringIO.new(pdf_data), filename: "shipping_label_#{shipment.id}.pdf", content_type: 'application/pdf')
        shipment.update(tracking_label: rails_blob_path(shipment.shipping_label, disposition: 'inline', only_path: true))
      end

      def append_free_shipping_method(shipment)
        walmart_shipment_value = 0
        shipping_category_name = get_order_shipment_state != 'shipped' ? 'Free Shipping' : 'Walmart Shipping'
        shipping_method = create_shipping_method(shipment, shipping_category_name)
        return unless shipping_category_name

        # If buy Label on AM order page, then we don't need to add 'Free Shipping' | 'Walmart Shipping'
        selected_shipping_method = shipment.selected_shipping_rate&.shipping_method&.name
        return ['Free Shipping', 'Walmart Shipping'].exclude?(shipping_category_name)

        shipment.shipping_methods << shipping_method unless shipment.shipping_methods.include?(shipping_method)
        shipment.shipping_rates.update_all(selected: false)
        shipment.reload
        free_shipping_rate = shipment.shipping_rates.where(shipping_method_id: shipping_method.id)
        free_shipping_rate.update(selected: true, buyer_selected: true)
        shipment.reload
        shipment.update_columns(cost: 0.0, buyer_paid_amount: walmart_shipment_value)
      end

      def create_shipping_method(_shipment, shipping_category_name = nil)
        shipping_category = if shipping_category_name.present?
                              Spree::ShippingCategory.find_or_create_by(name: shipping_category_name,
                                                                        store_id: store.id)
                            else
                              Spree::ShippingCategory.first
                            end
        attributes =
          {
            name: shipping_category_name || 'Walmart_Shipping',
            display_on: 'both',
            shipping_categories: [shipping_category],
            store_id: store.id
          }
        Spree::ShippingMethod.where(name: attributes[:name], store_id: attributes[:store_id]).first_or_create! do |method|
          method.calculator = Spree::Calculator::Shipping::FlatRate.first_or_create! if method.calculator.nil?
          method.display_on = attributes[:display_on]
          method.shipping_categories = attributes[:shipping_categories]
        end
      end

      def get_stock_location(shipment)
        variant_id = shipment.line_items&.first.variant_id
        stock_items = Spree::StockItem.joins(:stock_location).where(stock_location: { active:true }).where(variant_id: variant_id)
        return stock_items&.order(count_on_hand: :desc)&.first&.stock_location_id if stock_items.any?

        Spree::StockLocation.active.default
      end

      def get_order_shipment_state
        old_shipment_state = order&.shipment_state
        order_lines = order_data.dig('orderLines', 'orderLine')

        all_shipped = order_lines.all? do |order_line|
          statuses = order_line.dig('orderLineStatuses', 'orderLineStatus')
          statuses.all? { |status| status['status'] == 'Shipped' || status['status'] == 'Delivered' }
        end

        any_shipped = order_lines.all? do |order_line|
          statuses = order_line.dig('orderLineStatuses', 'orderLineStatus')
          statuses.all? { |status| status['status'] == 'Shipped' || status['status'] == 'Delivered' }
        end

        any_cancelled = order_lines.all? do |order_line|
          statuses = order_line.dig('orderLineStatuses', 'orderLineStatus')
          statuses.all? { |status| status['status'] == 'Cancelled' }
        end

        if all_shipped
          'shipped'
        elsif any_shipped
          'partial'
        elsif any_cancelled
          'canceled'
        else
          old_shipment_state.present? && old_shipment_state == 'shipped' ? old_shipment_state : 'ready'
        end
      end

      def get_shipment_state(line_item, shipment)
        old_shipment_state = shipment&.state
        statuses = line_item['orderLineStatuses']['orderLineStatus']

        all_shipped = statuses.all? { |status| ['Shipped', 'Delivered'].include?(status['status']) }
        any_cancelled = statuses.all? { |status| status['status'] == 'Cancelled' }

        if all_shipped
          'shipped'
        elsif any_cancelled
          'canceled'
        else
          old_shipment_state.present? && old_shipment_state == 'shipped' ? old_shipment_state : 'ready'
        end
      end

      def get_payment_state
        get_payment_state = get_order_shipment_state == 'canceled' ? 'void' : 'paid'
      end

      def get_email_id
        order_data.dig(:customerEmailId)
      end

      def order_state
        return 'canceled' if order_data.dig(:orderLines, :orderLine).first.dig(:orderLineStatuses, :orderLineStatus).first.dig(:status) == 'Cancelled'

        'complete'
      end

      def create_and_assign_line_items
        order_data.dig(:orderLines, :orderLine).each do |line_item|
          @listing, @product = find_listing(line_item.dig(:item, :sku))
          Rails.logger.info("walmart create_and_assign_line_items, listing id: #{@listing.id}") if @listing.present?

          walmart_line_item = Spree::LineItem.where(order_id: order.id, walmart_line_item_id: line_item.dig(:lineNumber)).first
          if walmart_line_item.present?
            walmart_line_item.update(quantity: get_line_item_quantity(line_item), price: get_line_item_price(line_item), currency: 'USD', listing_id: listing&.id)
            create_line_item_adjustment(walmart_line_item, line_item)
          else
            next unless listing
            variant_id = find_variant_id(listing, line_item)
            next unless variant_id
            variant = Spree::Variant.find(variant_id)
            next unless variant

            walmart_line_item = Spree::LineItem.find_or_initialize_by(variant_id: variant_id, order_id: order.id, currency: 'USD', walmart_line_item_id: line_item.dig(:lineNumber)&.to_i)

            # Updating price since it's sourced from the product.
            walmart_line_item.update(quantity: get_line_item_quantity(line_item), price: get_line_item_price(line_item), listing_id: listing&.id)

            order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : walmart Line Item (#{walmart_line_item.id}) has been successfully created")

            # Pull listing corresponding to order to update listing inventory
            # sync_walmart_listing_for_order(line_item)
            order.reload
            # append free shipping method in shipment for free_shipping orders
            inventory_units = walmart_line_item.inventory_units.reload
            shipment = inventory_units&.first&.shipment
            if shipment.present?
              state = get_shipment_state(line_item, shipment)
              shipment.update(state: state)
              append_free_shipping_method(shipment)
            end

            # Set inventory units to false for walmart orders as we are not running callbacks to finalize inventory_units
            walmart_line_item.inventory_units.update_all(pending: false, updated_at: Time.now)

            # Need to run only while creation of order
            SyncListingsAvailable.perform_now(store.id, listing, walmart_line_item.quantity, variant_id) if store.risky_listing.present?

            # assign pack_size of listing in line_item
            walmart_line_item.update(pack_size: listing.pack_size_value) if listing.pack_size_toggle.present?
            # Create Walmart Taxes
            create_line_item_adjustment(walmart_line_item, line_item)
          end
        end
      end

      def find_variant_id(listing, line_item)
        variant_sku = line_item.dig(:item, :sku)
        return if variant_sku.blank?

        # Find variant id in sale_channel_hash by sku
        variant_id = listing.walmart_variant_id_by_sku(variant_sku)
        variant_id || listing&.product&.master&.id
      end

      def get_line_item_charge_amount(line_item)
        line_item.dig(:charges, :charge).first.dig(:chargeAmount, :amount)&.to_f
      end

      def get_line_item_price(line_item)
        quantity = get_line_item_quantity(line_item)
        return get_line_item_charge_amount(line_item) if quantity.nil? || quantity.zero?
        get_line_item_charge_amount(line_item) / quantity
      end

      def get_line_item_quantity(line_item)
        line_item.dig(:orderLineQuantity, :amount)&.to_i
      end

      def create_line_item_adjustment(walmart_line_item, line_item)
        return unless line_item.dig('charges', 'charge').present?

        tax_price = line_item.dig('charges', 'charge').sum do |charge|
          if charge['chargeType'] == 'PRODUCT'
            # Add the charge amount and the tax amount
            tax_field = charge.dig('tax', 'taxAmount', 'amount')
            tax_amount = tax_field.present? ? tax_field&.to_f : 0
          else
            0
          end
        end
        tax_category = Spree::TaxCategory.find_or_create_by(name: 'Walmart_Tax', store_id: store.id)
        tax_rate = Spree::TaxRate.where(tax_category_id: tax_category.id, name: 'Walmart_Tax_Rate', amount: tax_price&.to_d).first_or_initialize
        tax_rate.calculator = Spree::Calculator::DefaultTax.first_or_create! if tax_rate.calculator.nil?
        return unless tax_rate.save!

        walmart_order_adjustment = walmart_line_item.adjustments.where(label: 'walmart_tax', source_type: 'Spree::TaxRate', order_id: order.id).take
        if walmart_order_adjustment.blank?
          walmart_order_adjustment = walmart_line_item.adjustments.create!(label: 'walmart_tax', source_id: tax_rate.id, source_type: 'Spree::TaxRate', amount: tax_price&.to_d, order_id: order.id)
        else
          walmart_order_adjustment.update(amount: tax_price&.to_d, source_id: tax_rate.id)
        end
        order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : walmart Tax Adjustment (#{walmart_order_adjustment.id}) has been successfully created with amount - #{tax_price&.to_d}")
      end

      def find_listing(sku)
        listing = Spree::Listing.where(status: ["Active", "InProgress"]).joins(:walmart_listing_skus)
          .where(spree_walmart_listing_skus: { sku: sku, sale_channel_id: sale_channel_id }).first
        listing = Spree::Listing.find_by(sku: sku, sale_channel_id: sale_channel_id) if listing.blank?
        product = listing&.product
        [listing, product]
      end

      def create_and_assign_address
        return false if order_data.dig(:shippingInfo).blank?

        address_data = order_data.dig(:shippingInfo, :postalAddress)

        return unless address_data.present?

        full_name = address_data.dig(:name) || ''
        last_name_check = full_name.split(' ').count >= 2
        if last_name_check
          firstname = full_name.split(' ').first
          lastname = full_name.split(' ').last
        else
          firstname = full_name
          lastname = ''
        end
        address_hash = { firstname: firstname, lastname: lastname, phone: order_data.dig(:shippingInfo, :phone) }

        final_hash = get_basic_address_hash(address_data)
        return if final_hash.empty?
        final_hash.merge!(address_hash)
        return unless final_hash.present?

        if order.ship_address.present?
          @address = order.ship_address
          address.update(final_hash)
        else
          @address = Spree::Address.create!(final_hash)
          order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : walmart Address created with id - Address #{@address.id}")
          order.update!(ship_address_id: address.id, bill_address_id: address.id)
        end
      end

      def create_and_assign_payments
        check_payment_method = Spree::PaymentMethod::Check.where(
          name: 'Walmart',
          description: 'Walmart Payments.',
          active: true
        ).first_or_initialize
        check_payment_method.store_ids = [store.id]
        check_payment_method.save!

        payment = Spree::Payment.find_or_create_by(
          order_id: order.id,
          payment_method: check_payment_method,
          state: 'completed'
        )
        payment.update(amount: get_total_price) if payment.present?
        payment
      end

      def get_basic_address_hash(address_data)
        basic_address_hash = {}
        if address_data.present?
          country = find_country(address_data.dig(:country))
          return basic_address_hash if country.nil?
          state = find_state(address_data.dig(:state), country)
          return basic_address_hash if state.nil?
          basic_address_hash = {
            address1: address_data.dig(:address1),
            address2: address_data.dig(:address2),
            city: address_data.dig(:city),
            zipcode: address_data.dig(:postalCode) || '23456',
            country_id: country.id,
            phone: address_data.dig(:phone),
            state_name: address_data.dig(:state),
            state_id: state&.id
          }
        end
        basic_address_hash
      end

      def find_country(country_code)
        country = Spree::Country.find_by(iso: country_code)
        country.presence || Spree::Country.default
      end

      def find_state(state_abbr, country)
        state = ::Spree::State.find_by(abbr: state_abbr&.upcase, country_id: country.id) if state_abbr.present? && country.present?
        state
      end

      def get_price(_price_type)
        0
      end

      def get_item_total_price
        order_data.dig('orderLines', 'orderLine').sum do |order_line|
          order_line.dig('charges', 'charge').sum do |charge|
            if charge['chargeType'] == 'PRODUCT'
              # Add the charge amount and the tax amount
              quantities_ordered = order_line.dig(:orderLineQuantity, 'amount')&.to_i
              charge_amount = charge.dig('chargeAmount', 'amount')&.to_f
            else
              0
            end
          end
        end
      end

      def get_total_price
        order_data.dig('orderLines', 'orderLine').sum do |order_line|
          order_line.dig('charges', 'charge').sum do |charge|
            if charge['chargeType'] == 'PRODUCT'
              # Add the charge amount and the tax amount
              quantities_ordered = order_line.dig(:orderLineQuantity, 'amount')&.to_i
              charge_amount = charge.dig('chargeAmount', 'amount')&.to_f
              tax_field = charge.dig('tax', 'taxAmount', 'amount')
              tax_amount = tax_field.present? ? tax_field&.to_f : 0
              tax_amount = order.sales_tax if tax_amount == 0  #When the order is canceled, then tax is null in order_data
              charge_amount + tax_amount
            else
              0
            end
          end
        end
      end

      # def handle_refunded_order
      #   refund_items = order_data.dig(:orderLines, :orderLine).map { |line_item| line_item.dig(:refund) }
      #   refund_items.each do |refund|
      #     order.reload
      #     payment = order.payments.last
      #     update_order_once_refunded && return if payment.refunds.count.eql?(refund_items.count)

      #     create_refund_for_order(payment, refund_items)
      #     update_order_once_refunded
      #   end
      # end

      # def update_order_once_refunded
      #   order.update_columns(payment_state: 'void', shipment_state: 'canceled')
      #   order.shipments.first.update_columns(state: 'canceled') if order.shipments.present?
      #   order.payments.first.update_columns(state: 'void') if order.payments.present?
      #   order.tracker_logs.create(details: "Refund has been created with order shipment state #{order.shipment_state} and payment state #{order.payment_state}")
      # end

      # def update_order_once_refunded
      #   order.update_columns(payment_state: 'void', shipment_state: 'canceled')
      #   order.shipments.first.update_columns(state: 'canceled') if order.shipments.present?
      #   order.payments.first.update_columns(state: 'void') if order.payments.present?
      #   order.tracker_logs.create(details: "Refund has been created with order shipment state #{order.shipment_state} and payment state #{order.payment_state}")
      # end

      # def create_refund_for_order(payment, refund_items)
      #   refund_items.each do |data|
      #     refund_amount = data
      #     refund_record = payment.refunds.find_or_initialize_by(refund_reference_id: data[:refundReferenceId], transaction_id: data[:refundReferenceId])
      #     refund_record.update(amount: refund_amount, refund_reason_id: 1)
      #   end
      # end

      # def sync_walmart_listing_for_order(line_item)
      #   listing = Spree::Listing.find_by(item_id: line_item.dig(:legacyItemId))
      #   return unless listing.present?

      #   GetItemJob.perform_later(listing, store, listing.item_id, nil, "Order")
      #   details = "ImportLog (#{import_log.id}): GetItemJob has been triggered for order number - #{order.number} and listing item_id - #{listing.item_id} to update listing inventory"
      #   order.tracker_logs.create(details:)
      # end

      def handle_cancel_order
        order.shipments.each do |shipment|
          shipment.update_columns(state: 'canceled')
          shipment.stock_item_units.update(state: 'stock', line_item_id: nil, shipment_id: nil)
        end
      end

      def walmart_fees(purchase_order)
        return estimate_walmart_fees if Spree::WalmartReconciliation.where(purchase_order: purchase_order).count <= 0
        {
          commission: Spree::WalmartReconciliation.where(purchase_order: purchase_order, amount_type: "Commission on Product").sum(:amount).abs,
          shipping_cost: Spree::WalmartReconciliation.where(purchase_order: purchase_order, transaction_description: [
                                                              "Walmart Shipping Label Service Charge",
                                                              "Shipping Insurance fee",
                                                            ]).sum(:amount).abs,
        }
      end

      def estimate_walmart_fees
        commission_total = order.line_items.sum do |item|
          listing = item.listing
          next 0 if listing.nil? || listing.sale_channel_hash.blank?

          sale_channel_hash = listing.sale_channel_hash.with_indifferent_access

          category_id = sale_channel_hash[:category]
          group_id    = sale_channel_hash[:product_group]
          type_id     = sale_channel_hash[:product_type]

          ::Walmart::CommissionCalculator
            .new(category_id: category_id, group_id: group_id, type_id: type_id, price: item.pre_tax_amount)
            .calculate
        end

        commission_total.positive? ? { estimated_commission: commission_total } : {}
      end
    end
  end
end
