class ProductNotCreatedError < ::StandardError
end

module SpreeInsertion
  module Walmart
    module CreateProduct
      class Product < SpreeInsertion::Base
        include ::WalmartHelper

        attr_accessor :seller_data, :product, :store, :import_log, :need_stock_update, :listing, :sale_channel_id
        attr_accessor :sale_channel, :oauth_application

        def initialize(seller_data, store, import_log, sale_channel_id)
          @store = store
          @seller_data = seller_data
          @import_log = import_log
          @sale_channel_id = sale_channel_id
          @sale_channel = Spree::SaleChannel.where(id: sale_channel_id).take
          @oauth_application = sale_channel&.oauth_application
        end

        def make
          begin
            is_new_product = false
            wpid = seller_data.dig(:wpid)
            itemID = wpid
            sku = seller_data.dig(:sku)
            product_sku = (seller_data[:Variations].blank? ? "" : "master-") + sku
            pruduct_name = seller_data.dig(:productName)
            variant_group_id = seller_data.dig(:variantGroupId)
            return nil if sku.blank?
            return nil if currency.blank?
            return nil if wpid.blank?
            return nil unless store.default_currency == currency
            @listing = get_walmart_listing(sale_channel_id, [sku], variant_group_id, itemID)
            @product = listing&.product || check_product_already_exists(sku, pruduct_name)
            # @product = listing&.product
            unless @product.present?
              @product = Spree::Product.new(name: pruduct_name, price: price, description: description, status: "active",
                                            lbs: lbs, oz: oz, available_on: Date.today.beginning_of_day)
              product.stores << store
              is_new_product = true
              # @need_stock_update = true
              product.sku = product_sku unless Spree::Variant.where(deleted_at: nil).where("sku ILIKE '#{sku}'").exists?
              product.master.currency = currency
              product.master.cost_currency = currency
            end
            product.set_slug
            # return product
            ret = product.save
            unless ret
              raise ProductNotCreatedError
            end

            if seller_data[:Variations]
              save_variants(seller_data[:Variations])
            else
              sync_listing_sold_qty(product.master, seller_data.dig(:inventory, :availToSellQty).to_i, seller_data.dig(:price, :amount))
            end

            # TODO: Need to confirm if it is possible to add images
            save_product_img(seller_data[:PictureDetails]) if seller_data[:PictureDetails] && is_new_product
            update_fileds_from_walmart_report(seller_data_skus)
          rescue ProductNotCreatedError => e
            update_failed_import_log_counter(import_log)
            write_walmart_import_logs(import_log, "#{e}, #{product&.errors&.full_messages}", "Product/Listing")
          rescue StandardError => e
            Rails.logger.error("Error while creating product: #{e.message}")
            # Increment import log error counter
            update_successful_import_log_counter(import_log)
            # update_failed_import_log_counter(import_log)
            write_walmart_import_logs(import_log, "StandardError, #{e}", "Product/Listing")
          else
            # Increment import log success counter
            update_successful_import_log_counter(import_log)
          end
          product
        end

        def find_city
          seller_data.dig(:Location) || "New York"
        end

        def find_state
          location = seller_data[:Location]
          state_name = location&.split(",")&.last if location
          if state_name
            state = Spree::State.find_by(name: state_name)
            state ||= find_country&.states&.first
          else
            state = find_country&.states&.first
          end
          state || Spree::State.first
        end

        def find_country
          # TODO: Get iso via Spree::OauthApplication site_id
          Spree::Country.find_by(iso: "US")
        end

        def currency
          seller_data.dig(:price, :currency)
        end

        def lbs
          # Not Supported
          # seller_data.dig(:ShippingWeight, :measure).to_f
          0.0
        end

        def oz
          0.0
        end

        # For Saving taxon in products table from Ebay
        # def save_taxons(taxon)
        #   taxon_id = Spree::Taxon.find_by(category_id: taxon[:CategoryID])&.id if taxon[:CategoryID]
        #   product.taxon_ids = taxon_id if taxon_id
        # end

        def save_variants(variants)
          variant_array = variants.is_a?(Array) ? variants : [variants]
          variant_array.each_with_index do |variant, _index|
            next if variant.dig(:sku).blank?

            variant_sku = variant[:sku]
            variant_price = variant.dig(:price, :amount)
            variant_quantity = variant.dig(:inventory, :availToSellQty).to_i
            existing_variant = get_walmart_variant(sale_channel_id, variant_sku)
            existing_variant = Spree::Variant.where(product_id: product.id, sku: variant_sku).take if existing_variant.blank?
            if existing_variant.present?
              existing_variant.update(price: variant_price, cost_currency: currency, currency: currency)
              sync_listing_sold_qty(existing_variant, variant_quantity, variant_price)
            else
              product_variant = product.variants.new(
                price: variant.dig(:price, :amount),
                cost_currency: currency,
                currency: currency,
                lbs: lbs,
                oz: oz,
                track_inventory: true,
              )
              product_variant.sku = variant[:sku] unless Spree::Variant.where(deleted_at: nil).where("sku ILIKE '#{variant[:sku]}'").exists?
              # @need_stock_update = true
              option_value = []
              option_value = create_option_value(variant[:variantGroupInfo]) if variant.dig(:variantGroupId).present?
              product_variant.option_values = option_value
              sync_listing_sold_qty(product_variant, variant_quantity, variant_price) if product_variant.save!
            end
          end
        end

        def creat_option_from_variant(variation_specifics) # [1,2]
          return nil unless product.variants.present?

          ebay_option_value = []

          option_type_value_array = variation_specifics.dig(:groupingAttributes).is_a?(Array) ? variation_specifics.dig(:groupingAttributes) : [variation_specifics.dig(:groupingAttributes)]
          option_type_value_array.each do |name_value_list|
            option_type_id = Spree::OptionType.find_by(name: name_value_list.dig(:name))
            option_value_id = Spree::OptionValue.find_by(name: name_value_list.dig(:value), option_type_id: option_type_id)&.id

            ebay_option_value += [option_value_id] if option_value_id.present?
          end

          product.variants.each do |variant|
            return variant if variant.option_values && (variant.option_values&.to_a&.pluck(:id) == ebay_option_value)
          end
          nil
        end

        def update_fileds_from_walmart_report(skus = [])
          item_images = { PictureURL: [] }
          return if oauth_application.blank?

          oauth_application.walmart_report_contents.where(sku: skus).each do |report_content|
            item_images[:PictureURL] << report_content.primary_image_url if report_content.primary_image_url.present?
          end
          save_product_img(item_images) if item_images[:PictureURL].present?
        end

        def save_product_img(img_urls)
          images = img_urls[:PictureURL]&.kind_of?(Array) ? img_urls[:PictureURL] : [img_urls[:PictureURL]]
          return false unless images.any?

          images.each do |img|
            begin
              ActiveRecord::Base.transaction do
                image = URI.parse(img)
                image_name = File.basename(image.path)
                option_text = "master"
                filename = "#{option_text}_#{product.master&.id}_#{image_name}"
                is_exist = ActiveStorage::Blob
                  .joins("INNER JOIN active_storage_attachments ON active_storage_blobs.id = active_storage_attachments.blob_id")
                  .joins("INNER JOIN spree_assets ON active_storage_attachments.record_id = spree_assets.id")
                  .where("spree_assets.viewable_id = #{product.master&.id} AND spree_assets.viewable_type = 'Spree::Variant' AND active_storage_blobs.filename = '#{filename}'").any?
                next if is_exist

                image_file = image.open
                product.images.create!(attachment: { io: image_file, filename: filename })
              end
            rescue StandardError => e
              next
            end
          end
        end

        def create_option_value(variation_specific)
          option_value = []
          return unless variation_specific

          option_type_array = variation_specific[:groupingAttributes].is_a?(Array) ? variation_specific[:groupingAttributes] : [variation_specific[:groupingAttributes]]
          option_type_array.each do |option_specific|
            option_type = store.option_types.find_or_create_by!(name: option_specific[:name], presentation: option_specific[:name]&.capitalize)
            product.option_types << option_type unless product.option_types.include?(option_type)
            option_val = option_type.option_values.find_or_initialize_by_downcase_name(option_specific[:value])
            option_val.update(presentation: option_specific[:value].capitalize)
            option_value += [option_val]
          end
          option_value
        end

        def description
          seller_data[:shortDescription]
        end

        def price
          seller_data.dig(:price, :amount)
        end

        # def available_on
        #   seller_data[:ListingDetails][:StartTime] if seller_data[:ListingDetails]
        # end

        # def discontinue_on
        #   seller_data[:ListingDetails][:EndTime] if seller_data[:ListingDetails]
        # end

        def check_product_already_exists(sku, pruduct_name)
          # different sku can have the same product name on Walmart
          # Example: gtin: 02309136125730 & 05157352879060
          variant = Spree::Variant.where(deleted_at: nil).where("sku ILIKE '#{sku}'").take
          product = variant&.product
          product.name == pruduct_name ? product : nil if product.present?
        end

        def sync_listing_sold_qty(variant, quantity, variation_specific_price = nil)
          variant_price = variation_specific_price ? variation_specific_price : price

          return unless listing.present?
          listing_inventory = listing.listing_inventories.find_or_create_by(variant_id: variant.id)
          low_availability = listing.walmart_item_low_availability_toggle_by_variant_id(variant.id)
          update_data = {
            total_listed_qty: quantity,
            total_sold_qty: 0,
            price: variant_price,
            low_availability_toggle: low_availability.dig("low_availability_toggle") || false,
            low_availability_value: low_availability.dig("low_availability_value") || 0,
          }
          listing_inventory.update(update_data) if listing_inventory.present?
        end

        def seller_data_skus
          skus = []
          skus << seller_data.dig(:sku) if seller_data.dig(:sku).present?
          seller_data[:Variations].to_a.each do |variant|
            skus << variant[:sku] if variant[:sku].present?
          end

          skus.uniq
        end
      end
    end
  end
end
