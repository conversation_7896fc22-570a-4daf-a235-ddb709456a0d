module SpreeInsertion
  module Walmart
    class UpdateListingInventory < SpreeInsertion::Base
      include ::WalmartHelper

      # task is WalmartPublishTask
      def execute(task)
        return if task.blank? || task.listing.blank?
        variant_id = task.listing.walmart_variant_id_by_sku(task.sku)

        # Lising record is present in spree_response
        listing_inventory = task.listing.listing_inventories.find_by(variant_id: variant_id)
        return if listing_inventory.blank?
        variant = Spree::Variant.find_by(id: variant_id)

        # task.content like: {"sku"=>"HAC300", "shipNodes"=>[{"quantity"=>{"unit"=>"EACH", "amount"=>7}, "shipNode"=>"10002537369"}]}
        quantity = task.content.dig("shipNodes", 0, "quantity", "amount")
        if quantity.present?
          listing_inventory.update(total_listed_qty: listing_inventory.total_sold_qty + quantity)
        end
      end
    end
  end
end
