module SpreeInsertion
  module Walmart
    class HistoricalOrder
      attr_accessor :order_data, :store, :import_log, :sale_channel_id

      def initialize(order_data, store, import_log, sale_channel_id)
        @order_data = order_data
        @store = store
        @import_log = import_log
        @sale_channel_id = sale_channel_id
      end

      def call
        ActiveRecord::Base.transaction do
          historical_order = Spree::HistoricalOrder.create!(
            number: @order_data["purchaseOrderId"],
            state: order_state,
            total: get_total_amount,
            item_total: get_item_total_price,
            payment_total: get_total_amount,
            currency: 'USD',
            store_id: store.id,
            user_id: nil,
            email: extract_email,
            channel: "walmart",
            payment_state: get_payment_state,
            shipment_state: get_order_shipment_state,
            order_date: Time.at(@order_data["orderDate"] / 1000),
            last_modified_date: Time.now,
            additional_data: nil, # Initially nil
            sale_channel_metadata: @order_data # Store full response
          )

          # Associate line items separately
          historical_order.update!(
            line_items: extract_line_items,
            shipment_details: extract_shipment_details,
            payment_history: extract_payment_history,
            fees: extract_fees,
            buyer_info: extract_buyer_info
          )

          Rails.logger.info "@@@@@@@@@Stored Walmart order ##{historical_order.number} successfully."
          historical_order
        end
      rescue => e
        Rails.logger.error "@@@@@@@@@Failed to store Walmart order: #{e.message} order_number: #{@order_data["purchaseOrderId"]}"
        nil
      end

      private

      def order_state
        return 'canceled' if order_data.dig("orderLines", "orderLine").first.dig("orderLineStatuses", "orderLineStatus").first.dig("status") == 'Cancelled'

        'complete'
      end

      def get_payment_state
        get_order_shipment_state == 'canceled' ? 'void' : 'paid'
      end

      def get_order_shipment_state
        order_lines = order_data.dig('orderLines', 'orderLine')

        all_shipped = order_lines.all? do |order_line|
          statuses = order_line.dig('orderLineStatuses', 'orderLineStatus')
          statuses.all? { |status| status['status'] == 'Shipped' || status['status'] == 'Delivered' }
        end

        any_shipped = order_lines.any? do |order_line|
          statuses = order_line.dig('orderLineStatuses', 'orderLineStatus')
          statuses.any? { |status| status['status'] == 'Shipped' || status['status'] == 'Delivered' }
        end

        any_cancelled = order_lines.any? do |order_line|
          statuses = order_line.dig('orderLineStatuses', 'orderLineStatus')
          statuses.all? { |status| status['status'] == 'Cancelled' }
        end

        if all_shipped
          'shipped'
        elsif any_shipped
          'partial'
        elsif any_cancelled
          'canceled'
        else
          'ready'
        end
      end

      def get_total_amount
        order_data.dig('orderLines', 'orderLine').sum do |order_line|
          order_line.dig('charges', 'charge').sum do |charge|
            if charge['chargeType'] == 'PRODUCT'
              # Add the charge amount and the tax amount
              quantities_ordered = order_line.dig(:orderLineQuantity, 'amount')&.to_i
              charge_amount = charge.dig('chargeAmount', 'amount') || 0
              tax_field = charge.dig('tax', 'taxAmount', 'amount') || 0
              charge_amount.to_f + tax_field.to_f
            else
              0
            end
          end
        end
      end

      def get_item_total_price
        order_data.dig('orderLines', 'orderLine').sum do |order_line|
          order_line.dig('charges', 'charge').sum do |charge|
            if charge['chargeType'] == 'PRODUCT'
              # Add the charge amount and the tax amount
              quantities_ordered = order_line.dig(:orderLineQuantity, 'amount')&.to_i
              charge_amount = charge.dig('chargeAmount', 'amount')&.to_f
            else
              0
            end
          end
        end
      end

      def extract_email
        @order_data.dig("customerEmailId") || "<EMAIL>"
      end

      def extract_charge(charge_name)
        charges = @order_data.dig("orderLines", "orderLine")&.first&.dig("charges", "charge")
        return 0.0 unless charges

        item_charge = charges.find { |c| c["chargeName"] == charge_name }
        item_charge ? item_charge.dig("chargeAmount", "amount").to_f : 0.0
      end

      def extract_charge_currency(charge_name)
        charges = @order_data.dig("orderLines", "orderLine")&.first&.dig("charges", "charge")
        return "USD" unless charges

        item_charge = charges.find { |c| c["chargeName"] == charge_name }
        item_charge ? item_charge.dig("chargeAmount", "currency") : "USD"
      end

      def extract_line_items
        order_lines = @order_data.dig("orderLines", "orderLine") || []

        order_lines.map do |line|
          {
            sku: line.dig("item", "sku"),
            product_name: line.dig("item", "productName"),
            quantity: line.dig("orderLineQuantity", "amount").to_i,
            price: extract_charge("ItemPrice"),
            status: line.dig("orderLineStatuses", "orderLineStatus")&.first&.dig("status"),
            fulfillment: line.dig("fulfillment")
          }
        end
      end

      def extract_shipment_details
        shipping_info = @order_data["shippingInfo"]
        return {} unless shipping_info

        {
          phone: shipping_info["phone"],
          method_code: shipping_info["methodCode"],
          estimated_ship_date: Time.at(shipping_info["estimatedShipDate"] / 1000),
          estimated_delivery_date: Time.at(shipping_info["estimatedDeliveryDate"] / 1000),
          address: {
            name: shipping_info.dig("postalAddress", "name"),
            address1: shipping_info.dig("postalAddress", "address1"),
            address2: shipping_info.dig("postalAddress", "address2"),
            city: shipping_info.dig("postalAddress", "city"),
            state: shipping_info.dig("postalAddress", "state"),
            country: shipping_info.dig("postalAddress", "country"),
            postal_code: shipping_info.dig("postalAddress", "postalCode")
          }
        }
      end

      def extract_payment_history
        {
          payment_status: get_payment_state,
          total_paid: get_total_amount,
          payment_method: "Walmart"
        }
      end

      def extract_fees
        {
          sales_tax: sales_tax_or_walmart_fee || 0,
          walmart_fee: sales_tax_or_walmart_fee || 0,
          processing_fee: extract_charge("ProcessingFee") || 0,
          total_fees: get_total_amount || 0
        }
      end

      def sales_tax_or_walmart_fee
        total_tax_amount = order_data.dig("orderLines", "orderLine")&.sum do |order_line|
          order_line.dig("charges", "charge")&.sum { |charge| charge.dig("tax", "taxAmount", "amount").to_f } || 0
        end       
      end


      def extract_buyer_info
        buyer = @order_data.dig("shippingInfo") || {}
        {
          name: buyer.dig("postalAddress", "name"),
          email: extract_email,
          phone: buyer["phone"],
          address: extract_shipment_details[:address]
        }
      end
    end
  end
end
