module SpreeInsertion
  module Walmart
    class Listing < SpreeInsertion::Base
      include ::WalmartHelper

      attr_accessor :sale_channel_id

      def initialize(response, store_id, sale_channel_id)
        @spree_response = response
        @store = Spree::Store.find_by(id: store_id)
        @sale_channel_id = sale_channel_id
      end

      def execute(seller_data)
        sku_hash = {}
        variant_group_id = seller_data[:variantGroupId]

        if seller_data[:Variations]
          seller_data[:Variations].each do |variant|
            sku_hash[variant[:sku]] = {
              wpid: variant[:wpid],
              variant_group_id: variant[:variantGroupId],
            }
          end
        else
          sku_hash[seller_data[:sku]] = {
            wpid: seller_data[:wpid],
            variant_group_id: seller_data[:variantGroupId],
          }
        end

        spree_response.each do |list|
          listing = get_walmart_listing(sale_channel_id, sku_hash.keys, variant_group_id, list[:item_id])
          if listing
            list[:sale_channel_hash] = merge_sale_channel_hash(listing, list[:sale_channel_hash])
            listing.update(list)
            update_fileds_from_walmart_report(listing, sku_hash.keys)
          else
            listing = Spree::Listing.create!(list)
            # listing.create_walmart_listing_skus(sku_hash.keys, sku_hash) if list[:status] == "Active"
            update_fileds_from_walmart_report(listing, sku_hash.keys)
            create_listing_inventory(listing, seller_data)
          end
        end
      end

      def update_fileds_from_walmart_report(listing, skus = [])
        item_images = { PictureURL: [] }
        return if skus.blank? || listing.blank?

        listing.oauth_application.walmart_report_contents.where(sku: skus).each do |report_content|
          item_images[:PictureURL] << report_content.primary_image_url if report_content.primary_image_url.present?
          if listing.walmart_primary_sku == report_content.sku || listing.walmart_primary_sku.blank?
            listing.item_url = report_content.item_url
          end
          weight = report_content.shipping_weight
          listing.update_walmart_shipping_weight(report_content.sku, weight.to_f) if weight.present?
        end
        update_listing_images(listing, item_images) if item_images[:PictureURL].present?
        listing.save!
      end

      def listing_walmart_images(listing, skus = [])
        item_images = { PictureURL: [] }
        listing.oauth_application.walmart_report_contents.where(sku: skus).each do |report_content|
          item_images[:PictureURL] << report_content.primary_image_url if report_content.primary_image_url.present?
        end
        item_images
      end

      def update_listing_images(listing, img_urls)
        images = img_urls[:PictureURL]&.kind_of?(Array) ? img_urls[:PictureURL] : [img_urls[:PictureURL]]
        if images.any?
          images.each do |img|
            begin
              ActiveRecord::Base.transaction do
                image = URI.parse(img)
                image_name = File.basename(image.path)
                option_text = "master"
                filename = "#{option_text}_#{listing.product&.master&.id}_#{image_name}"
                unless listing.image_files_blobs.where(filename: filename).any?
                  image_file = image.open
                  listing.image_files.attach(io: image_file, filename: filename)
                end
              end
            rescue StandardError => e
              next
            end
          end
        end
      end

      private

      def merge_sale_channel_hash(listing, new_hash_in)
        original_hash_in = listing.sale_channel_hash
        original_hash = JSON.parse(original_hash_in.to_h.to_json)
        new_hash = JSON.parse(new_hash_in.to_h.to_json)
        return new_hash if original_hash.blank?
        return original_hash if new_hash.blank?
        excluded_keys = ["variants"]
        original_hash_without_variants = original_hash.reject { |k, v| excluded_keys.include?(k) }
        new_hash_without_variants = new_hash.reject { |k, v| excluded_keys.include?(k) }
        ret = original_hash_without_variants.merge(new_hash_without_variants)
        new_variants = new_hash.dig("variants").to_h
        new_variants = original_hash.dig("variants").merge(new_variants) if original_hash.dig("variants")
        original_hash.dig("variants").each_key do |variant_id|
          if original_hash.dig("variants", variant_id, "item_content", "Orderable", "ShippingWeight").present?
            if new_variants.dig(variant_id, "item_content", "Orderable").present?
              new_variants[variant_id]["item_content"]["Orderable"]["ShippingWeight"] =
                original_hash.dig("variants", variant_id, "item_content", "Orderable", "ShippingWeight")
            end
          end
        end

        new_sale_channel_hash = {}
        if listing.is_create_new_walmart_item_method?
          new_sale_channel_hash = listing.walmart_merge_item_fields_in_spec(original_hash_in, { "variants" => new_variants })
          new_sale_channel_hash = JSON.parse(new_sale_channel_hash.to_h.to_json)
          new_variants = new_sale_channel_hash.dig("variants")
          response_from_apis = new_sale_channel_hash.dig("response_from_apis")
        end

        ret["variants"] = new_variants.present? ? new_variants : {}

        ret
      end

      def create_listing_inventory(listing, seller_data)
        product = listing.product
        if product.variants.present? && seller_data[:Variations]
          variant_array = seller_data[:Variations]
          # variant_array = variants[:Variation].is_a?(Array) ? variants[:Variation] : [variants[:Variation]]
          variant_array.each_with_index do |variant, _index|
            #  "variantGroupId"=>"VP#00705928045309", "variantGroupInfo"=>{"isPrimary"=>false, "groupingAttributes"=>[{"name"=>"multipack_quantity", "value"=>"100"}]}
            existing_variant = find_variant(product, variant.dig(:variantGroupInfo, :groupingAttributes))
            sync_listing_sold_qty(listing, existing_variant, variant.dig(:inventory, :availToSellQty).to_i, variant.dig(:price, :amount))
          end
        else
          sync_listing_sold_qty(listing, product.master, seller_data.dig(:inventory, :availToSellQty).to_i, seller_data.dig(:price, :amount))
        end
      end

      def find_variant(product, option_type_value_array)
        return nil if option_type_value_array.blank?

        ebay_option_value = []

        option_type_value_array.each do |name_value_list|
          next if name_value_list.nil?
          next if name_value_list[:name].nil? || name_value_list[:value].nil?

          option_type_id = Spree::OptionType.find_by(name: name_value_list.dig(:name))
          option_value_id = Spree::OptionValue.find_by(name: name_value_list.dig(:value), option_type_id: option_type_id)&.id

          ebay_option_value += [option_value_id] if option_value_id.present?
        end

        product.variants.each do |variant|
          return variant if variant.option_values && (variant.option_values&.to_a&.pluck(:id) == ebay_option_value)
        end
        nil
      end

      def sync_listing_sold_qty(listing, variant, quantity, price)
        return unless listing.present?
        return if variant.blank?

        listing_inventory = listing.listing_inventories.find_or_create_by(variant_id: variant.id)
        low_availability = listing.walmart_item_low_availability_toggle_by_variant_id(variant.id)
        update_data = {
          total_listed_qty: quantity,
          total_sold_qty: 0,
          price: price,
          low_availability_toggle: low_availability.dig("low_availability_toggle") || false,
          low_availability_value: low_availability.dig("low_availability_value") || 0,
        }
        listing_inventory.update(update_data) if listing_inventory.present?
      end
    end
  end
end
