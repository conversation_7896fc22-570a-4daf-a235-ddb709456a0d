module SpreeInsertion
  module Exception
    def update_successful_import_log_counter(log)
      log.increment!(:success_row_count)
    end

    def update_failed_import_log_counter(log)
      log.increment!(:error_row_count)
    end

    def write_import_logs(log, error, product)
      data = error_details(error, product)
      log.error_details.merge!(data)
      log.save!
    end

    def write_walmart_import_logs(log, error, product)
      data = error_details(error, product)
      log.error_details.merge!(data)
      log.save!
    end

    def write_ebay_import_logs(log, error, product)
      data = error_details(error, product)
      log.error_details.merge!(data)
      log.save!
    end

    def write_amazon_import_logs(log, error, product)
      data = error_details(error, product)
      log.error_details.merge!(data)
      log.save!
    end

    private

    def error_details(e, product)
      { "#{product}": { product_name: product, message: e } }
    end
  end
end
