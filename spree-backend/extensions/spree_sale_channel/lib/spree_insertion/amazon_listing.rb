module SpreeInsertion
  class AmazonListing < SpreeInsertion::Base
    def execute
      amazon_listings = Spree::Listing.for_brand('amazon')
      listing = amazon_listings.find_by(sku: spree_response[:sku])
      if spree_response[:item_id] && listing.blank?
        listing = amazon_listings.find_by(item_id: spree_response[:item_id])
      end

      if listing
        listing.update(spree_response.except('images'))
        update_listing_images(listing, spree_response['images']) if listing.image_files.empty?
        product = listing&.product
        listing.update(status: 'Unlinked') unless product.present?
      else
        listing = Spree::Listing.create!(spree_response.except('images'))
        product = listing&.product
        if product.present?
          update_listing_images(listing, spree_response['images']) if listing.present?
          create_listing_inventory(listing, spree_response)
        else
          listing.update(status: 'Unlinked')
        end
      end

      listing
    end

    private

    def create_listing_inventory(listing, spree_response)
      product = listing&.product
      # if product.variants.present? && spree_response[:Variations]
      #   variants = spree_response[:Variations]
      #   variant_array = variants[:Variation].is_a?(Array) ? variants[:Variation] : [variants[:Variation]]
      #   variant_array.each_with_index do |variant, _index|
      #     existing_variant = find_variant(product, variant[:VariationSpecifics])
      #     sync_listing_sold_qty(listing, existing_variant, variant.dig(:SellingStatus, :QuantitySold), variant[:StartPrice], variant[:Quantity])
      #   end
      # else
        sync_listing_sold_qty(listing, product&.master, 0, spree_response.dig(:sale_channel_hash, 'price'), spree_response[:quantity])
      # end
    end

    def update_listing_images(listing, images_hash)
      return if images_hash.nil?
      image_locators = [
        "main_product_image_locator",
        "other_product_image_locator_1",
        "other_product_image_locator_2",
        "other_product_image_locator_3",
        "other_product_image_locator_4",
        "other_product_image_locator_5",
        "other_product_image_locator_6",
        "other_product_image_locator_7",
        "other_product_image_locator_8"
      ]

      image_present = image_locators.any? { |field| images_hash[field] && !images_hash[field].empty? }

      if image_present
        image_locators.each do |field|
          image_url = images_hash[field]
          next if image_url.nil? || image_url.empty?
          begin
            ActiveRecord::Base.transaction do
              image = URI.parse(image_url)
              image_file = image.open
              image_name = File.basename(image.path)
              option_text = 'master'
              filename = "#{option_text}_#{listing&.product&.master&.id}_#{image_name}"
              listing.image_files.attach(io: image_file, filename: filename)
            end
          rescue StandardError => e
            next
          end
        end
      end
    end

    def sync_listing_sold_qty(listing, variant, sold_qty, price, quantity)
      return if listing.blank?

      return if variant.blank?

      listing_inventory = listing.listing_inventories.find_or_create_by(variant_id: variant.id)
      total_available_qty = quantity.to_i
      listing_inventory.update(total_listed_qty: total_available_qty, total_sold_qty: sold_qty.to_i, price: price.to_f, total_available_qty: total_available_qty) if listing_inventory.present?
    end
  end
end
