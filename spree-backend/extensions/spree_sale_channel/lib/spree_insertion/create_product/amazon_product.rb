require 'stringex'

class ProductNotCreatedError < ::StandardError
end

module SpreeInsertion
  module CreateProduct
    class AmazonProduct < SpreeInsertion::Base
      attr_accessor :seller_data, :product, :store, :import_log, :need_stock_update, :listing

      def initialize(seller_data, store, import_log)
        @store = store
        @seller_data = seller_data
        @import_log = import_log
      end

      def make
        begin
          is_new_product = false
          item_id = seller_data[:item_id]
          sku = seller_data[:sku]

          return nil if sku.blank?
          return nil if item_id.blank?
          return nil unless store.default_currency == seller_data[:currency]

          amazon_listings = Spree::Listing.for_brand('amazon')
          @listing = amazon_listings.find_by(sku: sku)
          if @listing.blank?            
            @listing = amazon_listings.find_by(item_id: item_id)
          end

          @product = listing&.product || check_product_already_exists

          unless @product.present?
            # not create new product anymore when not found product.
            # Link unlinked listing with products manually later(AM-2843)
            listing.update(status: 'Unlinked') if listing.present?
            return nil

            @product = Spree::Product.new(
              name: seller_data[:title],
              price: price,
              description: description,
              lbs: lbs,
              oz: oz,
              available_on: Date.today.beginning_of_day
            )
            product.stores << store
            is_new_product = true
            product.sku = sku unless Spree::Variant.where(deleted_at: nil).where("sku ILIKE '#{sku}'").exists?
            product&.master&.currency = seller_data[:currency]
            product&.master&.cost_currency = seller_data[:currency]
          end

          product&.set_slug
          ret = product.save
          unless ret
            raise ProductNotCreatedError
          end

          # if seller_data[:Variations]
          #   save_variants(seller_data[:Variations])
          # else
          #   sync_listing_sold_qty(product.master,
          #                         seller_data.dig(:SellingStatus, :QuantitySold), nil, seller_data[:quantity])
          # end

          if listing.present?
            variant = Spree::Variant.where(deleted_at: nil).where("sku ILIKE '#{sku}'").take
            sync_listing_sold_qty(variant, price, seller_data[:quantity])
          end

          save_product_img(seller_data['images']) if is_new_product
          #save_taxons(seller_data[:PrimaryCategory]) if seller_data[:PrimaryCategory]
        rescue ProductNotCreatedError => e
          update_failed_import_log_counter(import_log)
          write_amazon_import_logs(import_log, e, 'Amazon Product/Listing')
        rescue StandardError => e
          # Increment import log error counter
          update_successful_import_log_counter(import_log)
          # update_failed_import_log_counter(import_log)
          write_amazon_import_logs(import_log, "StandardError, #{e}", 'Amazon Product/Listing')
        else
          # Increment import log success counter
          update_successful_import_log_counter(import_log)
        end
        product
      end

      # For Saving taxon in products table from Ebay
      # def save_taxons(taxon)
      #   taxon_id = Spree::Taxon.find_by(category_id: taxon[:CategoryID])&.id if taxon[:CategoryID]
      #   product.taxon_ids = taxon_id if taxon_id
      # end

      # def save_variants(variants)
      #   lbs = seller_data.dig(:ShippingDetails, :CalculatedShippingRate, :WeightMajor).to_f
      #   oz = seller_data.dig(:ShippingDetails, :CalculatedShippingRate, :WeightMinor).to_f
      #   variant_array = variants[:Variation].is_a?(Array) ? variants[:Variation] : [variants[:Variation]]
      #   variant_array.each_with_index do |variant, _index|
      #     if (existing_variant = find_variant(variant[:VariationSpecifics]))
      #       existing_variant.update(price: variant[:StartPrice], cost_currency: seller_data[:Currency], currency: seller_data[:Currency],
      #                               lbs: lbs, oz: oz)
      #       sync_listing_sold_qty(existing_variant, variant.dig(:SellingStatus, :QuantitySold), variant[:StartPrice], variant[:Quantity])
      #     else
      #       product_variant = product.variants.new(
      #         price: variant[:StartPrice],
      #         # sku: variant[:SKU] || "",
      #         cost_currency: seller_data[:Currency],
      #         currency: seller_data[:Currency],
      #         lbs: lbs,
      #         oz: oz,
      #         track_inventory: true
      #       )
      #       # @need_stock_update = true
      #       option_value = create_option_value(variant[:VariationSpecifics])
      #       product_variant.option_values = option_value
      #       if product_variant.save!
      #         sync_listing_sold_qty(product_variant, variant.dig(:SellingStatus, :QuantitySold), variant[:StartPrice],
      #                               variant[:Quantity])
      #       end
      #     end
      #   end
      # end

      # def find_variant(variation_specifics)
      #   return nil if product.variants.blank?

      #   ebay_option_value = []

      #   option_type_value_array = variation_specifics[:NameValueList].is_a?(Array) ? variation_specifics[:NameValueList] : [variation_specifics[:NameValueList]]
      #   option_type_value_array.each do |name_value_list|
      #     option_type_id = Spree::OptionType.find_by(name: name_value_list[:Name])
      #     option_value_id = Spree::OptionValue.find_by(name: name_value_list[:Value], option_type_id: option_type_id)&.id

      #     ebay_option_value += [option_value_id] if option_value_id.present?
      #   end

      #   product.variants.each do |variant|
      #     return variant if variant.option_values && (variant.option_values&.to_a&.pluck(:id) == ebay_option_value)
      #   end
      #   nil
      # end

      def save_product_img(images_hash)
        image_locators = [
          "main_product_image_locator",
          "other_product_image_locator_1",
          "other_product_image_locator_2",
          "other_product_image_locator_3",
          "other_product_image_locator_4",
          "other_product_image_locator_5",
          "other_product_image_locator_6",
          "other_product_image_locator_7",
          "other_product_image_locator_8"
        ]

        image_present = image_locators.any? { |field| images_hash[field] && !images_hash[field].empty? }

        if image_present
          image_locators.each do |field|
            image_url = images_hash[field]
            next if image_url.nil? || image_url.empty?
            begin
              ActiveRecord::Base.transaction do
                image = URI.parse(image_url)
                image_file = image.open
                image_name = File.basename(image.path)
                option_text = 'master'
                filename = "#{option_text}_#{product&.master&.id}_#{image_name}"
                product.images.create!(attachment: { io: image_file, filename: filename })
              end
            rescue StandardError => e
              next
            end
          end
        end
      end

      # def create_option_value(variation_specific)
      #   option_value = []
      #   return unless variation_specific

      #   option_type_array = variation_specific[:NameValueList].is_a?(Array) ? variation_specific[:NameValueList] : [variation_specific[:NameValueList]]
      #   option_type_array.each do |option_specific|
      #     option_type = store.option_types.find_or_create_by!(name: option_specific[:Name], presentation: option_specific[:Name]&.capitalize)
      #     product.option_types << option_type unless product.option_types.include?(option_type)
      #     option_value += [option_type.option_values.find_or_create_by!(name: option_specific[:Value],
      #                                                                   presentation: option_specific[:Value]&.capitalize)]
      #   end
      #   option_value
      # end

      def description
        seller_data[:description]
      end

      def lbs
        seller_data[:sale_channel_hash]['lb']
      end

      def oz
        seller_data[:sale_channel_hash]['oz']
      end

      def price
        seller_data[:sale_channel_hash]['price']
      end

      def available_on
        seller_data[:ListingDetails][:StartTime] if seller_data[:ListingDetails]
      end

      def check_product_already_exists
        sku = seller_data[:sku]
        variant = Spree::Variant.where(deleted_at: nil).where("sku ILIKE '#{sku}'").take
        product = variant&.product
        if product.blank? && seller_data[:title]
          product = Spree::Product.find_by(name: seller_data[:title])
        end
        product
      end

      def sync_listing_sold_qty(variant, variation_specific_price = nil, quantity)
        variant_price = variation_specific_price || price

        return if listing.blank?

        return if variant.blank?

        listing_inventory = listing.listing_inventories.find_or_create_by(variant_id: variant.id)
        total_available_qty = quantity.to_i
        return if total_available_qty < 0
        listing_inventory.update(total_available_qty: total_available_qty, price: variant_price) if listing_inventory.present?
      end
    end
  end
end
