class ProductNotCreatedError < ::StandardError
end

module SpreeInsertion
  module CreateProduct
    class Product < SpreeInsertion::Base
      attr_accessor :seller_data, :product, :store, :import_log, :need_stock_update, :listing

      def initialize(seller_data, store, import_log)
        @store = store
        @seller_data = seller_data
        @import_log = import_log
      end

      def make
        begin
          is_new_product = false
          return nil unless store.default_currency == seller_data[:Currency]

          @listing = Spree::Listing.for_brand('eBay').find_by(item_id: seller_data[:ItemID]) if seller_data[:ItemID]
          lbs = seller_data.dig(:ShippingDetails, :CalculatedShippingRate, :WeightMajor).to_f
          oz = seller_data.dig(:ShippingDetails, :CalculatedShippingRate, :WeightMinor).to_f
          @product = listing&.product || check_product_already_exists

          unless @product.present?
            @product = Spree::Product.new(name: seller_data[:Title], price: price, description: description,
                                          lbs: lbs, oz: oz, available_on: Date.today.beginning_of_day)
            product.stores << store
            is_new_product = true
            # @need_stock_update = true
            product.sku = seller_data[:SKU] if seller_data[:SKU]
            product.master.currency = seller_data[:Currency]
            product.master.cost_currency = seller_data[:Currency]
          end

          raise ProductNotCreatedError unless product.save
          if seller_data[:Variations]
            save_variants(seller_data[:Variations])
          else
            sync_listing_sold_qty(product.master,
                                  seller_data.dig(:SellingStatus, :QuantitySold), nil, seller_data[:Quantity])
          end
          save_product_img(seller_data[:PictureDetails]) if seller_data[:PictureDetails] && is_new_product
        # save_taxons(seller_data[:PrimaryCategory]) if seller_data[:PrimaryCategory]
        rescue ProductNotCreatedError => e
          update_failed_import_log_counter(import_log)
          write_ebay_import_logs(import_log, e, 'eBay Product/Listing')
        rescue StandardError => e
          # Increment import log error counter
          update_successful_import_log_counter(import_log)
          # update_failed_import_log_counter(import_log)
          write_ebay_import_logs(import_log, "StandardError, #{e}", 'eBay Product/Listing')
        else
          # Increment import log success counter
          update_successful_import_log_counter(import_log)
        end
        product
      end

      def find_city
        seller_data.dig(:Location) || 'New York'
      end

      def find_state
        location = seller_data[:Location]
        state_name = location&.split(',')&.last if location
        if state_name
          state = Spree::State.find_by(name: state_name)
          state ||= find_country&.states&.first
        else
          state = find_country&.states&.first
        end
        state || Spree::State.first
      end

      def find_country
        Spree::Country.find_by(iso: seller_data[:Country])
      end

      # For Saving taxon in products table from Ebay
      # def save_taxons(taxon)
      #   taxon_id = Spree::Taxon.find_by(category_id: taxon[:CategoryID])&.id if taxon[:CategoryID]
      #   product.taxon_ids = taxon_id if taxon_id
      # end

      def save_variants(variants)
        lbs = seller_data.dig(:ShippingDetails, :CalculatedShippingRate, :WeightMajor).to_f
        oz = seller_data.dig(:ShippingDetails, :CalculatedShippingRate, :WeightMinor).to_f
        variant_array = variants[:Variation].is_a?(Array) ? variants[:Variation] : [variants[:Variation]]
        variant_array.each_with_index do |variant, _index|
          if existing_variant = find_variant(variant[:VariationSpecifics])
            existing_variant.update(price: variant[:StartPrice], cost_currency: seller_data[:Currency], currency: seller_data[:Currency],
            lbs: lbs, oz: oz)
            sync_listing_sold_qty(existing_variant, variant.dig(:SellingStatus, :QuantitySold), variant[:StartPrice], variant[:Quantity])
          else
            product_variant = product.variants.new(
              price: variant[:StartPrice],
              # sku: variant[:SKU] || "",
              cost_currency: seller_data[:Currency],
              currency: seller_data[:Currency],
              lbs: lbs,
              oz: oz,
              track_inventory: true
            )
            # @need_stock_update = true
            option_value = create_option_value(variant[:VariationSpecifics])
            product_variant.option_values = option_value
            sync_listing_sold_qty(product_variant, variant.dig(:SellingStatus, :QuantitySold), variant[:StartPrice], variant[:Quantity]) if product_variant.save!
          end
        end
      end

      def find_variant(variation_specifics) # [1,2]
        return nil unless product.variants.present?

        ebay_option_value = []

        option_type_value_array = variation_specifics.dig(:NameValueList).is_a?(Array) ? variation_specifics.dig(:NameValueList) : [variation_specifics.dig(:NameValueList)]
        option_type_value_array.each do |name_value_list|
          option_type_id = Spree::OptionType.find_by(name: name_value_list.dig(:Name))
          option_value_id = Spree::OptionValue.find_by(name: name_value_list.dig(:Value), option_type_id: option_type_id)&.id

          ebay_option_value += [option_value_id] if option_value_id.present?
        end

        product.variants.each do |variant|
          return variant if variant.option_values && (variant.option_values&.to_a&.pluck(:id) == ebay_option_value)
        end
        nil
      end

      def save_product_img(img_urls)
        images = img_urls[:PictureURL]&.kind_of?(Array) ? img_urls[:PictureURL] : [img_urls[:PictureURL]]
        return false unless images.any?

        images.each do |img|
          begin
            ActiveRecord::Base.transaction do
              image = URI.parse(img)
              image_file = image.open
              image_name = File.basename(image.path)
              option_text = "master"
              filename = "#{option_text}_#{product.master&.id}_#{image_name}"
              product.images.create!(attachment: { io: image_file, filename: filename })
            end
          rescue StandardError => e
            next
          end
        end
      end

      def create_option_value(variation_specific)
        option_value = []
        return unless variation_specific

        option_type_array = variation_specific[:NameValueList].is_a?(Array) ? variation_specific[:NameValueList] : [variation_specific[:NameValueList]]
        option_type_array.each do |option_specific|
          option_type = store.option_types.find_or_create_by!(name: option_specific[:Name], presentation: option_specific[:Name]&.capitalize)
          product.option_types << option_type unless product.option_types.include?(option_type)
          option_val = option_type.option_values.find_or_initialize_by(name: option_specific[:Value])
          option_val.update(presentation: option_specific[:Value].capitalize)
          option_value += [option_val]
        end
        option_value
      end

      def description
        seller_data[:Description]
      end

      def price
        seller_data[:SellingStatus][:CurrentPrice] if seller_data[:SellingStatus]
      end

      def available_on
        seller_data[:ListingDetails][:StartTime] if seller_data[:ListingDetails]
      end

      def discontinue_on
        seller_data[:ListingDetails][:EndTime] if seller_data[:ListingDetails]
      end

      def check_product_already_exists
        Spree::Product.find_by(name: seller_data[:Title])
      end

      def sync_listing_sold_qty(variant, sold_qty, variation_specific_price = nil, quantity)
        variant_price = variation_specific_price ? variation_specific_price : price

        return unless listing.present?
        listing_inventory = listing.listing_inventories.find_or_create_by(variant_id: variant.id)
        listed_quantity = quantity.to_i >= sold_qty.to_i ? quantity.to_i : quantity.to_i + sold_qty.to_i
        listing_inventory.update(total_listed_qty: listed_quantity, total_sold_qty: sold_qty, price: variant_price) if listing_inventory.present?
      end
    end
  end
end
