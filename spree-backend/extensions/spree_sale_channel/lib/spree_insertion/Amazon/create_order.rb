module SpreeInsertion
    module Amazon
      class CreateOrder < SpreeInsertion::Base
        include ::Spree::Admin::SubscribersHelper
        include ::Spree::Admin::LineItemsExtHelper

        attr_accessor :order_data, :store, :import_log, :order, :address, :product, :listing, :oauth_application, :order_line_items

        def initialize(order_data, store, oauth_application, import_log)
          @store = store
          @order_data = order_data
          @import_log = import_log
          @oauth_application = oauth_application
        end

        def make
          unless order_data.dig(:OrderStatus) == "Pending" || order_data.dig(:OrderStatus) == "PendingAvailability" ||  order_data.dig(:OrderStatus) == "Unfulfillable"
            begin
              @order = Spree::Order.find_or_create_by(number: order_data[:AmazonOrderId])

              order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : amazon Order (#{order_data[:AmazonOrderId]}) has been successfully synced")

              order_email = order_data.dig(:BuyerInfo, :BuyerEmail) || "<EMAIL>"

              previous_state = order.state
              order.update!(email: order_email, payment_state: get_payment_state , shipment_state: get_shipment_state, last_modified_date: order_data.dig(:LastUpdateDate),
                            completed_at: order_data.dig(:PurchaseDate), total: order_data.dig(:OrderTotal, :Amount) || 0.0,
                            currency: order_data.dig(:OrderTotal, :CurrencyCode), sale_channel_id: oauth_application.sale_channel_id,
                            state: order_state, sale_channel_metadata: order_data, channel: "amazon")
              create_and_assign_address if order_state != "canceled"
              create_and_assign_line_items if order_state != "canceled"
              create_and_assign_payments if order_state != "canceled"

              update_and_assign_shipments if order_state != "canceled"

              # create subscriber for email campaign
              find_or_create_subscriber

              order.update_columns(payment_total: order.payments.completed.sum(:amount)) if order.payment_total.to_f <= 0

              check_line_items_ext if previous_state != 'complete' && order_state == 'complete'
            rescue => e
              update_failed_import_log_counter(import_log)
              write_import_logs(import_log, e, order.number)
            else
              update_successful_import_log_counter(import_log)
            end
          end
        end

        def find_or_create_subscriber
          subscriber = Spree::Subscriber.find_or_create_by(email: order.email)
          process_subscriber_action(subscriber, 'placed order', order) if subscriber
        end

        def check_line_items_ext
          if order.line_items.present?
            order.line_items.each do |item|
              line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
              process_line_items_ext(line_items_ext, item, order) if line_items_ext
            end
          end
        end

        def update_and_assign_shipments
          order.shipments.each do |shipment|
            # Depricated - In future we will not bind listing with stock locations and rather handle it during shipment process
            shipment.update_columns(state: get_shipment_state, stock_location_id: get_stock_location, order_id: order.id)
            shipment.update_columns(address_id: address&.id) if address

            order.reload.update!(shipment_state: get_shipment_state)
            # Confirm stock_item_units should be in shipped state once shipment is shipped.
            if shipment.shipped?
              unless order_package = shipment.order_package
                order_package = order.order_packages.first_or_create
              end
              order_package.update_columns(state: 'shipped', tracking_label: shipment.tracking_label)
              # order_package.update_columns(selected_shipping_rate_id: shipment.selected_shipping_rate&.id) if shipment.shipping_rates
              shipment.update_columns(order_package_id: order_package.id)

              shipment.stock_item_units.update(state: 'shipped')
              shipment.inventory_units.update(state: 'shipped')
            end
          end
        end

        def append_free_shipping_method(shipment, line_item)
          amazon_shipment_value = line_item&.dig(:ShippingPrice, :Amount)&.to_f || 0
          # amazon_shipment_value = 0
          # shipping_category_name = "Free Shipping" if amazon_shipment_value != nil && amazon_shipment_value&.to_f == 0
          shipping_category_name = order_state != 'shipped' ? 'Free Shipping' : 'Amazon Shipping'
          if amazon_shipment_value.present? && amazon_shipment_value > 0
            shipping_category_name = 'Amazon Shipping'
          end
          # shipping_category_name = shipment.shipping_rates.first&.shipping_method&.shipping_categories&.first&.name
          # shipping_method = shipping_category_name.present? ? shipment.shipping_rates.first&.shipping_method : create_shipping_method(shipment)
          shipping_method = create_shipping_method(shipment, shipping_category_name)
          if shipping_category_name
            shipment.shipping_methods << shipping_method unless shipment.shipping_methods.include?(shipping_method)
            shipment.shipping_rates.update_all(selected: false)
            shipment.reload
            free_shipping_rate = shipment.shipping_rates.where(shipping_method_id: shipping_method.id)
            free_shipping_rate.update(selected: true, buyer_selected: true)
            shipment.reload
            # shipment.update_columns(cost: 0.0, buyer_paid_amount: amazon_shipment_value)
            shipment.update_columns(cost: amazon_shipment_value, buyer_paid_amount: amazon_shipment_value)
          end
        end

        def create_shipping_method(shipment, shipping_category_name = nil)
          shipping_category = if shipping_category_name.present?
                                Spree::ShippingCategory.find_or_create_by(name: shipping_category_name,
                                                                            store_id: store.id)
                              else
                                Spree::ShippingCategory.first
                              end
          attributes =
            {
              name: 'Amazon_Shipping' || 'Amazon_Shipping',
              display_on: 'both',
              shipping_categories: [shipping_category],
              store_id: store.id
            }

          shipping_method = Spree::ShippingMethod.where(name: attributes[:name], store_id: attributes[:store_id]).first_or_create! do |method|
            method.calculator = Spree::Calculator::Shipping::FlatRate.first_or_create! if method.calculator == nil
            method.display_on = attributes[:display_on]
            method.shipping_categories = attributes[:shipping_categories]
          end
          shipping_method
        end

        def get_stock_location

          line_item_data = order_line_items.first
          variant_id = product&.master_id
          stock_items = Spree::StockItem.where(variant_id: variant_id)
          return stock_items.order(count_on_hand: :desc).first.stock_location_id if stock_items.any?

          Spree::StockLocation.active.default
        end

        def get_shipment_state
          amazon_shipment_state = order_data.dig(:OrderStatus)
          if amazon_shipment_state == "Unshipped"
            return "ready"
          elsif amazon_shipment_state == "Canceled"
            return "pending"
          elsif amazon_shipment_state == "Shipped"
            return "shipped"
          end
        end

        def order_state
          if order_data.dig(:OrderStatus).downcase == "canceled"
            return "canceled"
          else
            return "complete"
          end
        end

        def create_and_assign_line_items
          response = ::Amazon::OrderApis::GetOrderItem.new(store.id, oauth_application.id).line_item(order_data.dig(:AmazonOrderId))
          @order_line_items = response.with_indifferent_access.dig(:payload, :OrderItems)
          intent_to_cancel = false
          item_total_price = 0
          order_line_items.each do |line_item|
            @listing, @product = find_listing(line_item.dig(:SellerSKU))
            Rails.logger.info("amazon create_and_assign_line_items, listing id: #{@listing.id}") if @listing.present?

            if intent_to_cancel == false
              requested_cancel = line_item&.dig(:BuyerRequestedCancel)
              if requested_cancel.present? && requested_cancel&.dig(:IsBuyerRequestedCancel) == "true"
                intent_to_cancel = true
                order_data[:intentToCancel] = "TRUE"
                order.update!(sale_channel_metadata: order_data)
              end
            end

            amazon_line_item = Spree::LineItem.where(order_id: order.id, amazon_line_item_id: line_item.dig(:OrderItemId)).first
            unless amazon_line_item.present?
              variant_id = @product&.master_id
              return unless variant_id.present?
              variant = Spree::Variant.find(variant_id)
              return unless variant.present?

              amazon_line_item = Spree::LineItem.find_or_create_by!(variant_id: variant_id, quantity: line_item.dig(:QuantityOrdered), price: get_line_item_price(line_item), order_id: order.id, currency: line_item.dig(:ItemPrice, :CurrencyCode), amazon_line_item_id: line_item.dig(:OrderItemId))
              amazon_line_item.update_columns(private_metadata: line_item)
              amazon_line_item.update_columns(price: get_line_item_price(line_item))
              amazon_line_item.update(listing_id: @listing.id) if @listing.present?
              order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : amazon Line Item (#{amazon_line_item.id}) has been successfully created")
              # append free shipping method in shipment for free_shipping orders
              shipment = order.shipments.first
              append_free_shipping_method(shipment, line_item) if shipment.present?

              # Set inventory units to false for amazon orders as we are not running callbacks to finalize inventory_units
              amazon_line_item.inventory_units.update_all(pending: false, updated_at: Time.now)

              # Need to run only while creation of order
              SyncListingsAvailable.perform_now(store.id, listing, amazon_line_item.quantity, variant_id) if listing.present? && store.risky_listing.present?

              # assign pack_size of listing in line_item
              amazon_line_item.update(pack_size: listing.pack_size_value) if listing.present? && listing.pack_size_toggle.present?
              # Create Ebay Taxes
              create_line_item_adjustment(amazon_line_item, line_item)
            else
              amazon_line_item.update(quantity: line_item[:QuantityOrdered], price: get_line_item_price(line_item), currency: line_item.dig(:ItemPrice, :CurrencyCode))
              amazon_line_item.update(listing_id: @listing.id) if @listing.present?
              if line_item&.dig(:ShippingPrice, :Amount)&.to_f.present?
                amazon_line_item.update_columns(private_metadata: line_item)
                shipment = order.shipments.first
                append_free_shipping_method(shipment, line_item) if shipment.present?
              end
            end
            # add FeesEstimateResult
            fees_estimate_result = amazon_line_item&.public_metadata&.dig("FeesEstimateResult")
            if fees_estimate_result.nil?
              seller_sku = line_item.dig(:SellerSKU)
              if seller_sku.present?
                order_item_id = line_item&.dig(:OrderItemId)
                total_li_price = line_item.dig(:ItemPrice, :Amount).to_f
                estimate_response = ::Amazon::ProductApis::GetMyFeesEstimateForSku.new(store.id, oauth_application.id).get_my_fees_estimate_for_sku(order_item_id, total_li_price, seller_sku)
                if estimate_response
                  estimate_result = response.with_indifferent_access.dig(:payload)
                  amazon_line_item.update_columns(public_metadata: estimate_result)
                end
              end
            end

            item_total_price = item_total_price + line_item.dig(:ItemPrice, :Amount).to_f
          end
          order.update!(item_total: item_total_price) if item_total_price > 0
        end

        # def find_variant_id(listing, line_item)
        #   variation_aspects = line_item.dig(:variationAspects)
        #   return listing.variant_stock_items_data.keys[0] unless variation_aspects

        #   listing.product.variants.each do |variant|
        #     variant_option_values = variant.option_values.map { |x| { "name" => x.option_type.name, "value" => x.name } }
        #     sorted_variant_option_values = variant_option_values.sort_by { |h| h["name"] }

        #     sorted_variation_aspects = variation_aspects.sort_by { |h| h["name"] }

        #     return variant.id if sorted_variant_option_values == sorted_variation_aspects
        #   end
        # end

        def get_line_item_price(line_item)
          total_line_item_price = line_item.dig(:ItemPrice, :Amount).to_f
          quantity = line_item.dig(:QuantityOrdered).to_f
          line_item_price = total_line_item_price/quantity
          line_item_price
        end

        def create_line_item_adjustment(amazon_line_item, line_item)

          return unless line_item.dig(:ItemTax)
          tax_price = line_item.dig(:ItemTax, :Amount) || 0
          tax_category = Spree::TaxCategory.find_or_create_by(name: "Amazon_Tax", store_id: store.id)
          tax_rate = Spree::TaxRate.where(tax_category_id: tax_category.id, name: "Amazon_Tax_Rate", amount: tax_price&.to_d).first_or_initialize
          tax_rate.calculator = Spree::Calculator::DefaultTax.first_or_create! if tax_rate.calculator.nil?
          if tax_rate.save!
            amazon_order_adjustment = amazon_line_item.adjustments.create!(label: "Amazon_tax", source_id: tax_rate.id, source_type: 'Spree::TaxRate', amount: 0.0, order_id: order.id)
            amazon_order_adjustment.update(amount: tax_price&.to_d)
            order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : amazon Tax Adjustment (#{amazon_order_adjustment.id}) has been successfully created with amount - #{tax_price&.to_d}")
          end
        end

        def find_listing(seller_sku)
          listing = Spree::Listing.for_brand('amazon').find_by(sku: seller_sku)
          # listing = Spree::Listing.find_by(sku: seller_sku)
          product = listing&.product
          return listing, product
        end

        def create_and_assign_address
          address_data = order_data.dig(:ShippingAddress)
          full_name = order_data.dig(:BuyerInfo, :BuyerName) || 'xx'
          last_space_index = full_name.rindex(' ')
          if last_space_index.nil?
            firstname = full_name
            lastname = ''
          else
            firstname = full_name[0, last_space_index].strip
            lastname = full_name[(last_space_index+1)..].strip
          end
          address_hash = {firstname: firstname, lastname: lastname}
          final_hash = get_basic_address_hash(address_data)
          return if final_hash.empty?
          final_hash.merge!(address_hash)
          if final_hash.present?
            unless order.ship_address.present?
              @address = Spree::Address.create!(final_hash)
              order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : amazon Address created with id - Address #{@address.id}")
              order.update(ship_address_id: address.id, bill_address_id: address.id)
            else
              @address = order.ship_address
              if full_name != 'xx' && final_hash&.dig(:firstname).present?
                address.update!(final_hash)
              end
            end
          end
        end

        def create_and_assign_payments
          check_payment_method = Spree::PaymentMethod::Check.where(
            name: 'Amazon',
            description: 'Amazon Payments.',
            active: true
          ).first_or_initialize
          check_payment_method.store_ids = [store.id]

          payment = Spree::Payment.find_or_create_by(amount: order_data.dig(:OrderTotal, :Amount), order_id: order.id, payment_method: check_payment_method, state: "completed")
        end

        def get_payment_state
          order_state == "canceled" ? "void" : "paid"
        end

        def get_basic_address_hash(address_data)
          basic_address_hash = {}
          if address_data.present?
            country = find_country(address_data.dig(:CountryCode))
            return basic_address_hash if country.nil?
            state = find_state(address_data.dig(:StateOrRegion), country)
            return basic_address_hash if state.nil?
            basic_address_hash = {
              address1: address_data.dig(:AddressLine1) || "#{address_data.dig(:City)} #{address_data.dig(:PostalCode)} #{state}",
              address2: address_data.dig(:AddressLine2),
              city: address_data.dig(:City),
              zipcode: address_data.dig(:PostalCode) || "23456",
              country_id: country.id,
              phone: "XXXXXXXXXXXX",
              state_name: state&.name,
              state_id: state&.id || 1
            }
          end
          basic_address_hash
        end

        def find_country(country_code)
          country = Spree::Country.find_by(iso: country_code)
          country.present? ? country : Spree::Country.default
        end

        def find_state(state, country)
          if state.present? && country.present?
            state = ::Spree::State.find_by(
                      "(abbr = ? OR name = ?) AND country_id = ?",
                      state&.upcase, state.capitalize, country.id
                    )
          end
          state
        end

        # def handle_refunded_order
        #   order_data.dig(:paymentSummary, :payments).each do |data|
        #     if payment_paid_and_refunded?(data)
        #       payment = order.payments.last
        #       update_order_once_refunded && return if payment.refunds.count.eql?(order_data.dig(:paymentSummary, :refunds).count)

        #       create_refund_for_order(payment)
        #       update_order_once_refunded
        #     end
        #   end
        # end

        # def payment_paid_and_refunded?(payment_data)
        #   payment_data[:paymentStatus].downcase == "paid" && EBAY_REFUNDED_STATUSES.include?(order_data[:orderPaymentStatus])
        # end

        # def update_order_once_refunded
        #   order.update_columns(payment_state: 'balance_due', shipment_state: 'canceled')
        #   order.shipments.first.update_columns(state: 'canceled') if order.shipments.present?
        # end

        # def create_refund_for_order(payment)
        #   order_data.dig(:paymentSummary, :refunds).each do |data|
        #     refund_amount = data[:amount][:value]
        #     refund_record = payment.refunds.find_or_initialize_by(refund_reference_id: data[:refundReferenceId])
        #     refund_record.update(amount: refund_amount, refund_reason_id: 1)
        #   end
        # end

        # def sync_ebay_listing_for_order(line_item)
        #   listing = Spree::Listing.find_by(item_id: line_item.dig(:legacyItemId))
        #   return unless listing.present?

        #   GetItemJob.perform_later(listing, store, listing.item_id, nil, "Order")
        #   details = "ImportLog (#{import_log.id}): GetItemJob has been triggered for order number - #{order.number} and listing item_id - #{listing.item_id} to update listing inventory"
        #   order.tracker_logs.create(details:)
        # end
      end
    end
  end
