module SpreeInsertion
  module AccountPolicy
    class ReturnPolicy < SpreeInsertion::Base
      def execute(sale_channel_id)
        if spree_response.present?
          api_policy_ids = []
          spree_response.with_indifferent_access[:returnPolicies].each do |policy|
            return_policy = Spree::EbayPolicy.find_or_create_by(policy_type: 'return', policy_id: policy[:returnPolicyId], store_id: store.id, sale_channel_id: sale_channel_id)
            return_policy.update(response_hash: policy)
            api_policy_ids << policy[:returnPolicyId]
          end
          Spree::EbayPolicy.where(store_id: store.id, policy_type: 'return', sale_channel_id: sale_channel_id).where.not(policy_id: api_policy_ids).destroy_all
        end
      end
    end
  end
end
