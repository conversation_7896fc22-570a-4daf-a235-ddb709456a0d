module SpreeInsertion
  module AccountPolicy
    class FullfillmentPolicy < SpreeInsertion::Base
      def execute(sale_channel_id)
        if spree_response.present?
          api_policy_ids = []
          spree_response.with_indifferent_access[:fulfillmentPolicies].each do |policy|
            fullfillment_policy = Spree::EbayPolicy.find_or_create_by(policy_type: 'fullfillment', policy_id: policy[:fulfillmentPolicyId], store_id: store.id, sale_channel_id: sale_channel_id)
            fullfillment_policy.update(response_hash: policy)
            api_policy_ids << policy[:fulfillmentPolicyId]
          end
          Spree::EbayPolicy.where(store_id: store.id, policy_type: 'fullfillment', sale_channel_id: sale_channel_id).where.not(policy_id: api_policy_ids).destroy_all
        end
      end
    end
  end
end
