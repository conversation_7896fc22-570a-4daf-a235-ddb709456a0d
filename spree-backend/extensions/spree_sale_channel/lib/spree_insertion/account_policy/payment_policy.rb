module SpreeInsertion
  module AccountPolicy
    class PaymentPolicy < SpreeInsertion::Base
      def execute(sale_channel_id)
        if spree_response.present?
          api_policy_ids = []
          spree_response.with_indifferent_access[:paymentPolicies].each do |policy|
            payment_policy = Spree::EbayPolicy.find_or_create_by(policy_type: 'payment', policy_id: policy[:paymentPolicyId], store_id: store.id, sale_channel_id: sale_channel_id)
            payment_policy.update(response_hash: policy)
            api_policy_ids << policy[:paymentPolicyId]
          end
          Spree::EbayPolicy.where(store_id: store.id, policy_type: 'payment', sale_channel_id: sale_channel_id).where.not(policy_id: api_policy_ids).destroy_all
        end
      end
    end
  end
end