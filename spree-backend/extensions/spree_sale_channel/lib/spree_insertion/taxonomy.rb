module SpreeInsertion
  class Taxonomy < SpreeInsertion::Base
    attr_accessor :taxonomy

    def format_taxonomy_record_response
      @taxonomy = Spree::Taxonomy.find_or_create_by!(name: "eBay", store_id: store.id)
      return unless taxonomy
      root_taxons = spree_response.with_indifferent_access[:rootCategoryNode][:childCategoryTreeNodes]

      root_taxons.each do |root_taxon|
        get_category_node(root_taxon, taxonomy.root)
      end
    end

    def get_category_node(taxon, parent_taxon)
      parent_taxon = parent_taxon.children.find_or_create_by(name: taxon[:category][:categoryName], category_id: taxon[:category][:categoryId], taxonomy_id: taxonomy.id)
      return if taxon['leafCategoryTreeNode'] == 'true'

      if taxon[:childCategoryTreeNodes].present?
        taxon[:childCategoryTreeNodes].each do |child_taxon|
          get_category_node(child_taxon, parent_taxon)
        end
      end
    end
  end
end

