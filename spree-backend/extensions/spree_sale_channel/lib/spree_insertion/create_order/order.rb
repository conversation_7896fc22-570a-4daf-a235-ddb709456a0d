class ProductNotCreatedError < ::StandardError
end

module SpreeInsertion
  module CreateOrder
    class Order < SpreeInsertion::Base
      include ::Spree::Admin::SubscribersHelper
      include ::Spree::Admin::LineItemsExtHelper

      attr_accessor :order_data, :store, :import_log, :order, :address, :product, :listing

      EBAY_REFUNDED_STATUSES = %w[FULLY_REFUNDED PARTIALLY_REFUNDED].freeze

      def initialize(order_data, store, import_log)
        @store = store
        @order_data = order_data
        @import_log = import_log
      end

      def make
        unless order_data.dig(:orderPaymentStatus) == "FAILED" || order_data.dig(:orderPaymentStatus) == "PENDING"
          begin
            @order = Spree::Order.find_or_create_by(number: order_data[:orderId])

            order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : Order has been successfully synced")

            order_email = get_email_id.present? ? get_email_id : (order.email || '<EMAIL>')

            previous_state = order.state
            order.update!(item_total: get_price("priceSubtotal"), shipment_total: get_price("deliveryCost"), total: get_price("total"), state: order_state, email: order_email, shipment_state: get_shipment_state, currency: order_data.dig(:pricingSummary, :total, :currency), last_modified_date: order_data.dig(:lastModifiedDate), sale_channel_metadata: order_data, completed_at: order_data.dig(:creationDate), channel: "eBay")

            handle_cancel_order if order_state == 'canceled' # Cancels inventory units to ensure accurate count management.

            oauth_application = Spree::OauthApplication.where(seller_name: order_data[:sellerId]).last
            order.update(sale_channel_id: oauth_application.sale_channel_id) if oauth_application

            create_and_assign_address
            create_and_assign_line_items
            create_and_assign_payments
            update_and_assign_shipments

            # create subscriber for email campaign
            find_or_create_subscriber

            check_line_items_ext if previous_state != 'complete' && order_state == 'complete'

            order.update_columns(payment_state: order_data[:orderPaymentStatus].downcase, shipment_state: get_shipment_state)
            handle_refunded_order
          rescue => e
            update_failed_import_log_counter(import_log)
            write_import_logs(import_log, e, order.number)
          else
            update_successful_import_log_counter(import_log)
            GetTransactionsJob.perform_later(store.id, oauth_application.id, order.number) if oauth_application
          end
        end
      end

      def find_or_create_subscriber
        subscriber = Spree::Subscriber.find_or_create_by(email: order.email)
        process_subscriber_action(subscriber, 'placed order', order) if subscriber
      end

      def check_line_items_ext
        if order.line_items.present?
          order.line_items.each do |item|
            line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
            process_line_items_ext(line_items_ext, item, order) if line_items_ext
          end
        end
      end

      def update_and_assign_shipments
        order.reload
        order.shipments.each do |shipment|
          # If order is shipped from ebay
          if order_data.dig(:fulfillmentHrefs).present? && !shipment.shipped?
            shipment.update_columns(tracking: order_data.dig(:fulfillmentHrefs).first&.split('/')&.last)
            order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : Received tracking details from ebay and updating shipment (#{shipment.number})")
          end
          # Depricated - In future we will not bind listing with stock locations and rather handle it during shipment process
          shipment.update_columns(state: get_shipment_state, stock_location_id: get_stock_location, order_id: order.id, address_id: address.id )
          # Confirm stock_item_units should be in shipped state once shipment is shipped.
          if shipment.shipped?
            unless order_package = shipment.order_package
              order_package = order.order_packages.first_or_create
            end

            order_package.update_columns(state: 'shipped', tracking_label: shipment.tracking_label)
            # order_package.update_columns(selected_shipping_rate_id: shipment.selected_shipping_rate&.id) if shipment.shipping_rates

            shipment.update_columns(order_package_id: order_package.id)
            shipment.stock_item_units.update(state: 'shipped')
            shipment.inventory_units.update(state: 'shipped')
          end
        end
      end

      def append_free_shipping_method(shipment)
        ebay_shipment_value = order_data.dig(:pricingSummary, :deliveryCost, :value)
        shipping_category_name = "Free Shipping" if ebay_shipment_value != nil && ebay_shipment_value&.to_f == 0
        shipping_method = create_shipping_method(shipment, shipping_category_name)
        if shipping_category_name
          shipment.shipping_methods << shipping_method unless shipment.shipping_methods.include?(shipping_method)
          shipment.shipping_rates.update_all(selected: false)
          free_shipping_rate = shipment.shipping_rates.where(shipping_method_id: shipping_method.id)
          free_shipping_rate.update(selected: true, buyer_selected: true)
          shipment.update_columns(cost: 0.0, buyer_paid_amount: ebay_shipment_value)
        end
      end

      def create_shipping_method(shipment, shipping_category_name=nil)
        shipping_category = shipping_category_name.present? ? Spree::ShippingCategory.find_or_create_by(name: shipping_category_name, store_id: store.id) : Spree::ShippingCategory.first
        attributes =
          {
            name: shipping_category_name || 'Ebay_Shipping',
            display_on: 'both',
            shipping_categories: [shipping_category],
            store_id: store.id
          }
        shipping_method = Spree::ShippingMethod.where(name: attributes[:name], store_id: attributes[:store_id]).first_or_create! do |method|
          method.calculator = Spree::Calculator::Shipping::FlatRate.first_or_create! if method.calculator == nil
          method.display_on = attributes[:display_on]
          method.shipping_categories = attributes[:shipping_categories]
        end
        shipping_method
      end

      def get_stock_location
        line_item_data = order_data.dig(:lineItems).first
        variant_id = find_variant_id(listing, line_item_data)
        stock_items = Spree::StockItem.where(variant_id: variant_id)
        return stock_items.order(count_on_hand: :desc).first.stock_location_id if stock_items.any?

        Spree::StockLocation.active.default
      end

      def get_shipment_state
        return "canceled" if order_data.dig(:cancelStatus, :cancelState).downcase == "canceled"

        return order.shipment_state if order.shipped?

        ebay_shipment_state = order_data.dig(:orderFulfillmentStatus)
        if ebay_shipment_state == "NOT_STARTED"
          return "ready"
        elsif ebay_shipment_state == "FULFILLED"
          return "shipped"
        end
      end

      def get_email_id
        ebay_emails = []
        order_data.dig(:fulfillmentStartInstructions).each do |data|
          address_data = data[:shippingStep][:shipTo]
          next false unless address_data.present?
          ebay_emails += [address_data[:email]]
        end
        ebay_emails = ebay_emails.uniq
        ebay_emails.first if ebay_emails
      end

      def order_state
        if order_data.dig(:cancelStatus, :cancelState).downcase == "canceled"
          return "canceled"
        else
          return "complete"
        end
      end

      def create_and_assign_line_items
        order_data.dig(:lineItems).each do |line_item|
          @listing, @product = find_listing(line_item.dig(:legacyItemId))
          variant_id = find_variant_id(listing, line_item)
          variant = Spree::Variant.find(variant_id)

          ebay_line_item = Spree::LineItem.where(order_id: order.id, ebay_line_item_id: line_item.dig(:lineItemId)).first
          unless ebay_line_item.present?
            ebay_line_item = Spree::LineItem.find_or_create_by!(variant_id: variant_id, quantity: line_item[:quantity], price: get_line_item_price(line_item), order_id: order.id, currency: line_item.dig(:total, :currency), ebay_line_item_id: line_item.dig(:lineItemId))

            # Updating price since it's sourced from the product.
            ebay_line_item.update_columns(price: get_line_item_price(line_item), listing_id: listing&.id)

            order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : Line Item (#{ebay_line_item.id}) has been successfully created")

            # Pull listing corresponding to order to update listing inventory
            sync_ebay_listing_for_order(line_item)

            # append free shipping method in shipment for free_shipping orders
            shipment = order.shipments.first
            append_free_shipping_method(shipment) if shipment.present?


            # Set inventory units to false for ebay orders as we are not running callbacks to finalize inventory_units
            ebay_line_item.inventory_units.update_all(pending: false, updated_at: Time.now)

            # Need to run only while creation of order
            SyncListingsAvailable.perform_now(store.id, listing, ebay_line_item.quantity, variant_id) if store.risky_listing.present?

            # assign pack_size of listing in line_item
            ebay_line_item.update(pack_size: listing.pack_size_value) if listing.pack_size_toggle.present?
            # Create Ebay Taxes
            create_line_item_adjustment(ebay_line_item, line_item)
          else
            ebay_line_item.update(quantity: line_item[:quantity], price: get_line_item_price(line_item), currency: line_item.dig(:total, :currency), listing_id: listing&.id)
          end
        end
      end

      def find_variant_id(listing, line_item)
        variation_aspects = line_item.dig(:variationAspects)
        return listing.variant_stock_items_data.keys[0].to_i unless variation_aspects

        listing.product.variants.each do |variant|
          variant_option_values = variant.option_values.map { |x| { "name" => x.option_type.name, "value" => x.name } }
          sorted_variant_option_values = variant_option_values.sort_by { |h| h["name"] }

          sorted_variation_aspects = variation_aspects.sort_by { |h| h["name"] }

          return variant.id.to_i if sorted_variant_option_values == sorted_variation_aspects
        end
      end

      def get_line_item_price(line_item)
        delivery_price =  line_item.dig(:deliveryCost, :shippingCost, :value)
        total_price =  line_item.dig(:total, :value)
        line_item_price = (total_price.to_f - delivery_price.to_f)/line_item.dig(:quantity) if total_price
        line_item_price
      end

      def create_line_item_adjustment(ebay_line_item, line_item)
        return unless line_item.dig(:ebayCollectAndRemitTaxes)
        tax_price = line_item.dig(:ebayCollectAndRemitTaxes).map { |hash| hash.with_indifferent_access["amount"]["value"].to_f }.reduce(0, :+) || 0
        tax_category = Spree::TaxCategory.find_or_create_by(name: "Ebay_Tax", store_id: store.id)
        tax_rate = Spree::TaxRate.where(tax_category_id: tax_category.id, name: "Ebay_Tax_Rate", amount: tax_price&.to_d).first_or_initialize
        tax_rate.calculator = Spree::Calculator::DefaultTax.first_or_create! if tax_rate.calculator.nil?
        if tax_rate.save!
          ebay_order_adjustment = ebay_line_item.adjustments.create!(label: "eBay_tax", source_id: tax_rate.id, source_type: 'Spree::TaxRate', amount: 0.0, order_id: order.id)
          ebay_order_adjustment.update(amount: tax_price&.to_d)
          order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : Tax Adjustment (#{ebay_order_adjustment.id}) has been successfully created with amount - #{tax_price&.to_d}")
        end
      end

      def find_listing(item_id)
        listing = Spree::Listing.for_brand('eBay').find_by(item_id: item_id)
        # listing = Spree::Listing.find_by(item_id: item_id)
        product = listing.product
        return listing, product
      end

      def create_and_assign_address
        order_data.dig(:fulfillmentStartInstructions).each do |data|
          address_data = data[:shippingStep][:shipTo]
          next false unless address_data.present?

          full_name = address_data[:fullName] || ''
          last_space_index = full_name.rindex(' ')
          if last_space_index.nil?
            firstname = full_name
            lastname = ''
          else
            firstname = full_name[0, last_space_index].strip
            lastname = full_name[(last_space_index+1)..].strip
          end
          address_hash = {firstname: firstname, lastname: lastname}
          final_hash = get_basic_address_hash(address_data)
          final_hash.merge!(address_hash)
          if final_hash.present?
            unless order.ship_address.present?
              @address = Spree::Address.create!(final_hash)
              order.tracker_logs.create(details: "ImportLog (#{import_log.id}) : Address created with id - Address #{@address.id}")
              order.update!(ship_address_id: address.id, bill_address_id: address.id)
            else
              @address = order.ship_address
              address.update(final_hash)
            end
          end
        end
      end

      def create_and_assign_payments
        check_payment_method = Spree::PaymentMethod::Check.where(
          name: 'Ebay',
          description: 'Ebay Payments.',
          active: true
        ).first_or_initialize
        check_payment_method.store_ids = [store.id]
        check_payment_method.save!

        order_data.dig(:paymentSummary, :payments).each do |data|
          payment = Spree::Payment.find_or_create_by(amount: data[:amount][:value], order_id: order.id, payment_method: check_payment_method, public_metadata: {paymentReferenceId: data[:paymentReferenceId]} )
          payment.update(state: payment_state(data)) if payment
        end
      end

      def payment_state(data)
        if data[:paymentStatus].downcase == "paid"
          return 'completed'
        elsif data[:paymentStatus].downcase == "pending"
          return 'pending'
        else
          return 'failed'
        end
      end

      def get_basic_address_hash(address_data)
        basic_address_hash = {}
        contact_address = address_data.dig(:contactAddress)
        if contact_address.present?
          country = find_country(contact_address.dig(:countryCode))
          state = find_state(contact_address.dig(:stateOrProvince), country)
          basic_address_hash = {
            address1: contact_address.dig(:addressLine1),
            address2: contact_address.dig(:addressLine2),
            city: contact_address.dig(:city),
            zipcode: contact_address.dig(:postalCode) || "23456",
            country_id: country.id,
            phone: address_data.dig(:primaryPhone, :phoneNumber) || "XXXXXXXXXXXX",
            state_name: contact_address.dig(:stateOrProvince),
            state_id: state&.id
          }
        end
        basic_address_hash
      end

      def find_country(country_code)
        country = Spree::Country.find_by(iso: country_code)
        country.present? ? country : Spree::Country.default
      end

      def find_state(state_abbr, country)
        if state_abbr.present? && country.present?
          state = ::Spree::State.find_by(abbr: state_abbr&.upcase, country_id: country.id)
        end
        state
      end

      def get_price(price_type)
        order_data.dig(:pricingSummary, price_type.to_s, :value)
      end

      def handle_refunded_order
        order_data.dig(:paymentSummary, :payments).each do |data|
          if payment_paid_and_refunded?(data)
            order.reload
            payment = order.payments.last
            update_order_once_refunded && return if payment.refunds.count.eql?(order_data.dig(:paymentSummary, :refunds).count)

            create_refund_for_order(payment)
            update_order_once_refunded
          end
        end
      end

      def payment_paid_and_refunded?(payment_data)
        payment_data[:paymentStatus].downcase == "paid" && EBAY_REFUNDED_STATUSES.include?(order_data[:orderPaymentStatus])
      end

      def update_order_once_refunded
        order.update_columns(payment_state: 'partial', shipment_state: 'canceled')
        order.shipments.first.update_columns(state: 'canceled') if order.shipments.present?
        order.payments.first.update_columns(state: 'partial') if order.payments.present?
        order.tracker_logs.create(details: "Refund has been created with order shipment state #{order.shipment_state} and payment state #{order.payment_state}")
      end

      def create_refund_for_order(payment)
        order_data.dig(:paymentSummary, :refunds).each do |data|
          refund_amount = data[:amount][:value]
          refund_record = payment.refunds.find_or_initialize_by(refund_reference_id: data[:refundReferenceId], transaction_id: data[:refundReferenceId])
          refund_record.update(amount: refund_amount, refund_reason_id: 1)
        end
      end

      def sync_ebay_listing_for_order(line_item)
        listing = Spree::Listing.find_by(item_id: line_item.dig(:legacyItemId))
        return unless listing.present?

        GetItemJob.perform_later(listing, store, listing.item_id, nil, "Order")
        details = "ImportLog (#{import_log.id}): GetItemJob has been triggered for order number - #{order.number} and listing item_id - #{listing.item_id} to update listing inventory"
        order.tracker_logs.create(details:)
      end

      def handle_cancel_order
        order.shipments.each do |shipment|
          shipment.update_columns(state: 'canceled')
          shipment.stock_item_units.update(state: 'stock', line_item_id: nil, shipment_id: nil)
        end
      end
    end
  end
end
