module SpreeInsertion
  module UpcItem
    class ScannedItem
      attr_accessor :spree_response

      def initialize(response, scanned_item)
        @spree_response = response.with_indifferent_access
        @object = scanned_item
      end

      def execute
        return unless spree_response.present?
        upc_data = []
        if @object.upc_data.present?
          if @object.upc_data.respond_to?(:with_indifferent_access)
            upc_data.push(@object.upc_data)
          else
            upc_data = @object.upc_data
          end
        end
        if upc_data.detect {|data| data[:service] == spree_response[:service]}.nil?
          upc_data.push(spree_response)
        end
        item_updates = { "ean"=> spree_response.dig(:ean), "title"=> spree_response.dig(:title), "description"=> spree_response.dig(:description), "brand"=> spree_response.dig(:brand), "model"=> spree_response.dig(:model), "currency"=> spree_response.dig(:currency), "images"=> spree_response.dig(:images), "upc_data"=> upc_data }
        @object.update(item_updates)
      end
    end
  end
end
