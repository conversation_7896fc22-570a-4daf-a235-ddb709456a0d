module SpreeInsertion
  class Listing < SpreeInsertion::Base
    def execute(seller_data)
      spree_response.each do |list|
        listing = Spree::Listing.for_brand('eBay').find_by(item_id: list[:item_id])
        if listing
          listing.update(list)
        else
          listing = Spree::Listing.create!(list)
          update_listing_images(listing, seller_data[:PictureDetails]) if listing.present? && seller_data[:PictureDetails].present?
          create_listing_inventory(listing, seller_data)
        end
      end
    end

    private

    def create_listing_inventory(listing, seller_data)
      product = listing.product
      if product.variants.present? && seller_data[:Variations]
        variants = seller_data[:Variations]
        variant_array = variants[:Variation].is_a?(Array) ? variants[:Variation] : [variants[:Variation]]
        variant_array.each_with_index do |variant, _index|
          existing_variant = find_variant(product, variant[:VariationSpecifics])
          sync_listing_sold_qty(listing, existing_variant, variant.dig(:SellingStatus, :QuantitySold), variant[:StartPrice], variant[:Quantity])
        end
      else
        sync_listing_sold_qty(listing, product.master, seller_data.dig(:SellingStatus, :QuantitySold), seller_data[:SellingStatus][:CurrentPrice], seller_data[:Quantity])
      end
    end

    def update_listing_images(listing, img_urls)
      images = img_urls[:PictureURL]&.kind_of?(Array) ? img_urls[:PictureURL] : [img_urls[:PictureURL]]
      if images.any?
        images.each do |img|
          begin
            ActiveRecord::Base.transaction do
              image = URI.parse(img)
              image_file = image.open
              image_name = File.basename(image.path)
              option_text = "master"
              filename = "#{option_text}_#{listing.product&.master&.id}_#{image_name}"
              listing.image_files.attach(io: image_file, filename: filename)
            end
          rescue StandardError => e
            next
          end
        end
      end
    end

    def find_variant(product, variation_specifics)
      ebay_option_value = []

      option_type_value_array = variation_specifics.dig(:NameValueList).is_a?(Array) ? variation_specifics.dig(:NameValueList) : [variation_specifics.dig(:NameValueList)]
      option_type_value_array.each do |name_value_list|
        option_type_id = Spree::OptionType.find_by(name: name_value_list.dig(:Name))
        option_value_id = Spree::OptionValue.find_by(name: name_value_list.dig(:Value), option_type_id: option_type_id)&.id

        ebay_option_value += [option_value_id] if option_value_id.present?
      end

      product.variants.each do |variant|
        return variant if variant.option_values && (variant.option_values&.to_a&.pluck(:id) == ebay_option_value)
      end
      nil
    end

    def sync_listing_sold_qty(listing, variant, sold_qty, price, quantity)
      return unless listing.present?
      listing_inventory = listing.listing_inventories.find_or_create_by(variant_id: variant.id)
      listed_quantity = quantity.to_i >= sold_qty.to_i ? quantity.to_i : quantity.to_i + sold_qty.to_i
      listing_inventory.update(total_listed_qty: listed_quantity, total_sold_qty: sold_qty, price: price) if listing_inventory.present?
    end
  end
end
