module SyncServices
  module Listing
    class GroupingProcessor
      attr_reader :seller_data_hash

      def initialize(seller_data_hash, sale_channel)
        @sale_channel = sale_channel
        @seller_data_hash = seller_data_hash
        @items_grouped = { with_vgi: {}, without_vgi: [] }
      end

      def process
        return seller_data_hash unless walmart_brand?

        # Group items based on variantGroupId
        group_items_by_vgi

        # Process grouped items for final seller data hash
        result = []

        # Process items with variantGroupId (VGI)
        @items_grouped[:with_vgi].each do |variantGroupId, items|
          next if items.blank?

          primary = nil
          variants = []

          items.each do |item|
            if item[:primary]
              primary = item.clone
              primary[:isPrimary] = true
            end
            variants << item
          end

          if primary.nil?
            primary = items[0].clone
            primary[:isPrimary] = true
          end

          primary[:Variations] = variants

          result << primary
        end

        # Process items without variantGroupId (individual items)
        @items_grouped[:without_vgi].each do |item|
          item[:isPrimary] = true
          result << item
        end

        # Return the final result for seller data hash
        seller_data_hash = result
        seller_data_hash
      end

      private

      def group_items_by_vgi
        Array(seller_data_hash.dig(:items, :ItemResponse)).flatten.each do |seller_data|
          next if seller_data[:sku].blank?

          variantGroupId = seller_data.dig(:variantGroupId)

          if variantGroupId.present?
            @items_grouped[:with_vgi][variantGroupId] ||= []
            @items_grouped[:with_vgi][variantGroupId] << seller_data
          else
            @items_grouped[:without_vgi] << seller_data
          end
        end
      end

      def walmart_brand?
        @sale_channel.brand == "walmart"
      end
    end
  end
end
