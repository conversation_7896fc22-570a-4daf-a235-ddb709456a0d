module SyncServices
  module Listing
    class Base
      def initialize(seller_data_hash, store, import_log, sale_channel, product = nil)
        @sale_channel = sale_channel
        @store = store
        @seller_data_hash = get_seller_data(seller_data_hash)
        @import_log = import_log
        @sale_channel_id = @sale_channel.id
        @oauth_application = @sale_channel.oauth_application
        @sale_channel_brand = @sale_channel.brand
        @product = product

        # Process the data before passing it on to ListingSync
        @seller_data_hash = InventoryProcessor.new(@seller_data_hash, @sale_channel).process
        @seller_data_hash = GroupingProcessor.new(@seller_data_hash, @sale_channel).process # This will call the grouping and return the processed hash
      end

      def walmart_sale_channel?
        @sale_channel.brand == "walmart"
      end

      def get_seller_data(seller_data_hash)
        if @sale_channel.brand == "ebay"
          seller_data_hash.dig(:response, :GetSellerListResponse, :ItemArray, :Item)
        else
          seller_data_hash
        end
      end
    end
  end
end
