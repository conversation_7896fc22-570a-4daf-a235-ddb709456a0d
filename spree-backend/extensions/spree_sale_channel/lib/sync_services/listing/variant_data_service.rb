module SyncServices
  module Listing
    class VariantDataService
      attr_reader :variant_formatted_data, :now_variant_formatted_data

      def initialize(valid_sale_channel)
        @walmart_sale_channel = valid_sale_channel # true or false
        @variant_formatted_data = []
        @now_variant_formatted_data = []
      end

      def group_variant_data(seller_data, formatted_seller_data)
        return unless @walmart_sale_channel.present?

        # Group data based on variantGroupId presence
        if seller_data.dig(:variantGroupId).present?
          variant_formatted_data.push(formatted_seller_data)
        else
          now_variant_formatted_data.push(formatted_seller_data)
        end
      end

      # Get only the appropriate group based on variant presence (for Walmart channel only)
      def get_grouped_data(seller_data)
        return [] unless @walmart_sale_channel.present?

        if seller_data.dig(:variantGroupId).present?
          variant_formatted_data
        else
          now_variant_formatted_data
        end
      end

      # Clear the data arrays to prevent accumulation across listings
      def reset_data
        return unless @walmart_sale_channel.present?

        variant_formatted_data.clear
        now_variant_formatted_data.clear
      end
    end
  end
end
