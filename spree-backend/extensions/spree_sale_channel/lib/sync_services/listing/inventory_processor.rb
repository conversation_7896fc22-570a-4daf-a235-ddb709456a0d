module SyncServices
  module Listing
    class InventoryProcessor
      attr_reader :seller_data_hash, :sale_channel

      def initialize(seller_data_hash, sale_channel)
        # @seller_data_hash = seller_data_hash.with_indifferent_access
        @seller_data_hash = if seller_data_hash.is_a?(Hash)
                              seller_data_hash.with_indifferent_access
                            else
                              seller_data_hash
                            end
        @sale_channel = sale_channel
      end

      def process
        return seller_data_hash unless walmart_brand?

        inventories = build_inventory_hash
        merge_inventory_data(inventories)
        seller_data_hash
      end

      private

      def walmart_brand?
        sale_channel.brand == "walmart"
      end

      def build_inventory_hash
        inventories = {}

        seller_data_hash.dig(:inventories, :inventories)&.each do |inventory|
          next if inventory[:sku].blank?

          inventories[inventory[:sku]] = aggregate_inventory(inventory[:nodes])
        end

        inventories
      end

      def aggregate_inventory(nodes)
        {
          inputQty: nodes.sum { |n| n.dig(:inputQty, :amount).to_i },
          availToSellQty: nodes.sum { |n| n.dig(:availToSellQty, :amount).to_i },
          reservedQty: nodes.sum { |n| n.dig(:reservedQty, :amount).to_i },
          nodes: nodes
        }
      end

      def merge_inventory_data(inventories)
        Array(seller_data_hash.dig(:items, :ItemResponse)).each do |seller_data|
          next if seller_data[:sku].blank?

          seller_data[:inventory] = inventories[seller_data[:sku]]
        end
      end
    end
  end
end
