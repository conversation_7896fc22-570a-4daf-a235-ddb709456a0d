module SyncServices
  module Listing
    class ListingSync < Listing::Base
      include ::Spree::Admin::ElasticsearchHelper
      def execute
        process_seller_data
      end

      private

      def process_seller_data
        variant_data_service = VariantDataService.new(walmart_sale_channel?)

        @seller_data_hash.flatten.each do |seller_data|
          sync_listing(seller_data, variant_data_service)
          variant_data_service.reset_data if walmart_sale_channel?
        end
      end

      def sync_listing(seller_data, variant_data_service)
        formatted_seller_data = format_seller_data(seller_data)
        return unless formatted_seller_data

        if walmart_sale_channel?
          variant_data_service.group_variant_data(seller_data, formatted_seller_data)
          grouped_data = variant_data_service.get_grouped_data(seller_data)
        end

        listing_service = RecordInserter::Listing::Inserter.new(
          grouped_data, formatted_seller_data, @store.id, @sale_channel
        )

        listing_service.call
      rescue StandardError => e
        Rails.logger.error "Error syncing listing: #{e.message}"
      end

      def format_seller_data(seller_data)
        Formatter::Listing::ResponseFormatter.new(seller_data, @sale_channel, @product).format
      rescue StandardError => e
        Rails.logger.error "Error formatting seller data: #{e.message}"
        nil
      end
    end
  end
end
