module Connector
  module UpcItem
    class UpcLookup < UpcItem::Base
      def format_upc_lookup_response
        data = {}
        return false unless response_hash
        response_hash.with_indifferent_access.dig(:items).each do |upc_item_data|
          data.merge!({"ean"=> upc_item_data.dig(:ean), "title"=> upc_item_data.dig(:title), "description"=> upc_item_data.dig(:description), "brand"=> upc_item_data.dig(:brand), "model"=> upc_item_data.dig(:model), "currency"=> upc_item_data.dig(:currency), "images"=> upc_item_data.dig(:images), "upc_data"=> upc_item_data })
        end
        data
      end
    end
  end
end
