module Connector
  module Walmart
    class GetOrders < Connector::Walmart::Base
      attr_accessor :sale_channel_id, :historical_pull

      def initialize(response_hash, store, import_log, sale_channel_id, historical_pull = nil)
        @response_hash = response_hash
        @store = store
        @import_log = import_log
        @sale_channel_id = sale_channel_id
        @historical_pull = historical_pull
      end

      def format_order_response
        response_hash.each do |order_data|
          purchased_order_id = order_data.dig('purchaseOrderId')
          walmart_update_order_hook = Spree::WalmartOrderHook.create!(order_id: purchased_order_id, event_type: "Update Hook", hook_received_at: Time.zone.now, payload: order_data) unless historical_pull.present?
          if historical_pull.present?
            response = SpreeInsertion::Walmart::HistoricalOrder.new(order_data, store, import_log, sale_channel_id).call
          else
            response = SpreeInsertion::Walmart::Order.new(order_data, store, import_log, sale_channel_id, walmart_update_order_hook).make
          end
        end
      end
    end
  end
end
