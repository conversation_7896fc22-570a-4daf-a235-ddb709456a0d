require "stringex"

module Connector
  module Walmart
    class GetAllItems < Connector::Walmart::Base
      include ::Spree::Admin::ElasticsearchHelper

      attr_accessor :listing_data, :condition_list, :seller_list_data, :product, :sale_channel_id, :oauth_application, :items_grouped

      def format_listing_response(status, oauth_application)
        @sale_channel_id = oauth_application.sale_channel_id
        @oauth_application = oauth_application
        @condition_list = []

        group_items_by_vgi

        # Handle the items with variantGroupId
        products = @items_grouped.dig(:with_vgi)
        # products like {id1: [{}, {}], id2: [{}, {}]}
        products.each do |variantGroupId, items|
          next if items.blank?

          primary = nil
          variants = []
          items.each do |item|
            if item[:primary]
              primary = item.clone
              primary[:isPrimary] = true
            end
            variants << item
          end

          if primary.nil?
            primary = items[0].clone
            primary[:isPrimary] = true
          end

          primary[:Variations] = variants
          @product = SpreeInsertion::Walmart::CreateProduct::Product.new(primary, store, import_log, sale_channel_id).make

          # For creating listing records in spree
          if product&.id?
            @seller_list_data = []
            listing = get_spree_listing_hash(primary, product)
            SpreeInsertion::Walmart::Listing.new(listing, store, sale_channel_id).execute(primary)
          end
        end

        # Handle the items without variantGroupId
        products = @items_grouped.dig(:without_vgi)
        # products like [{}, {}]
        products.each do |item|
          @product = SpreeInsertion::Walmart::CreateProduct::Product.new(item, store, import_log, sale_channel_id).make
          # For creating listing records in spree
          if product&.id?
            @seller_list_data = []
            listing = get_spree_listing_hash(item, product)
            SpreeInsertion::Walmart::Listing.new(listing, store, sale_channel_id).execute(item)
          end
        end

        helper_sync_listing_to_elasticsearch
        return condition_list.uniq
      end

      def get_spree_listing_hash(seller_data, product)
        if seller_data[:ConditionID]
          condition_list.push({
            name: seller_data[:ConditionDisplayName],
            condition_id: seller_data[:ConditionID],
            store_id: store.id,
          })
        end

        @listing_data = {
          title: seller_data[:productName],
          sku: seller_data[:sku],
          # quantity: seller_data[:Quantity],
          currency: seller_data.dig(:price, :currency),
          item_id: seller_data[:wpid],
          product_id: product.id,
          store_id: store.id,
          stock_item_id: product.stock_items&.first&.id,
          # item_url: seller_data.dig(:ListingDetails, :ViewItemURL),
          sale_channel_id: sale_channel_id,
        }

        # description_field(seller_data)
        # listing_allow_offer_details(seller_data)
        # listing_details(seller_data)
        # category_details(seller_data)
        listing_status(seller_data)
        listing_variant_specific_quantity(seller_data, product)
        # find_shipping_policy_id(seller_data)
        # find_return_policy_id(seller_data)
        # find_payment_policy_id(seller_data)
        # find_item_specifics(seller_data)
        # auction_pricing(seller_data) if seller_data.dig(:ListingType) == "Chinese"
        sale_channel_hash(seller_data, product)

        seller_list_data.push(listing_data)
      end

      def description_field(seller_data)
        # listing_data[:description] = seller_data[:Description] unless seller_data[:Description].nil?
        listing_data
      end

      def listing_allow_offer_details(seller_data)
        if seller_data[:BestOfferDetails] && seller_data[:BestOfferDetails][:BestOfferEnabled]
          allow_offer = seller_data.dig(:BestOfferDetails, :BestOfferEnabled)
          listing_data[:allow_offer] = allow_offer

          minOfferPrice = seller_data.dig(:ListingDetails, :MinimumBestOfferPrice)
          listing_data[:minimum_offer_price] = minOfferPrice.to_f unless minOfferPrice.nil?

          autoacceptOfferPrice = seller_data.dig(:ListingDetails, :BestOfferAutoAcceptPrice)
          listing_data[:autoaccept_offer_price] = autoacceptOfferPrice.to_f unless autoacceptOfferPrice.nil?
        else
          listing_data[:allow_offer] = false
        end
        listing_data
      end

      def listing_details(seller_data)
        listing_data.merge!({
          start_time: seller_data[:ListingDetails][:StartTime],
          end_time: seller_data[:ListingDetails][:EndTime],
          item_url: seller_data[:ListingDetails][:ViewItemURL],
        }) if seller_data[:ListingDetails]
      end

      def category_details(seller_data)
        category_id = seller_data.dig(:PrimaryCategory, :CategoryID)
        listing_data[:category_id] = category_id unless category_id.nil?
        listing_data
      end

      def listing_status(seller_data)
        # lifecycleStatus in ["ACTIVE", "ARCHIVED", "RETIRED"]
        lifecycle_status = seller_data.dig(:lifecycleStatus)
        if "ACTIVE".eql?(lifecycle_status)
          published_status = seller_data.dig(:publishedStatus)
          #  publishedStatus in ["PUBLISHED", "READY_TO_PUBLISH", "IN_PROGRESS", "UNPUBLISHED", "STAGE", "SYSTEM_PROBLEM"]
          if ["PUBLISHED"].include?(published_status)
            listing_data[:status] = "Active"
          elsif ["UNPUBLISHED"].include?(published_status)
            listing_data[:status] = "draft"
          elsif ["READY_TO_PUBLISH", "IN_PROGRESS", "STAGE"].include?(published_status)
            listing_data[:status] = "InProgress"
          else
            listing_data[:status] = "Inactive"
          end
        else
          listing_data[:status] = "Inactive"
        end

        listing_data
      end

      def find_shipping_policy_id(seller_data)
        shipping_id = seller_data.dig(:SellerProfiles, :SellerShippingProfile, :ShippingProfileID)
        listing_data[:shipping_id] = shipping_id if shipping_id.present?
        listing_data
      end

      def find_return_policy_id(seller_data)
        return_id = seller_data.dig(:SellerProfiles, :SellerReturnProfile, :ReturnProfileID)
        listing_data[:return_id] = return_id if return_id.present?
        listing_data
      end

      def find_payment_policy_id(seller_data)
        payment_id = seller_data.dig(:SellerProfiles, :SellerPaymentProfile, :PaymentProfileID)
        listing_data[:payment_id] = payment_id if payment_id.present?
        listing_data
      end

      def find_item_specifics(seller_data)
        listing_data[:category_name] = seller_data[:PrimaryCategory][:CategoryName] if seller_data[:PrimaryCategory]
        listing_data
      end

      def listing_variant_specific_quantity(seller_data, product)
        variant_stock_items_data = {}
        if seller_data.dig(:Variations)
          variant_array = seller_data.dig(:Variations).kind_of?(Array) ? seller_data.dig(:Variations) : [seller_data.dig(:Variations)]
          variant_array.each do |variation|
            # variant = find_variant(variation.dig(:variantGroupInfo))
            variant = Spree::Variant.find_by(sku: variation.dig(:sku))
            variant_stock_items_data[variant&.id] = {
              # "stock_item_id" => find_variant_stock_item(variant, seller_data),
              "quantity" => variation.dig(:inventory, :availToSellQty),
              "sold_quantity" => 0,
            }
          end
        else
          variant_stock_items_data[product&.master&.id] = {
            # "stock_item_id" => find_variant_stock_item(product.master, seller_data),
            "quantity" => seller_data.dig(:inventory, :availToSellQty),
            "sold_quantity" => 0,
          }
        end

        listing_data[:variant_stock_items_data] = variant_stock_items_data
        listing_data
      end

      def find_variant_stock_item(variant, seller_data)
        # TODO
        nil
        # return nil unless variant.present?
        # stock_location = Spree::StockLocation.find_by(zipcode: seller_data.dig(:PostalCode))
        # stock_item = variant.stock_items.find_by(stock_location_id: stock_location&.id) if variant&.stock_items
        # return stock_item&.id
      end

      def find_variant(variation_specifics) #[1,2]
        return nil unless product.variants.present?
        return unless variation_specific

        option_type_value_array = variation_specific[:groupingAttributes].is_a?(Array) ? variation_specific[:groupingAttributes] : [variation_specific[:groupingAttributes]]
        ebay_option_value = []
        # option_type_value_array = variation_specifics.dig(:NameValueList).kind_of?(Array) ? variation_specifics.dig(:NameValueList) : [variation_specifics.dig(:NameValueList)]
        option_type_value_array.each do |option_specific|
          option_type = store.option_types.find_by(name: option_specific[:name], presentation: option_specific[:name]&.capitalize)
          option_val = option_type.option_values.find_by(name: option_specific[:value])
          ebay_option_value << option_val
        end
        product.variants.each do |variant|
          return variant if variant.option_values && variant.option_values.to_a.pluck(:id) == ebay_option_value
        end
        return nil
      end

      def auction_pricing(seller_data)
        auction_pricing = {}
        auction_pricing[product&.master&.id] = {
          "quantity" => seller_data.dig(:Quantity),
          "auction_duration" => seller_data.dig(:ListingDuration),
          "pricing_format" => "Auction",
          "starting_bid" => seller_data.dig(:ListingDetails, :ConvertedStartPrice),
          "buy_it_now" => seller_data.dig(:BuyItNowPrice),
          "reserve_price" => seller_data.dig(:ListingDetails, :ConvertedReservePrice),
        }

        listing_data[:auction_pricing] = auction_pricing
        listing_data
      end

      def item_response_to_item_content(seller_data)
        # https://developer.walmart.com/api/us/mp/items#operation/getAllItems
        # Item response fields:
        # mart
        # sku        should be set in orderable
        # condition  should be set in visible
        # availability	string
        # wpid		string
        # upc		string   should be set in orderable
        # gtin		string should be set in orderable
        # productName		string should be set in visible
        # shelf	string
        # productType	 string
        # price	object    should be set in orderable
        # publishedStatus	string
        # additionalAttributes	object
        # unpublishedReasons	object
        # lifecycleStatus	string
        # variantGroupId	string should be set in visible
        # variantGroupInfo	object should be set in visible
        # isDuplicate	boolean
        # duplicateItemInfo	object

        item_content = { Orderable: {}, Visible: {} }
        fill_keys(seller_data, [:mart, :productType, :sku], item_content)
        fill_keys(seller_data, [:sku], item_content[:Orderable])
        mart = seller_data.dig(:mart)
        product_type = seller_data.dig(:productType)
        sku = seller_data.dig(:sku)
        gtin = seller_data.dig(:gtin)
        upc = seller_data.dig(:upc)
        if gtin.present?
          item_content[:Orderable][:productIdentifiers] = {
            productIdType: "GTIN",
            productId: gtin,
          }
        elsif upc.present?
          item_content[:Orderable][:productIdentifiers] = {
            productIdType: "UPC",
            productId: upc,
          }
        end
        item_content[:Orderable][:price] = seller_data.dig(:price, :amount) if seller_data.dig(:price).present?

        # visible
        fill_keys(seller_data, [:condition, :productName, :variantGroupId], item_content[:Visible])
        # variantGroupInfo (in seller_data) Example:
        # "variantGroupInfo" => { "isPrimary" => false, "groupingAttributes" => [{ "name" => "multipack_quantity", "value" => "100" }] } }
        if seller_data.dig(:variantGroupInfo)
          isPrimaryVariant = seller_data.dig(:variantGroupInfo, :isPrimary)
          names = seller_data.dig(:variantGroupInfo, :groupingAttributes).map do |a|
            item_content[:Visible][a[:name]] = a[:value]
            a[:name]
          end
          item_content[:Visible][:variantAttributeNames] = names
          item_content[:Visible][:isPrimaryVariant] = isPrimaryVariant ? "Yes" : "No"
        end
        item_content
      end

      def iventory_response_to_iventory_content(seller_data)
        nodes = []

        if seller_data.dig(:inventory, :nodes).present?
          seller_data.dig(:inventory, :nodes).each do |node|
            nodes << {
              "shipNode": node.with_indifferent_access[:shipNode],
              "quantity": {
                "inputQty": node.with_indifferent_access.dig(:inputQty, :amount),
                "availToSellQty": node.with_indifferent_access.dig(:availToSellQty, :amount),
                "reservedQty": node.with_indifferent_access.dig(:reservedQty, :amount),
              },
            }
          end
        end
        {
          "sku": seller_data[:sku],
          "shipNodes": nodes,
        }
      end

      def response_to_content(seller_data)
        specifics = {}
        specifics[:response_from_apis] = seller_data

        specifics[:item_content] = item_response_to_item_content(seller_data)
        specifics[:price_content] = { "sku": seller_data[:sku], "price": seller_data.dig(:price, :amount) }
        specifics[:inventory_content] = iventory_response_to_iventory_content(seller_data)
        specifics
      end

      def sale_channel_hash(seller_data, product)
        # sale_channel_hash exapmle:
        # {
        #   "primary_variant_id": "variant1_id", # This field exist only the response include variantGroupId
        #   "variants": {
        #     "variant1_id": {
        #         "sku": "xxx",
        #         "productType": "xxx",
        #         "response_from_apis": {},
        #         "item_content": {
        #           "orderable": {},
        #           "visible": {}
        #         },
        #         "price_content": {
        #           "sku": "", # The sku here is not requied, because we can get from variant1_id.sku
        #           "price": 4.99
        #         },
        #         "inventory_content":{
        #           "sku": "xxx", # The sku here is not requied, because we can get from variant1_id.sku
        #           "shipNodes": [{"shipNode": "10000003527", "quantity": { "inputQty": 1, "availToSellQty": 1, "reservedQty": 0 }}]
        #         }
        #     },
        #     "variant2_id": ...
        #   }
        # }
        sale_channel_hash = { variants: {} }
        product_type = ""

        master_variant = product.master
        variations = seller_data.dig(:Variations)
        sale_channel_hash[:primary_variant_id] = master_variant.id if variations.present?

        if variations.present?
          variations.each do |variation|
            variant = Spree::Variant.find_by(sku: variation.dig(:sku))
            sale_channel_hash[:variants][variant.id] = response_to_content(variation) if variant.present?
            product_type = variation.dig(:productType) if product_type.blank?
          end
        else
          product_type = seller_data.dig(:productType)
          sale_channel_hash[:variants][master_variant.id] = response_to_content(seller_data)
        end

        product_type_in_db = Spree::WalmartProductType.where(name: product_type).first if product_type.present?

        if product_type.present? && product_type_in_db.present?
          sale_channel_hash[:product_type] = product_type_in_db.id
          sale_channel_hash[:product_group] = product_type_in_db.group.id
          sale_channel_hash[:category] = product_type_in_db.category.id
          listing_data[:category_id] = product_type_in_db.category.id
        end

        listing_data[:sale_channel_hash] = sale_channel_hash
        listing_data
      end

      # group items by variantGroupId
      def group_items_by_vgi
        @items_grouped = { with_vgi: {}, without_vgi: [] }
        inventories = {}
        response_hash.with_indifferent_access[:inventories][:inventories].each do |inventory|
          # Example:
          # { "sku" => "44450E", "nodes" => [
          #   {
          #     "shipNode" => "10002537369",
          #     "inputQty" => { "unit" => "EACH", "amount" => 10 },
          #     "availToSellQty" => { "unit" => "EACH", "amount" => 10 },
          #     "reservedQty" => { "unit" => "EACH", "amount" => 0 },
          #   },
          # ] }
          next if inventory[:sku].blank?

          inv = inventory[:nodes]

          inputQty = 0
          availToSellQty = 0
          reservedQty = 0
          inventory[:nodes].each do |node|
            inputQty += node.dig(:inputQty, :amount).to_i
            availToSellQty += node.dig(:availToSellQty, :amount).to_i
            reservedQty += node.dig(:reservedQty, :amount).to_i
          end
          inv = {}
          inv[:inputQty] = inputQty
          inv[:availToSellQty] = availToSellQty
          inv[:reservedQty] = reservedQty
          inv[:nodes] = inventory[:nodes]
          inventories[inventory[:sku]] = inv
        end

        [response_hash.with_indifferent_access[:items][:ItemResponse]].flatten.each do |seller_data|
          next if seller_data[:sku].blank?

          variantGroupId = seller_data.dig(:variantGroupId)
          seller_data[:inventory] = inventories[seller_data[:sku]]
          if variantGroupId.present?
            @items_grouped[:with_vgi][variantGroupId] = [] if @items_grouped.dig(:with_vgi, variantGroupId).blank?
            @items_grouped[:with_vgi][variantGroupId] << seller_data
          else
            @items_grouped[:without_vgi] << seller_data
          end
        end
      end

      private

      def fill_keys(src, keys, dst)
        return unless keys.is_a?(Array)
        return unless src.is_a?(Hash) && dst.is_a?(Hash)

        keys.each do |key|
          dst[key] = src[key] if src[key].present?
        end
      end
    end
  end
end
