module Connector
  module Amazon
    class ReturnReport < Connector::Amazon::Base
      attr_accessor :sale_channel_id, :oauth_application, :response_hash

      def format_return_response(oauth_application_id)
        @oauth_application = Spree::OauthApplication.find(oauth_application_id)
        @sale_channel_id = oauth_application.sale_channel_id
        @response_hash_data = @response_hash.force_encoding('ISO-8859-1').encode('UTF-8')
        Rails.logger.info("=====entering format_return_response 1: #{@response_hash_data}")
        response_array = @response_hash_data.split("\n")
        Rails.logger.info("=====entering format_return_response 2: #{response_array}")
        headers = response_array.first.split("\t")
        Rails.logger.info("=====entering format_return_response 3: #{headers}")
        
        response_array.drop(1).each do |data_row|
          data_row = data_row.split("\t")
          return_hash = {}
          headers.each_with_index do |h, idx|
            return_hash = return_hash.merge(h => data_row[idx])
          end
          order_number = return_hash['Order ID']
          next if order_number.nil?
          order = Spree::Order.find_by(number: order_number)
          next if order.nil?
          next if order.state == 'returned'
          # next if order.return_authorizations.any?

          event_type = 'AMAZON_RETURN'
          normalized_data = Return::AmazonReturnNormalizer.normalize(return_hash)
          Return::HandleReturnWebhook.new(order_data: normalized_data, event_type: event_type).call
        end
      end
      
    end
  end
end
