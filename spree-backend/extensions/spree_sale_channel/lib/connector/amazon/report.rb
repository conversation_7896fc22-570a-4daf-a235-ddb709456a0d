module Connector
  module Amazon
    class Report < Connector::Amazon::Base
      include ::Spree::Admin::ElasticsearchHelper

      attr_accessor :sale_channel_id, :oauth_application, :response_hash, :seller_list_data, :listing_data, :store, :product, :api_response

      def format_listing_response(oauth_application_id)
        @oauth_application = Spree::OauthApplication.find(oauth_application_id)
        @sale_channel_id = oauth_application.sale_channel_id
        @response_hash_data = @response_hash.force_encoding('ISO-8859-1').encode('UTF-8')
        response_array = @response_hash_data.split("\n")
        headers = response_array.first.split("\t")
        sale_channel_hash = {}

        response_array.drop(1).each do |data_row|
          import_log = Spree::ImportLog.create(store_id: store.id)
          data_row = data_row.split("\t")
          headers.each_with_index do |h, idx|
            sale_channel_hash = sale_channel_hash.merge(h => data_row[idx])
          end

          seller_data = seller_data_hash(sale_channel_hash)
          @product = SpreeInsertion::CreateProduct::AmazonProduct.new(seller_data, store, import_log).make

          listing = get_spree_listing_hash(seller_data, product, sale_channel_hash)
          next unless listing
          created_listing = SpreeInsertion::AmazonListing.new(listing, store.id).execute

          if created_listing || !created_listing.sale_channel_hash
            created_listing.update(sale_channel_hash: @api_response) if @api_response.present?
          end

          if created_listing || !created_listing.sale_channel_metadata
            created_listing.update(sale_channel_metadata: sale_channel_hash) if sale_channel_hash.present?
          end
        end

        helper_sync_product_to_elasticsearch
      end

      def seller_data_hash(sale_channel_hash)
        @listing_data = {
          title: sale_channel_hash['item-name'],
          sku: sale_channel_hash['seller-sku'],
          quantity: sale_channel_hash['quantity'],
          item_id: sale_channel_hash['listing-id'],
          sale_channel_id: sale_channel_id,
          store_id: store.id,
          currency: store.default_currency,
          sale_channel_hash: sale_channel_hash,
          status: sale_channel_hash['status']
        }
        description_field(sale_channel_hash)
        set_weight(sale_channel_hash)
        build_image_hash

        listing_data
      end

      def get_spree_listing_hash(seller_data, product, sale_channel_hash)
        response = ::Amazon::CatalogApis::GetCatalogItem.new(store.id, oauth_application.id).get_attribute(sale_channel_hash['asin1'])
        if response.nil?
          return nil
        end

        product_type = response.dig("productTypes",0, "productType")
        category_name = product_type.split('_').map(&:capitalize).join(' ')
        category_id =  "#{product_type}_#{category_name}"
        if product.present?
          seller_data.merge(product_id: product&.id, stock_item_id: product&.stock_items&.first&.id, category_id: category_id, category_name: category_name)
        else
          seller_data.merge(category_id: category_id, category_name: category_name)
        end
      end

      def build_image_hash
        return if api_response.dig('errors').present?

        # Create the nested images hash
        listing_data['images'] = {}

        # Extracting main product image locator
        listing_data['images']['main_product_image_locator'] = api_response&.dig("attributes", "main_product_image_locator", 0, "media_location")

        # Extracting other product image locators
        api_response["attributes"].each do |key, value|
          if key.start_with?("other_product_image_locator")
            index = key.split('_').last
            listing_data['images']["other_product_image_locator_#{index}"] = value[0]["media_location"]
          end
        end
      end

      def description_field(sale_channel_hash)
        listing_data[:description] = sale_channel_hash['item-description'] unless sale_channel_hash['item-description'].nil?
        listing_data
      end

      def set_weight(sale_channel_hash)
        sku = sale_channel_hash['seller-sku']
        @api_response = ::Amazon::ListingApis::GetListingItem.new(store.id, oauth_application.id).get_listing_item(sku)
        weight_array = api_response&.dig('attributes', 'item_package_weight')
        return if weight_array.nil?

        weight = weight_array&.first&.[]('value')

        lb = weight.to_i

        # Calculate ounces
        oz_decimal = (weight - lb) * 16
        oz = oz_decimal.round(2)
        listing_data[:sale_channel_hash]['lb'] = lb
        listing_data[:sale_channel_hash]['oz'] = oz

        listing_data
      end
    end
  end
end
