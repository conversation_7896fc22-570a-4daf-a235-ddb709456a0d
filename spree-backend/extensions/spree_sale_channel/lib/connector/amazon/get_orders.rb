module Connector
  module Amazon
    class GetOrders < Connector::Amazon::Base

      def format_order_response(oauth_application)
        response_hash.with_indifferent_access.dig(:payload, :Orders)&.each do |order_data|
          begin
            store = oauth_application&.store
            import_log = Spree::ImportLog.create(store_id: store.id)
            SpreeInsertion::Amazon::CreateOrder.new(order_data, store, oauth_application, import_log).make
            sleep(1) # Sleep for a short time to avoid hitting API rate limits
          rescue => e
            Rails.logger.error("Amazon format_order_response failed: #{e.message}")
          end
        end
      end
    end
  end
end