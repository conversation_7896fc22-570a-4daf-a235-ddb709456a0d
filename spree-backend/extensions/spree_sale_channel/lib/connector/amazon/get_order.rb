module Connector
  module Amazon
    class GetOrder < Connector::Amazon::Base

      def format_specific_order_response(oauth_application)
        order_data = response_hash.with_indifferent_access.dig(:payload)
        begin
          store = oauth_application&.store
          import_log = Spree::ImportLog.create(store_id: store.id)
          SpreeInsertion::Amazon::CreateOrder.new(order_data, store, oauth_application, import_log).make
        rescue => e
          Rails.logger.error("Amazon format_specific_order_response failed: #{e.message}")
        end
      end
    end
  end
end