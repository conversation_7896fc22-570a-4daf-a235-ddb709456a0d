module Connector
  module Ebay
    class Trading < Connector::Ebay::Base
      include ::Spree::Admin::ElasticsearchHelper

      attr_accessor :listing_data, :condition_list, :seller_list_data, :product, :sale_channel_id, :oauth_application

      def format_listing_response(status, oauth_application)
        @sale_channel_id = oauth_application.sale_channel_id
        @oauth_application = oauth_application
        @condition_list = []
        [response_hash.with_indifferent_access[:GetSellerListResponse][:ItemArray][:Item]].flatten.each do |seller_data|
          next if status.present? && seller_data.dig(:SellingStatus, :ListingStatus) != "Active"
          @seller_list_data = []
          @product = SpreeInsertion::CreateProduct::Product.new(seller_data, store, import_log).make
          # For creating listing records in spree
          if product&.id?
            listing = get_spree_listing_hash(seller_data, product)
            SpreeInsertion::Listing.new(listing, store).execute(seller_data)
          end
        end

        helper_sync_product_to_elasticsearch
        return condition_list.uniq
      end

      def get_spree_listing_hash(seller_data, product)
        if seller_data[:ConditionID]
          condition_list.push({
            name: seller_data[:ConditionDisplayName],
            condition_id: seller_data[:ConditionID],
            store_id: store.id
          })
        end

        @listing_data = {
          title: seller_data[:Title],
          sku: seller_data[:SKU],
          # quantity: seller_data[:Quantity],
          currency: seller_data[:Currency],
          item_id: seller_data[:ItemID],
          product_id: product.id,
          store_id: store.id,
          stock_item_id: product.stock_items&.first&.id,
          item_url: seller_data.dig(:ListingDetails, :ViewItemURL),
          sale_channel_id: sale_channel_id
        }

        description_field(seller_data)
        listing_allow_offer_details(seller_data)
        listing_details(seller_data)
        category_details(seller_data)
        listing_status(seller_data)
        listing_variant_specific_quantity(seller_data)
        find_shipping_policy_id(seller_data)
        find_return_policy_id(seller_data)
        find_payment_policy_id(seller_data)
        find_item_specifics(seller_data)
        auction_pricing(seller_data) if seller_data.dig(:ListingType) == "Chinese"

        seller_list_data.push(listing_data)
      end

      def description_field(seller_data)
        listing_data[:description] = seller_data[:Description] unless seller_data[:Description].nil?
        listing_data
      end

      def listing_allow_offer_details(seller_data)
        if seller_data[:BestOfferDetails] && seller_data[:BestOfferDetails][:BestOfferEnabled]
          allow_offer = seller_data.dig(:BestOfferDetails, :BestOfferEnabled)
          listing_data[:allow_offer] = allow_offer

          minOfferPrice = seller_data.dig(:ListingDetails, :MinimumBestOfferPrice)
          listing_data[:minimum_offer_price] = minOfferPrice.to_f unless minOfferPrice.nil?

          autoacceptOfferPrice = seller_data.dig(:ListingDetails, :BestOfferAutoAcceptPrice)
          listing_data[:autoaccept_offer_price] = autoacceptOfferPrice.to_f unless autoacceptOfferPrice.nil?
        else
          listing_data[:allow_offer] = false
        end
        listing_data
      end

      def listing_details(seller_data)
        listing_data.merge!({
          start_time: seller_data[:ListingDetails][:StartTime],
          end_time: seller_data[:ListingDetails][:EndTime],
          item_url: seller_data[:ListingDetails][:ViewItemURL]
        }) if seller_data[:ListingDetails]
      end

      def category_details(seller_data)
        category_id = seller_data.dig(:PrimaryCategory, :CategoryID)
        listing_data[:category_id] = category_id unless category_id.nil?
        listing_data
      end

      def listing_status(seller_data)
        status = seller_data.dig(:SellingStatus, :ListingStatus)
        listing_data[:status] = status unless status.nil?
        listing_data
      end

      def find_shipping_policy_id(seller_data)
        shipping_id = seller_data.dig(:SellerProfiles, :SellerShippingProfile, :ShippingProfileID)
        listing_data[:shipping_id] = shipping_id if shipping_id.present?
        listing_data
      end

      def find_return_policy_id(seller_data)
        return_id = seller_data.dig(:SellerProfiles, :SellerReturnProfile, :ReturnProfileID)
        listing_data[:return_id] = return_id if return_id.present?
        listing_data
      end

      def find_payment_policy_id(seller_data)
        payment_id = seller_data.dig(:SellerProfiles, :SellerPaymentProfile, :PaymentProfileID)
        listing_data[:payment_id] = payment_id if payment_id.present?
        listing_data
      end

      def find_item_specifics(seller_data)
        listing_data[:category_name] = seller_data[:PrimaryCategory][:CategoryName] if seller_data[:PrimaryCategory]
        listing_data
      end

      def listing_variant_specific_quantity(seller_data)
        variant_stock_items_data = {}
        if seller_data.dig(:Variations)
          variant_array = seller_data.dig(:Variations, :Variation).kind_of?(Array) ? seller_data.dig(:Variations, :Variation) : [seller_data.dig(:Variations, :Variation)]
          variant_array.each do |variation|
            variant = find_variant(variation.dig(:VariationSpecifics))
            variant_stock_items_data[variant&.id] = {
              'stock_item_id' => find_variant_stock_item(variant, seller_data),
              'quantity' => variation.dig(:Quantity),
              'sold_quantity' => variation.dig(:SellingStatus, :QuantitySold)
            }
          end
        else
          variant_stock_items_data[product&.master&.id] = {
            'stock_item_id' => find_variant_stock_item(product.master, seller_data),
            'quantity' => seller_data.dig(:Quantity),
            'sold_quantity' => seller_data.dig(:SellingStatus, :QuantitySold)
          }
        end

        listing_data[:variant_stock_items_data] = variant_stock_items_data
        listing_data
      end

      def find_variant_stock_item(variant, seller_data)
        return nil unless variant.present?
        stock_location = Spree::StockLocation.find_by(zipcode: seller_data.dig(:PostalCode))
        stock_item = variant.stock_items.find_by(stock_location_id: stock_location&.id) if variant&.stock_items
        return stock_item&.id
      end

      def find_variant(variation_specifics) #[1,2]
        return nil unless product.variants.present?
        ebay_option_value = []

        option_type_value_array = variation_specifics.dig(:NameValueList).kind_of?(Array) ? variation_specifics.dig(:NameValueList) : [variation_specifics.dig(:NameValueList)]
        option_type_value_array.each do |name_value_list|
          ebay_option_value = ebay_option_value + [Spree::OptionValue.find_by(name: name_value_list.dig(:Value))&.id]
        end
        product.variants.each do |variant|
         return variant if variant.option_values && variant.option_values.to_a.pluck(:id) == ebay_option_value
        end
        return nil
      end

      def auction_pricing(seller_data)
        auction_pricing = {}
        auction_pricing[product&.master&.id] = {
          'quantity' => seller_data.dig(:Quantity),
          'auction_duration' => seller_data.dig(:ListingDuration),
          'pricing_format' => 'Auction',
          'starting_bid' => seller_data.dig(:ListingDetails, :ConvertedStartPrice),
          'buy_it_now' => seller_data.dig(:BuyItNowPrice),
          'reserve_price' => seller_data.dig(:ListingDetails, :ConvertedReservePrice)
        }

        listing_data[:auction_pricing] = auction_pricing
        listing_data
      end
    end
  end
end
