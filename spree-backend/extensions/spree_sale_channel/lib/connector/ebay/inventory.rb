module Connector
  module Ebay
    class Inventory < Connector::Ebay::Base
      attr_accessor :location_data

      def format_inventory_location_response
        stock_location_data = []
        return unless response_hash.present?
        response_hash.with_indifferent_access["locations"].each_with_index do |location, index|
          @location_data = {
              name: location[:name].present? ? ("Ebay_" + location[:name].to_s + index.to_s) : "Ebay_#{index}",
              active: location[:merchantLocationStatus] == 'ENABLED' ? true : false,
              ebay: {merchantLocationKey: location["merchantLocationKey"]},
              channel: 0
          }

          location_field(location)
          stock_location_data.push(location_data)
        end
        stock_location_data
      end

      def location_field(location)
        if location['location'] && location['location']['address']
          data = location['location']['address']
          location_data.merge!({
            address1: data['addressLine1'] || '2782 Courtright Street',
            address2: data['addressLine2'] || '2782 Courtright Street',
            city: data['city'] || 'Medora',
            country_id: find_country_id(data['country']) || '224',
            state_id: find_state_id(data['stateOrProvince'], data['country'] ) || '483',
            zipcode: data[:postalCode] || '35242',
            store_id: store.id
          })
        else
          location_data.merge!({
            address1: '2782 Courtright Street',
            address2: '2782 Courtright Street',
            city: 'Medora',
            state_id: '483',
            country_id: '224',
            zipcode: '35242',
            store_id: store.id
          })
        end
      end

      def find_country_id(data)
        return unless data.present?
        country_data = ::Spree::Country.find_by(iso: data)&.id
        country_data
      end

      def find_state_id(state_abbr, country_iso)
        return unless state_abbr.present?
        state_id = ::Spree::State.find_by(abbr: state_abbr, country_id: find_country_id(country_iso) )&.id
        state_id
      end 
    end
  end
end