module Connector
  module Ebay
    class GetCategorySuggestion < Connector::Ebay::Base

      def format_taxon_suggestion_response
        return false unless response_hash

        response_hash.with_indifferent_access[:categorySuggestions].each_with_object({}) do |category_data, m|
          id = category_data.dig(:category, :categoryId)
          name = category_data.dig(:category, :categoryName)
          m[id] = name
        end
      end
    end
  end
end
