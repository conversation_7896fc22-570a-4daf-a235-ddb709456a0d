module Connector
  module Ebay
    class GetTransaction < Connector::Ebay::Base
      def call
        order_data = {}
        eBay_fee = {}
        return false unless response_hash

        response_hash.with_indifferent_access[:transactions].each do |transaction|
          if transaction['feeType'] == 'AD_FEE'
            eBay_fee['ad_fee'] = eBay_fee['ad_fee'] || {}
            item_id = ''
            transaction.dig('references').each do |reference|
              next if reference.dig('referenceType') != "ITEM_ID"
              item_id = reference.dig('referenceId')
            end
            eBay_fee['ad_fee'][item_id] = transaction.dig('amount', 'value').to_f
          else
            order_data['order_id'] = transaction['orderId']
            transaction['orderLineItems']&.each do |order_line_item|
              final_marketplace_hash = {}
              order_line_item.dig('marketplaceFees').each do |marketplace_fee|
                internal_hash = {}
                internal_hash[marketplace_fee['feeType']] = marketplace_fee.dig('amount', 'value').to_f
                final_marketplace_hash.merge!(internal_hash)
              end
              eBay_fee[order_line_item.dig('lineItemId')] = final_marketplace_hash
            end
          end
        end
        order_data = order_data.merge(eBay_fee)
        SpreeInsertion::EbayFees.new(order_data, store).execute(order_data)
      end
    end
  end
end
