module RecordInserter
  module Listing
    class Inserter
      include ::WalmartHelper
      attr_reader :selller_data_hash, :seller_data, :store_id, :sale_channel, :listing, :listing_data, :sku_hash

      def initialize(selller_data_hash, seller_data, store_id, sale_channel)
        @selller_data_hash = selller_data_hash
        @seller_data = seller_data
        @listing_data = seller_data.dig(:listing)
        @store_id = store_id
        @sale_channel = sale_channel
        @sku_hash = build_sku_hash(seller_data)
        @listing = listing_finder(seller_data, sale_channel, sku_hash)
      end

      def call
        insert_record
      end

      private

      def insert_record
        if walmart_sale_channel?
          variant_group_id = seller_data[:variantGroupId]
          @selller_data_hash.each do |list|
            process_walmart_listing(list, variant_group_id)
          end
        else
          process_other_channel
        end
      end

      def process_walmart_listing(list, variant_group_id)
        listing = get_walmart_listing(sale_channel.id, sku_hash.keys, variant_group_id, list.dig(:listing, :item_id))
        if listing.present?
          # list[:listing][:sale_channel_hash] = SaleChannelHashMerger.new(listing.sale_channel_hash, list.dig(:listing, :sale_channel_hash)).merge
          list[:listing][:sale_channel_hash] = merge_sale_channel_hash(listing.sale_channel_hash, list.dig(:listing, :sale_channel_hash))
          listing.update(list.dig(:listing))
          update_fields_from_walmart_report(listing, sku_hash.keys)
          ::RecordInserter::ListingInventory::Inserter.new(listing, @seller_data).call if listing.status == "Active"
        else
          create_new_walmart_listing(list)
        end
      end

      def merge_sale_channel_hash(original_hash_in, new_hash_in)
        original_hash = JSON.parse(original_hash_in.to_h.to_json)
        new_hash = JSON.parse(new_hash_in.to_h.to_json)
        return new_hash if original_hash.blank?
        return original_hash if new_hash.blank?
        excluded_keys = ["variants"]
        original_hash_without_variants = original_hash.reject { |k, v| excluded_keys.include?(k) }
        new_hash_without_variants = new_hash.reject { |k, v| excluded_keys.include?(k) }
        ret = original_hash_without_variants.merge(new_hash_without_variants)
        new_variants = new_hash.dig("variants").to_h
        new_variants = original_hash.dig("variants").merge(new_variants) if original_hash.dig("variants")
        original_hash.dig("variants").each_key do |variant_id|
          if original_hash.dig("variants", variant_id, "item_content", "Orderable", "ShippingWeight").present?
            if new_variants.dig(variant_id, "item_content", "Orderable").present?
              new_variants[variant_id]["item_content"]["Orderable"]["ShippingWeight"] =
                original_hash.dig("variants", variant_id, "item_content", "Orderable", "ShippingWeight")
            end
          end
        end

        ret["variants"] = new_variants.present? ? new_variants : {}

        ret
      end

      def update_fields_from_walmart_report(listing, skus = [])
        return if listing.status != "Active"

        item_images = { PictureURL: [] }
        return if skus.blank? || listing.blank?

        listing.oauth_application.walmart_report_contents.where(sku: skus).each do |report_content|
          item_images[:PictureURL] << report_content.primary_image_url if report_content.primary_image_url.present?
          if listing.walmart_primary_sku == report_content.sku || listing.walmart_primary_sku.blank?
            listing.update_columns(item_url: report_content.item_url) if report_content.item_url.present?
          end
          weight = report_content.shipping_weight
          listing.update_walmart_shipping_weight(report_content.sku, weight.to_f) if weight.present?
        end
        update_listing_images(listing, item_images) if item_images[:PictureURL].present?
        listing.save!
      end

      def listing_walmart_images(listing, skus = [])
        item_images = { PictureURL: [] }
        listing.oauth_application.walmart_report_contents.where(sku: skus).each do |report_content|
          item_images[:PictureURL] << report_content.primary_image_url if report_content.primary_image_url.present?
        end
        item_images
      end

      def update_listing_images(listing, img_urls)
        return if listing.product.nil?
        images = img_urls[:PictureURL]&.kind_of?(Array) ? img_urls[:PictureURL] : [img_urls[:PictureURL]]
        if images.any?
          images.each do |img|
            begin
              ActiveRecord::Base.transaction do
                image = URI.parse(img)
                image_name = File.basename(image.path)
                option_text = "master"
                filename = "#{option_text}_#{listing.product&.master&.id}_#{image_name}"
                unless listing.image_files_blobs.where(filename: filename).any?
                  image_file = image.open
                  listing.image_files.attach(io: image_file, filename: filename)
                end
              end
            rescue StandardError => e
              next
            end
          end
        end
      end

      def create_new_walmart_listing(list)
        new_listing = Spree::Listing.create!(list.dig(:listing))
        update_fields_from_walmart_report(new_listing, sku_hash.keys)
        ::RecordInserter::ListingInventory::Inserter.new(new_listing, seller_data).call
      end

      def process_other_channel
        if listing.present?
          listing.update(listing_data)
          create_condition_list
          update_listing_images(listing, seller_data[:images]) if seller_data[:images].present?
          RecordInserter::ListingInventory::Inserter.new(listing, seller_data).call
        else
          create_new_listing
          create_condition_list
        end
      end

      def create_new_listing
        new_listing = Spree::Listing.create!(listing_data)
        RecordInserter::ListingInventory::Inserter.new(new_listing, seller_data).call
        update_listing_images(new_listing, seller_data[:images]) if seller_data[:images].present?
      end

      def create_condition_list
        return unless seller_data.dig(:condition_list)

        condition = seller_data.dig(:condition_list)
        Spree::Condition.find_or_create_by(name: condition[:name], condition_id: condition[:condition_id], store_id: condition[:store_id], sale_channel_id: sale_channel.id)  
      end

      def walmart_sale_channel?
        sale_channel.brand == "walmart"
      end
    end
  end
end
