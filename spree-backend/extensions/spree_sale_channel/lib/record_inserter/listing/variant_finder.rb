module RecordInserter
  module Listing
    class VariantFinder
      attr_reader :product, :option_type_value_array

      def initialize(product, option_type_value_array)
        @product = product
        @option_type_value_array = if option_type_value_array.is_a?(Array) 
                                    option_type_value_array
                                  else
                                    [option_type_value_array.transform_keys { |key| key.to_s.downcase.to_sym }]
                                  end
      end

      def call
        return nil if option_type_value_array.blank? || product.nil?

        ebay_option_value = []

        # Process option_type_value_array to get the option values
        option_type_value_array.each do |name_value_list|
          next if name_value_list.nil? || name_value_list[:name].nil? || name_value_list[:value].nil?

          option_type_id = Spree::OptionType.find_by(name: name_value_list.dig(:name))
          option_value_id = Spree::OptionValue.find_by(name: name_value_list.dig(:value), option_type_id: option_type_id)&.id

          ebay_option_value += [option_value_id] if option_value_id.present?
        end

        # Find the variant based on the option values
        product.variants.each do |variant|
          return variant if variant.option_values&.pluck(:id) == ebay_option_value
        end

        nil
      end
    end
  end
end

