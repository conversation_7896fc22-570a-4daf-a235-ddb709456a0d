module RecordInserter
  module ListingInventory
    class Inserter
      def initialize(listing, seller_data)
        @listing = listing
        @sale_channel = @listing.sale_channel
        @sale_channel_brand = @listing.sale_channel&.brand
        @seller_data = seller_data
        @product = listing.product
      end

      def call
      	return if @product.nil?

        if @product.variants.present? && @seller_data[:variations]
          variant_array = Array(@seller_data[:variations])
          variant_array.each do |variant|
            # existing_variant = RecordInserter::Listing::VariantFinder.new(@product, find_variant_params(variant)).call That was used in previous walmart case
            existing_variant = RecordInserter::Listing::VariantFinder.new(@product, variant.dig(:additional_data, :variation_specifics)).call
            sync_listing_sold_qty(existing_variant, extract_listed_qty(variant), extract_price(variant), extract_sold_qty(variant))
            # sync_listing_sold_qty(listing, existing_variant, variant.dig(:inventory, :availToSellQty).to_i, variant.dig(:price, :amount))
          end
        else
          sync_listing_sold_qty(@product.master, @seller_data.dig(:listing, :quantity).to_i, @seller_data.dig(:inventory, :price).to_f, @seller_data.dig(:inventory, :sold_quantity).to_i)
        end
      end

      private

      def walmart_sale_channel?
      	@sale_channel_brand == "walmart"
      end

      def extract_listed_qty(data)
        walmart_sale_channel? ? data.dig(:inventory_data, :input_inventory_quantity).to_i : data.dig(:variant, :quantity).to_i
      end

      def extract_price(data)
        walmart_sale_channel? ? data.dig(:inventory_data, :price).to_f : data.dig(:variant, :price).to_f
      end

      def extract_sold_qty(data)
        walmart_sale_channel? ? data.dig(:inventory_data, :sold_inventory_quantity).to_i : data.dig(:variant ,:sold_quantity).to_i
      end

      def extract_quantity(data)
        walmart_sale_channel? ? data.dig(:inventory_data, :input_inventory_quantity).to_i : data[:Quantity].to_i
      end

      # def find_variant_params(data)
      #   walmart_sale_channel? ? data.dig(:additional_data, :variation_specifics) : variant[:VariationSpecifics]
      # end

      def sync_listing_sold_qty(variant, qty, price, sold_qty = 0)
        return unless @listing.present? && variant.present?

        listing_inventory = @listing.listing_inventories.find_or_create_by(variant_id: variant.id)
        listing_inventory.update(total_listed_qty: qty, total_sold_qty: sold_qty, price: price) if listing_inventory.present?
      end
    end
  end
end
