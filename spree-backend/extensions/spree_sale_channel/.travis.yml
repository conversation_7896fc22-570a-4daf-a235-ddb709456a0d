os: linux
dist: bionic

addons:
  apt:
    sources:
      - google-chrome
    packages:
      - google-chrome-stable

services:
  - mysql
  - postgresql

language: ruby

rvm:
  - 2.7
  - 3.0

env:
  - DB=mysql
  - DB=postgres

before_install:
  - mysql -u root -e "GRANT ALL ON *.* TO 'travis'@'%';"

before_script:
  - CHROME_MAIN_VERSION=`google-chrome-stable --version | sed -E 's/(^Google Chrome |\.[0-9]+ )//g'`
  - CHROMEDRIVER_VERSION=`curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_MAIN_VERSION"`
  - curl "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip" -O
  - unzip chromedriver_linux64.zip -d ~/bin
  - nvm install 16

script:
  - bundle exec rake test_app
  - bundle exec rspec
