# frozen_string_literal: true

module Admin
  class BatchUpdateStockItemUnitCommand
    prepend SimpleCommand

    attr_reader :product, :stock_item_unit_id, :attributes, :current_user, :action_place

    def initialize(product:, stock_item_unit_id:, attributes:, current_user:, action_place:)
      @product = product
      @stock_item_unit_id = stock_item_unit_id
      @attributes = attributes
      @current_user = current_user
      @action_place = action_place
    end

    def call
      if remove?
        command = ::Admin::StockRemovalCheckCommand.call(product:, stock_item_units:)
        errors.add_multiple_errors(command.errors) unless command.success?
        return if errors.present?
      end

      ApplicationRecord.transaction do
        restore_locked if lock?
        restore_stock_from_removed if restore_removed?

        stock_item_units.each do |unit|
          prev_state = unit.state
          unit.update!(attributes.except(:from_state))
          record_activity_log(unit, previous_state: prev_state)
        end
      end
    end

    private

    def stock_item_units
      scope = Spree::StockItemUnit.joins(stock_item: :variant).where(spree_variants: { product_id: product.id })
      scope = scope.where(id: stock_item_unit_id)
      if lock? || remove?
        scope = scope.where(state: :stock)
      elsif from_state.present?
        scope = scope.where(state: from_state)
      end
      scope
    end

    def lock?
      attributes[:state].to_s == "locked"
    end

    def remove?
      attributes[:state].to_s == "removed"
    end

    def stock?
      attributes[:state].to_s == "stock"
    end

    def restore_removed?
      attributes[:state].to_s == "stock" && from_state == "removed"
    end

    def from_state
      attributes[:from_state].to_s
    end

    def restore_locked
      line_item_id = attributes[:line_item_id]
      shipment_id = attributes[:shipment_id]

      return if line_item_id.blank? || shipment_id.blank?

      stock_item_units = product.stock_item_units.where(line_item_id:, shipment_id:, state: :locked)

      stock_item_units.each do |unit|
        unit.update!(state: :stock, line_item_id: nil, shipment_id: nil)
        record_activity_log(unit, previous_state: 'locked')
      end
    end

    def restore_stock_from_removed
      stock_item_units = product.stock_item_units.where(id: attributes[:stock_item_unit_id], state: :removed)

      stock_item_units.each do |unit|
        unit.update!(state: :stock, line_item_id: nil, shipment_id: nil)
        record_activity_log(unit, previous_state: 'removed')
      end
    end

    def record_activity_log(stock_item_unit, previous_state: nil)
      action_key = resolve_stock_item_unit_action(stock_item_unit.state, previous_state)
      ::Spree::ActivityLog.create!(
        loggable: stock_item_unit,
        user_id: current_user.id,
        action_name: stock_item_unit.number,
        role: current_user.spree_roles.first&.name,
        date: Time.current,
        email: current_user.email,
        action_place: action_place,
        action: ::Spree::ActivityLogActions.get(:stock_item_unit, action_key),
        product_id: product&.id
      )
    end

    def resolve_stock_item_unit_action(current_state, previous_state)
      case current_state
      when "removed" then :remove
      when "locked"  then :lock
      when "stock"
        case previous_state
        when "removed" then :restore_removed
        when "locked"  then :restore_locked
        else :add
        end
      else
        :edit
      end
    end
  end
end
