# frozen_string_literal: true

module Admin
  class BatchCreateStockItemUnitCommand
    prepend SimpleCommand

    attr_reader :stock_item_unit_params, :current_user, :action_place

    def initialize(stock_item_unit_params:, current_user:, action_place:)
      @stock_item_unit_params = stock_item_unit_params
      @current_user = current_user
      @action_place = action_place
    end

    def call
      return {} if stock_item_unit_params.blank?

      ApplicationRecord.transaction do
        stock_item_units.each do |stock_item, units|
          stock_movement = build_stock_movement(stock_item: stock_item, quantity: units.size)
          unless stock_movement.save
            errors.add_multiple_errors(stock_movement.errors.messages)
            raise ActiveRecord::Rollback
          end
          units.each do |unit|
            unit.pack_size = (unit.pack_size.presence || 1)
            unless unit.save
              errors.add(:stock_unit, "failed to create")
              raise ActiveRecord::Rollback
            end
            record_activity_log(unit) if unit.previous_changes.key?('id')
          end
        end
        stock_item_units
      end
    end

    private

    def record_activity_log(stock_item_unit)
      ::Spree::ActivityLog.create!(
        loggable: stock_item_unit,
        user_id: current_user.id,
        action_name: stock_item_unit.number,
        role: current_user.spree_roles.first&.name,
        date: Time.current,
        email: current_user.email,
        action_place: action_place,
        action: ::Spree::ActivityLogActions.get(:stock_item_unit, :add),
        product_id: stock_item_unit.stock_item&.product&.id
      )
    end

    def stock_item_units
      @stock_item_units ||= stock_item_unit_params.map do |attrs|
        stock_item = Spree::StockItem.find(attrs[:stock_item_id])
        stock_item.stock_item_units.build(sanitize(attrs))
      end.group_by(&:stock_item)
    end

    def build_stock_movement(stock_item:, quantity:)
      stock_location = stock_item.stock_location
      stock_location.stock_movements.build(quantity: quantity, stock_item: stock_item)
    end

    def sanitize(attrs)
      attrs = ActionController::Parameters.new(attrs) if attrs.is_a?(Hash)
      attrs.permit(
        :vendor_receipt_number,
        :vendor_inventory_number,
        :vendor_inventory_cost,
        :vendor_receipt_date,
        :expiry_date,
        :remark,
        :number,
        :pack_size,
        :expiry_type,
      )
    end
  end
end
