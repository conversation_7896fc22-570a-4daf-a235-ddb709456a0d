# frozen_string_literal: true

module Admin
  class BatchCreateStockItemCommand
    prepend SimpleCommand

    attr_reader :product, :stock_item_params, :stock_items, :current_user, :action_place

    def initialize(product:, stock_item_params:, current_user:, action_place:)
      @product = product
      @stock_item_params = stock_item_params
      @stock_items = {}
      @current_user = current_user
      @action_place = action_place
    end

    def call
      return if stock_item_params.blank?

      ApplicationRecord.transaction do
        stock_item_params.to_unsafe_h.each_with_object({}) do |(_key, params), m|
          key = "#{params[:variant_id]}-#{params[:stock_location_id]}"
          stock_items[key] ||= build_stock_item(params: params)
          stock_item = stock_items[key]
          unless stock_item.save
            errors.add_multiple_errors(stock_item.errors.messages)
            raise ActiveRecord::Rollback
          end
          record_activity_log(stock_item) if stock_item.previous_changes.key?('id')
          m[stock_item] ||= []
          m[stock_item] += build_stock_item_units(params: params)
        end
      end
    end

    private

    def record_activity_log(resource)
      ::Spree::ActivityLog.create!(
        loggable: resource,
        user_id: current_user.id,
        action_name: product&.name,
        role: current_user.spree_roles.first&.name,
        date: Time.current,
        email: current_user.email,
        action_place: action_place,
        action: ::Spree::ActivityLogActions.get(:stock_item, :add),
        product_id: product&.id
      )
    end

    def build_stock_item(params:)
      variant_id = params[:variant_id].presence || product.master.id
      stock_location_id = params[:stock_location_id]
      stock_location_section_id = params[:stock_location_section_id].presence

      product.stock_items.find_or_initialize_by(variant_id:, stock_location_id:, stock_location_section_id:)
    end

    def build_stock_item_units(params:)
      Array.new(params[:quantity].to_i) do
        attributes = {
          expiry_date: expiry_date(params: params),
          vendor_receipt_number: vendor_receipt_number(params: params),
          vendor_receipt_date: vendor_receipt_date(params: params),
          vendor_inventory_number: vendor_inventory_number(params: params),
          vendor_inventory_cost: vendor_inventory_cost(params: params),
          remark: remark(params: params),
          pack_size: pack_size(params: params),
          expiry_type: expiry_type(params: params),
        }.compact

        stock_item_unit = Spree::StockItemUnit.new(attributes)
        stock_item_unit
      end
    end

    def expiry_date(params:)
      enabled = ActiveModel::Type::Boolean.new.cast(params[:expiry_date_individually])
      enabled ? nil : params[:expiry_date].presence
    end

    def vendor_receipt_number(params:)
      enabled = ActiveModel::Type::Boolean.new.cast(params[:vendor_receipt_number_individually])
      enabled ? nil : params[:vendor_receipt_number].presence
    end

    def vendor_inventory_number(params:)
      enabled = ActiveModel::Type::Boolean.new.cast(params[:vendor_inventory_number_individually])
      enabled ? nil : params[:vendor_inventory_number].presence
    end

    def vendor_inventory_cost(params:)
      enabled = ActiveModel::Type::Boolean.new.cast(params[:vendor_inventory_cost_individually])
      enabled ? nil : params[:vendor_inventory_cost].presence
    end

    def vendor_receipt_date(params:)
      enabled = ActiveModel::Type::Boolean.new.cast(params[:vendor_receipt_date_individually])
      enabled ? nil : params[:vendor_receipt_date].presence
    end

    def remark(params:)
      enabled = ActiveModel::Type::Boolean.new.cast(params[:remark_individually])
      enabled ? nil : params[:remark].presence
    end

    def pack_size(params:)
      enabled = ActiveModel::Type::Boolean.new.cast(params[:pack_size_individually])
      enabled ? 1 : params[:pack_size].presence
    end

    def expiry_type(params:)
      params[:expiry_type].presence
    end
  end
end
