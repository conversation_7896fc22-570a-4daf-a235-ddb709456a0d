# frozen_string_literal: true

module Admin
  class HandleScanCommand
    prepend SimpleCommand

    attr_reader :options

    def initialize(store:, options:)
      @store = store
      @options = options
    end

    def call
      case options[:type]
      when "ebay_catalog"
        search_ebay_catalog
      when "select_stock"
        fetch_select_stock_unit
      when "restore_stock"
        fetch_stock_item_unit
      when "check_stock"
        fetch_stock_item_unit
      when "input"
        options.merge(value: decoded_text)
      when "barcodelookup"
        fetch_upc_item
      when "upcitemdb"
        fetch_upc_item
      when "scan_vin"
        options.merge(vin: decoded_text)
      when "search_product_vin"
        options.merge(vin: decoded_text)
      end
    rescue => e
      errors.add(:base, e.message)
    end

    private

    def decoded_text
      @decoded_text ||= options[:id].to_s.strip
    end

    def fetch_upc_item
      resp = UpcItem::Lookup.new.get_product_details(decoded_text, options[:type], store)
      item = Spree::ScannedItem.find_or_create_by(upc: decoded_text, user_id: user.id)

      unless resp
        item.update!(status: :failed)
        errors.add(:base, "Product not found")
        return
      end

      SpreeInsertion::UpcItem::ScannedItem.new(resp[:items].first, item).execute
      item.update!(status: :completed)
      if user.present?
        ::Spree::ActivityLog.create!(
          loggable: item,
          user_id: user.id,
          action: ::Spree::ActivityLogActions.get(:scanned_item, :add),
          role: user.spree_roles.first&.name,
          date: Time.current,
          email: user.email,
          action_place: 'N/A',
          action_name: item.title,
          product_id: nil,
        )
      end

      options.merge(type: "refresh")
    end

    def fetch_stock_item_unit
      stock_item_unit = ::Spree::StockItemUnit.find_by(number: decoded_text)

      unless stock_item_unit
        errors.add(:base, "Stock number not found")
        return
      end

      unless legitimate?(stock_item_unit: stock_item_unit)
        errors.add(:base, "This is wrong item for this order!")
        return
      end

      options.merge(number: decoded_text)
    end

    def fetch_select_stock_unit
      stock_item_unit = ::Spree::StockItemUnit.find_by(number: decoded_text)
      if options[:order_id].present?
        if stock_item_unit
          if legitimate?(stock_item_unit: stock_item_unit)
            if stock_item_unit.state == "stock" # rubocop:disable Metrics/BlockNesting
              options.merge(number: decoded_text)
            else
              errors.add(:base, "Stock number not found")
              return # rubocop:disable Style/RedundantReturn
            end
          else
            errors.add(:base, "This is wrong item for this order!")
            return # rubocop:disable Style/RedundantReturn
          end
        else
          errors.add(:base, "Stock number not found")
          return # rubocop:disable Style/RedundantReturn
        end
      elsif stock_item_unit && stock_item_unit.state == "stock"
        options.merge(number: decoded_text)
      else
        errors.add(:base, "Stock number not found")
        return # rubocop:disable Style/RedundantReturn
      end
    end

    def legitimate?(stock_item_unit:)
      return true unless order

      order.line_items.exists?(variant_id: stock_item_unit.stock_item.variant_id)
    end

    def search_ebay_catalog
      upc = options[:id].to_s.strip
      upc_number = decoded_text.length == 12 ? "0" + upc : upc
      response = Ebay::BrowseApis::Search.new(options[:store_id], sale_channel.oauth_application.id).call(upc_number)

      unless response
        errors.add(:base, "Product not found")
        return
      end

      options.merge(response: response, sale_channel_id: sale_channel.id)
    end

    def user
      @user ||= options[:user_token].presence && ::Spree::User.find_by(user_token: options[:user_token])
    end

    def store
      @store ||= options[:store_id].presence && ::Spree::Store.find(options[:store_id])
    end

    def sale_channel
      @sale_channel ||= fetch_sale_channel
    end

    def fetch_sale_channel
      id = options[:sale_channel_id].presence || options[:saleChannelId].presence
      return unless id

      ::Spree::SaleChannel.find(id)
    end

    def order
      @order ||= options[:order_id].presence && ::Spree::Order.find(options[:order_id])
    end
  end
end
