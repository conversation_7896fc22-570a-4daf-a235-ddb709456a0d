/* eslint-disable no-unused-vars */
function updateWeight () {
  // Get the input values from lbs and oz fields
  const lbsValue = parseFloat($('#lbsinputField').val()) || 0
  const ozValue = parseFloat($('#ozinputField').val()) || 0

  // Convert lbs to oz (1 lb = 16 oz)
  const totalOz = lbsValue * 16 + ozValue

  // Update the 'weight' input field with the new total value
  $('#weight').val(totalOz.toFixed(8))
}

// Call updateWeight() initially to calculate and display the initial total weight
updateWeight()

// Listen for changes in lbs and oz fields
$('#lbsinputField, #ozinputField').on('input', function () {
  updateWeight()
})
/* eslint-enable no-unused-vars */
