/* eslint-disable no-unused-vars */
$(document).ready(function () {
  const selectElement = $('select[name="variant[option_value_ids][]"]')

  function hideElement (element) {
    if (element) {
      element.css('display', 'none')
    }
  }

  function showElement (element) {
    if (element) {
      element.css('display', 'block')
    }
  }

  function printSelectedOptionAndLength () {
    const selectedOption = selectElement.find('option:selected').text()

    const inventoryNumberElement = $('[data-hook="inventory_number"]')
    const expiryDateElement = $('#variant_expiry_date_field')
    const receiptNumberElement = $('[data-hook="receipt_number"]')
    const vendorReceiptDateElement = $('[data-hook="admin_product_form_vendor_receipt_date"]')

    if (window.location.pathname.includes('/edit') || window.location.pathname.includes('/new')) {
      hideElement(inventoryNumberElement)
      hideElement(expiryDateElement)
      hideElement(receiptNumberElement)
      hideElement(vendorReceiptDateElement)
    }
  }

  selectElement.on('change', printSelectedOptionAndLength)
  printSelectedOptionAndLength()
})
/* eslint-enable no-unused-vars */
