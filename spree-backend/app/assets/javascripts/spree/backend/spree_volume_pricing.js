//= require spree/backend

// spree's version only handles 'input', not 'select', and this breaks spree_volume_pricing

$(document).on('click', '#add_volume_price', function () {
  const target = $(this).data('target')
  const newTableRow = $(target + ' tr:visible:first')
  if (newTableRow.length === 0) {
    window.show_flash('error', 'No Volume Price Found, Please refresh first to add!')
  }

  newTableRow.find('span.select2').remove()

  // Initialize Select2 for select elements within the new table row
  newTableRow.find('select.select2').select2()

  $('select.select2').select2()
})
