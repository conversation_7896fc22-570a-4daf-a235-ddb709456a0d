document.addEventListener("spree:load", function () {
  const QuickSearchInput = document.getElementById("quick_search");

  if (QuickSearchInput) {
    const QuickSearchPlaceHolder = QuickSearchInput.placeholder;
    const TargetSearchFieldId = document.querySelector(
      "input.js-quick-search-target"
    ).id;
    const AssociatedLabelName = document.querySelector(
      `label[for="${TargetSearchFieldId}"]`
    ).innerHTML;

    QuickSearchInput.placeholder = `${QuickSearchPlaceHolder} ${AssociatedLabelName}`;
  }

  $(".js-show-index-filters").click(function () {
    $(".filter-well").slideToggle();
    $(this).parents(".filter-wrap").toggleClass("collapsed");
  });

  // TODO: remove this js temp behaviour and fix this decent
  // Temp quick search
  // When there was a search term, copy it
  $(".js-quick-search").val($(".js-quick-search-target").val());

  // Catch the quick search form submit and submit the real form
  $("#quick-search").submit(function () {
    $(".js-quick-search-target").val($(".js-quick-search").val());
    $("#table-filter form").submit();
    return false;
  });

  // Clickable ransack filters
  $(".js-add-filter").click(function () {
    var ransackField = $(this).data("ransack-field");
    var ransackValue = $(this).data("ransack-value");
    $("#" + ransackField).val(ransackValue);
    $("#table-filter form").submit();
  });

  $(document).on("click", ".js-delete-filter", function () {
    var ransackField = $(this).parents(".js-filter").data("ransack-field");

    $("#" + ransackField).val("");
    $("#table-filter form").submit();
  });

  function ransackField(value) {
    switch (value) {
      case "Date Range":
        return "Start";
      case "":
        return "Stop";
      default:
        return value.trim();
    }
  }

  // To appear in the filtered options, the elements id attribute must start with 'q_',
  // and it must have the class'.js-filterable'.
  // function applyFilters() {
  $('[id^="q_"].js-filterable').each(function () {
    var $this = $(this);
    if (
      $this.val() !== null &&
      $this.val() !== "" &&
      $this.val().length !== 0
    ) {
      var ransackValue, filter;
      var ransackFieldId = $this.attr("id");
      var label = $('label[for="' + ransackFieldId + '"]');

      // Check if a filter with the same content already exists
      var existingFilter = $(
        '.js-filter[data-ransack-field="' + ransackFieldId + '"]'
      );

      if (existingFilter.length === 0) {
        if ($this.is("select")) {
          ransackValue = $this
            .find("option:selected")
            .toArray()
            .map(function (option) {
              return option.text;
            })
            .join(", ");
        } else {
          ransackValue = $this.val();
        }
        label = ransackField(label.text()) + ": " + ransackValue;

        var cleanLabel = DOMPurify.sanitize(label);
        filter =
          '<span class="js-filter badge-secondary d-inline-flex align-items-center" data-ransack-field="' +
          ransackFieldId +
          '">' +
          cleanLabel +
          `<svg class="js-delete-filter" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1C5.9 1 1 5.9 1 12C1 18.1 5.9 23 12 23C18.1 23 23 18.1 23 12C23 5.9 18.1 1 12 1ZM15.7 9.7L13.4 12L15.7 14.3C16.1 14.7 16.1 15.3 15.7 15.7C15.5 15.9 15.3 16 15 16C14.7 16 14.5 15.9 14.3 15.7L12 13.4L9.7 15.7C9.5 15.9 9.3 16 9 16C8.7 16 8.5 15.9 8.3 15.7C7.9 15.3 7.9 14.7 8.3 14.3L10.6 12L8.3 9.7C7.9 9.3 7.9 8.7 8.3 8.3C8.7 7.9 9.3 7.9 9.7 8.3L12 10.6L14.3 8.3C14.7 7.9 15.3 7.9 15.7 8.3C16.1 8.7 16.1 9.3 15.7 9.7Z" fill="white"/>
          </svg></span>`;
        $(".js-filters").append(filter).show();
      }
    }
  });

  // per page drop-down
  // preserves all selected filters / queries supplied by user
  // changes only per_page value
  $(".js-per-page-select").change(function () {
    var form = $(this).closest(".js-per-page-form");
    var url = form.attr("action");
    var value = $(this).val().toString();
    if (url.match(/\?/)) {
      url += "&per_page=" + value;
    } else {
      url += "?per_page=" + value;
    }
    Turbo.visit(url);
  });

  // injects per_page settings to all available search forms
  // so when user changes some filters / queries per_page is preserved
  document.addEventListener("spree:load", function () {
    var perPageDropdown = $(".js-per-page-select:first");
    if (perPageDropdown.length) {
      var perPageValue = perPageDropdown.val().toString();
      var perPageInput =
        '<input class="hidden_per_page_input" type="hidden" name="per_page" value=' +
        perPageValue +
        " />";

      $(".hidden_per_page_input").remove();
      $("#table-filter form").append(perPageInput);
    }
  });
});
