@page {
  size: landscape;
  margin: 1cm;
}
.table {
  border-collapse: collapse !important;
}

.table td {
  background-color: #fff !important;
}

.table-bordered {
  border: 1px solid #ddd;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #ddd;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}

table {
  background-color: transparent;
}

th {
  background-color: #f0f0f0 !important;
  text-align: left;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ddd;
}

.table > tbody + tbody {
  border-top: 2px solid #ddd;
}
.table .table {
  background-color: #fff;
}
 .logo {
  width: auto;
  height: 20px;
 }

.spree-head {
  padding: 20px 0px 20px 20px;
  width: 100%;
  max-width: 100%;
  background: #162e40;
  height: 50;
}

thead {
  display: table-header-group
}

tfoot {
  display: table-row-group
}

tr {
  page-break-inside: avoid
}

