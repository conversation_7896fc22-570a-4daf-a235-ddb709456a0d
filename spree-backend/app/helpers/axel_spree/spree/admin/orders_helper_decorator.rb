# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module OrdersHelperDecora<PERSON>
        def get_header_title_for_order(order)
          # default
          return "Listing Title" if order.nil?
          return "Listing Title" if order.line_items.nil?
          return "Listing Title" if order.line_items.first.nil?
          # for storefront channel/for Amazon channel if listing_id not null
          return "Listing Title" if order.line_items.first.listing.present?
          # for eBay channel
          return "Listing Title" if order.line_items.first.ebay_item_id.present?
          return "Listing Title" if order.line_items.first.ebay_line_item_id.present?
          # for Amazon channel if listing_id is null
          return "Listing Title" if order.line_items.first.amazon_line_item_id.present?

          # for others
          "Product Name" if order.line_items.first.product.present?
        end

        def get_header_title_for_line_item(line_item)
          # default
          return "Listing Title" if line_item.nil?
          # for storefront channel/for Amazon channel if listing_id not null
          return "Listing Title" if line_item.listing.present?
          # for eBay channel
          return "Listing Title" if line_item.ebay_item_id.present?
          return "Listing Title" if line_item.ebay_line_item_id.present?
          # for Amazon channel if listing_id is null
          return "Listing Title" if line_item.amazon_line_item_id.present?

          # for others
          "Product Name" if line_item.product.present?
        end

        def get_line_item_listing_title(line_item)
          return if line_item.nil?

          # for Walmart channel
          if line_item.walmart_line_item_id.present?
            return line_item.listing.walmart_title_by_variant_id(line_item.variant_id) if line_item.listing.present?
          end

          # for storefront channel/for Amazon channel if listing_id not null
          return line_item.listing.title if line_item.listing.present?

          # for eBay channel
          if line_item.ebay_item_id.present?
            listing = ::Spree::Listing.find_by(item_id: line_item.ebay_item_id)
            return listing.title if listing.present?
          end

          if line_item.ebay_line_item_id.present?
            line_item.order&.sale_channel_metadata&.dig("lineItems")&.each do |item|
              legacy_item_id = item.dig("legacyItemId")
              line_item_id = item.dig("lineItemId")
              if legacy_item_id && line_item.ebay_line_item_id == line_item_id
                # listing = ::Spree::Listing.find_by(item_id: legacy_item_id)
                # return listing.title if listing.present?
                listing_title = item.dig("title")
                return listing_title if listing_title
              end
            end
          end
          # for Amazon channel if listing_id is null
          if line_item.amazon_line_item_id.present?
            return line_item&.product&.listings&.for_brand('amazon')&.last&.title
          end

          # for others
          line_item.product.name if line_item.product.present?
        end

        def line_item_shipment_price(line_item, quantity)
          if line_item.storefront_sale_channel?
            # pack_size = line_item.listing&.pack_size_value || 1
            pack_size = line_item.pack_size
            total = (quantity / pack_size) * line_item.price
            ::Spree::Money.new(total, currency: line_item.currency)
          else
            ::Spree::Money.new(line_item.price * quantity, currency: line_item.currency)
          end
        end

        def walmart_cancel_order_alert?(order)
          return false if order.channel != 'walmart'

          intent_to_cancel = false
          order.sale_channel_metadata.to_h.dig("orderLines", "orderLine").to_a.each do |order_line|
            if order_line.dig("intentToCancel") == 'TRUE'
              intent_to_cancel = true
              break
            end
          end
          intent_to_cancel
        end

        def amazon_cancel_order_alert?(order)
          return false if order.channel != 'amazon'

          intent_to_cancel = false
          if order&.sale_channel_metadata&.dig("intentToCancel") == 'TRUE'
            intent_to_cancel = true
          end
          intent_to_cancel
        end

        def get_line_item_listing_quantity(line_item)
          return line_item.quantity unless line_item.storefront_sale_channel?

          line_item.quantity / line_item.pack_size
        end

        def fetch_variant_image(line_item)
          listing = line_item.listing
          variant = line_item.variant
          product = line_item.product
          return nil if product.nil? || variant.nil?

          image_url = nil

          if listing.present?
            listing_images = listing.image_files

            # Try finding variant-specific image
            variant_images = listing_images.select do |img|
              img.filename.to_s.split("_")[1].to_i == variant.id
            end

            # Fallback to master variant if variant-specific image not found
            if variant_images.blank?
              variant_images = listing_images.select do |img|
                img.filename.to_s.split("_")[1].to_i == product.master.id
              end
            end

            # Get first image sorted by position
            image_data = variant_images.map do |image|
              next unless image.blob.present?
              position = listing.image_uploads.find_by(image_id: image.id)&.position.to_i
              { position: position, blob: image.blob }
            end.compact.sort_by { |img| img[:position] }.first

            image_url = Rails.application.routes.url_helpers.rails_blob_url(image_data[:blob], only_path: true) if image_data
          end

          # Final fallback to product/variant images if listing image not found
          if image_url.blank?
            fallback_image = (variant.images.presence || product.images).first
            if fallback_image&.attachment&.blob.present?
              image_url = Rails.application.routes.url_helpers.rails_blob_url(fallback_image.attachment.blob, only_path: true)
            end
          end

          image_url
        end
      end
    end
  end
end

Spree::Admin::OrdersHelper.prepend(AxelSpree::Spree::Admin::OrdersHelperDecorator)
