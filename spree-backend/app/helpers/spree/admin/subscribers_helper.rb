# frozen_string_literal: true

module Spree
  module Admin
    module Subscribers<PERSON><PERSON><PERSON>
      def subscriber_level_options(_selected = nil)
        Spree::Subscriber.levels.map { |k, v| [k.to_s.capitalize, v] }
      end

      def process_subscriber_action(subscriber, behavior, order = nil)
        # order&.reload
        email = subscriber.email
        user = Spree::User.find_by(email: email)
        sale_channel = find_sale_channel(order)

        complete_orders = Spree::Order.where(email: email, state: 'complete')
        order_count, quantity_count, amount_sum = calculate_order_metrics(complete_orders)

        subscriber.assign_attributes(
          visit_count: subscriber.visit_count + 1,
          sale_channel_id: subscriber.sale_channel_id || sale_channel&.id,
          source: subscriber.source || sale_channel&.brand,
          name: subscriber.name || user&.name || complete_orders&.sample&.name,
          order_count: order_count,
          quantity_count: quantity_count,
          amount_sum: amount_sum
        )

        subscriber.login_count = subscriber.login_count + 1 if behavior == 'logged in'

        old_level = subscriber.level

        update_subscriber_level(subscriber, user, order_count, quantity_count, amount_sum)
        subscriber.save

        new_level = subscriber.level

        Spree::SubscriberBehavior.create!(
          subscriber_id: subscriber.id,
          behavior: behavior,
          detail: generate_behavior_detail(email, behavior, new_level, old_level, sale_channel&.brand),
          record_type: order ? "Spree::Order" : "Spree::User",
          record_id: order&.id || user&.id
        )
      end

      private

      def find_sale_channel(order)
        order&.sale_channel.presence || Spree::SaleChannel.for_brand('storefront').last
      end

      def calculate_order_metrics(orders)
        [orders&.count, orders&.sum(:item_count), orders&.sum(:total)]
      end

      def update_subscriber_level(subscriber, user, order_count, quantity_count, amount_sum)
        return if subscriber.is_vip?

        if qualifies_for_vip?(order_count, quantity_count, amount_sum)
          subscriber.level = "VIP"
        elsif order_count > 0
          subscriber.level = "buyer"
        elsif user
          subscriber.level = "customer" if subscriber.is_subscriber?
        end
      end

      def qualifies_for_vip?(order_count, quantity_count, amount_sum)
        order_count >= Spree::Subscriber::VIP_ORDER_COUNT_THRESHOLD ||
          quantity_count >= Spree::Subscriber::VIP_QUANTITY_COUNT_THRESHOLD ||
          amount_sum >= Spree::Subscriber::VIP_AMOUNT_SUM_THRESHOLD
      end

      def generate_behavior_detail(email, behavior, new_level, old_level, sale_channel)
        if new_level == old_level
          "Subscriber #{email} #{behavior} from #{sale_channel}."
        else
          "Subscriber #{email} #{behavior} from #{sale_channel}. Old Level: #{old_level}, New level: #{new_level}."
        end
      end
    end
  end
end
