# frozen_string_literal: true

module Spree
  module Admin
    module ProductsHelper
      def find_uniq_chanels
        channels = ::Spree::SaleChannel.select("DISTINCT ON (brand) *")
        channels
      end

      def published_name(product)
        product.listings.active&.for_brand('storefront')&.last&.title
      end

      def publish_check(sale_channel_id, product_id)
        publish_checked = ::Spree::Listing.find_by(sale_channel_id: sale_channel_id, product_id: product_id).present?
        publish_checked
      end

      def ebay_oauth_app
        ebay_oauth = ::Spree::SaleChannel.any?
        ebay_oauth
      end

      def published_channel_count(brand, product_id)
        sale_channels = ::Spree::SaleChannel.for_brand(brand)
        sale_channel_ids = sale_channels.pluck(:id)

        total_sale_channels = sale_channels.count

        published_channel_ids = ::Spree::Listing.active.where(product_id: product_id).pluck(:sale_channel_id)
        comman_ids = sale_channel_ids & published_channel_ids

        [total_sale_channels, comman_ids.count]
      end

      def find_sale_channels(brand)
        sale_channels = ::Spree::SaleChannel.for_brand(brand)
        sale_channels
      end

      def convert_oz_to_lbs(object)
        pounds = object.lbs.nil? ? 0 : object.lbs
        ounces = object.oz.nil? ? 0 : object.oz

        weight = pounds * 16 + ounces
        ounces = weight % 16
        pounds = (weight / 16).to_i

        object.update(lbs: pounds, oz: ounces)
      end

      def format_field_value(value)
        if value.to_i == value
          value.to_i
        else
          value
        end
      end

      # Due deprectation of Spree::ProductsHelper#available_status,
      # copy of original Spree::Admin::ProductHelper
      def available_status_ex(product)
        return Spree.t("admin.product.archived") if product.status == "archived"
        return Spree.t("admin.product.deleted") if product.deleted?

        if product.available?
          Spree.t("admin.product.active")
        else
          Spree.t("admin.product.draft")
        end
      end
    end
  end
end
