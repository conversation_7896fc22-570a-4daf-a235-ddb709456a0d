# frozen_string_literal: true

module Spree
  module Admin
    module DigitalHelper
      def asset_icon(asset)
        file_name = case asset.attachment.content_type
        when /pdf\z/
          "file-pdf.svg"
        when /\Aimage/
          "file-image.svg"
        when /zip\z/
          "file-zip.svg"
        when "text/csv", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          "file-spreadsheet.svg"
        when /\Avideo/
          "file-video.svg"
        when /\Aaudio/
          "file-audio.svg"
        when /\Afont/
          "file-earmark-font.svg"
        else
          "file-unknown.svg"
        end

        icon_name = File.basename(file_name, File.extname(file_name))
        inline_svg_tag("filetypes/#{icon_name}.svg", class: "icon-#{icon_name} ", size: "24px*24px")
      end
    end
  end
end
