# frozen_string_literal: true

module Spree
  module Admin
    module ElasticsearchHel<PERSON>
      def helper_sync_product_to_elasticsearch
        return if Rails.env.test?
        
        AxelSpree::Elasticsearch::Product.new.sync
        AxelSpree::Elasticsearch::Listing.new.sync
      rescue StandardError => e
        Rails.logger.error("helper_sync_product_to_elasticsearch failed: #{e.message}")
      end

      def helper_sync_listing_to_elasticsearch
        return if Rails.env.test?

        AxelSpree::Elasticsearch::Product.new.sync
        AxelSpree::Elasticsearch::Listing.new.sync
      rescue StandardError => e
        Rails.logger.error("helper_sync_listing_to_elasticsearch failed: #{e.message}")
      end
    end
  end
end
