# frozen_string_literal: true

module Spree
  module Admin
    module ShipmentHelper
      def non_easypost_shipping_category(shipment)
        shipment.line_items.any? { |line_item| line_item.listing&.shipping_category&.shipping_methods&.any?(&:local_pickup) }
      end

      def fetch_shipping_method(shipment)
        first_shipping_category_name = shipment.line_items.first.listing&.shipping_category&.name

        if first_shipping_category_name&.include?("Free Shipping")
          first_shipping_category_name
        elsif non_easypost_shipping_category(shipment)
          "local_pickup"
        else
          "easypost"
        end
      end

      def check_shipping_category(order_package_number)
        order_package = Spree::OrderPackage.find_by(number: order_package_number)
        shipping_categories = []
        order_package.shipments.each do |shipment|
          shipping_category = fetch_shipping_method(shipment)
          shipping_categories.push(shipping_category)
        end
        shipping_categories.uniq.length == 1 ? "" : "disabled"
      end
    end
  end
end
