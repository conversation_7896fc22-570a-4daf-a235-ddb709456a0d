# frozen_string_literal: true

module Spree
  module Admin
    module Metaba<PERSON><PERSON>ep<PERSON><PERSON><PERSON>el<PERSON>
      def metabase_report_configured(tenant_name)
        tenant = ::Spree::Organisation.where(name: tenant_name)
        return false if tenant.nil?

        mb_settting = ::Spree::MetabaseSetting.where(organisation: tenant).first
        return false if mb_settting.nil?

        report_settting = ::Spree::MetabaseReportSetting.where(organisation: tenant).first
        return false if report_settting.nil?

        true
      end

      def metabase_report_iframe_url(tenant_name, report_id)
        tenant = ::Spree::Organisation.where(name: tenant_name)
        return if tenant.nil?

        mb_settting = ::Spree::MetabaseSetting.where(organisation: tenant).first
        return if mb_settting.nil?
        return if report_id.nil?

        report_settting = ::Spree::MetabaseReportSetting.find_by(id: report_id)
        return if report_settting.nil?

        # you will need to install 'jwt' gem first via 'gem install jwt' or in your project Gemfile
        require "jwt"
        metabase_site_url = mb_settting.metabase_site_url
        metabase_secret_key = mb_settting.metabase_secret_key
        metabase_sharing_type = report_settting.metabase_sharing_type
        metabase_sharing_data = report_settting.metabase_sharing_data
        return report_settting.metabase_sharing_data if metabase_sharing_type == "public_link"

        payload = if metabase_sharing_type == "dashboard"
          {
            resource: { dashboard: metabase_sharing_data.to_i },
            params: {},
            exp: Time.now.to_i + (10 * 1440 * 60 * 1), # 10 days expiration
          }
        elsif metabase_sharing_type == "question"
          {
            resource: { question: metabase_sharing_data.to_i },
            params: {},
            exp: Time.now.to_i + (10 * 1440 * 60 * 1), # 10 days expiration
          }
        end
        token = JWT.encode(payload, metabase_secret_key)
        iframe_url = metabase_site_url + "/embed/#{metabase_sharing_type}/" + token + "#bordered=true&titled=true"
        iframe_url
      end

      def metabase_root_menu_names(tenant_name)
        tenant = ::Spree::Organisation.where(name: tenant_name)
        return ["Metabase Report"] if tenant.nil?

        mb_settting = ::Spree::MetabaseSetting.where(organisation: tenant).first
        return ["Metabase Report"] if mb_settting.nil?

        root_menus = ::Spree::MetabaseReportSetting.where(organisation: tenant).select("DISTINCT root_menu_name")
        return ["Metabase Report"] if root_menus.nil?

        root_menus.map(&:root_menu_name)
      end

      def metabase_report_menu_name(tenant_name, report_id)
        tenant = ::Spree::Organisation.where(name: tenant_name)
        return "Metabase Sub Report" if tenant.nil?

        mb_settting = ::Spree::MetabaseSetting.where(organisation: tenant).first
        return "Metabase Sub Report" if mb_settting.nil?
        return "Metabase Sub Report" if report_id.nil?

        report_settting = ::Spree::MetabaseReportSetting.find_by(id: report_id)
        return "Metabase Sub Report" if report_settting.nil?

        report_settting&.report_menu_name
      end

      def metabase_reports(tenant_name, root_menu_name)
        tenant = ::Spree::Organisation.where(name: tenant_name)
        return [] if tenant.nil?

        mb_settting = ::Spree::MetabaseSetting.where(organisation: tenant).first
        return [] if mb_settting.nil?

        report_setttings = ::Spree::MetabaseReportSetting.where(organisation: tenant, root_menu_name: root_menu_name)
        return [] if report_setttings.nil?

        report_setttings
      end
    end
  end
end
