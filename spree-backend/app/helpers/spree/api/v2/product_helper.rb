# frozen_string_literal: true

module Spree
  module Api
    module V2
      module ProductHelper
        extend ActiveSupport::Concern

        class_methods do
          def published_name(product)
            product.listings.active.for_brand('storefront').last&.title
          end

          def description(product)
            product.listings.active.for_brand('storefront').last&.description
          end

          def subscription_details(listing)
            listing&.subscription_details
          end

          def get_listing_id(product, params)
            params[:listing_id]&.to_i || product.try(:listing_id) || product.last_active_listing&.id
          end

          def get_product_listing(product, params)
            product.all_active_product_listings.find_by(id: get_listing_id(product, params))
          end

          def find_variants(product, variant_ids)
            ids = product.variants.any? ? (variant_ids & product.variants.ids) : (variant_ids & [product.master.id])
            product.variants_including_master.where(id: ids)
          end

          def listing_images(product, params)
            listing = get_product_listing(product, params)
            return if listing.nil?

            # Group images by variant ID for faster lookup
            images_by_variant = listing.image_files.group_by { |img| img.filename.to_s.split("_")[1] }

            product.variants_including_master.map do |variant|
              listing_images = (images_by_variant[variant.id.to_s] || []).map do |img|
                next unless img.blob.present?
                {
                  position: listing.image_uploads.find_by(image_id: img.id)&.position.to_i,
                  blob_id: img.id,
                  image_url: Rails.application.routes.url_helpers.rails_blob_url(img.blob, only_path: true)
                }
              end&.compact&.sort_by { |img| img[:position] }

              {
                is_master: variant.is_master,
                variant_id: variant.id,
                image_url_list: listing_images
              }
            end
          end
        end
      end
    end
  end
end
