# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Wished<PERSON><PERSON>Hel<PERSON>
        extend ActiveSupport::Concern

        class_methods do      
          def fetch_variant_image(wished_item)
            listing = wished_item.listing
            variant = wished_item.variant
            product = wished_item.product
            return nil if product.nil? || variant.nil?

            image_url = nil

            if listing.present?
              listing_images = listing.image_files

              # Try finding variant-specific image
              variant_images = listing_images.select do |img|
                img.filename.to_s.split("_")[1].to_i == variant.id
              end

              # Fallback to master variant if variant-specific image not found
              if variant_images.blank?
                variant_images = listing_images.select do |img|
                  img.filename.to_s.split("_")[1].to_i == product.master.id
                end
              end

              # Get first image sorted by position
              image_data = variant_images.map do |image|
                next unless image.blob.present?
                position = listing.image_uploads.find_by(image_id: image.id)&.position.to_i
                { position: position, blob: image.blob }
              end.compact.sort_by { |img| img[:position] }.first

              image_url = Rails.application.routes.url_helpers.rails_blob_url(image_data[:blob], only_path: true) if image_data
            end

            # Final fallback to product/variant images if listing image not found
            if image_url.blank?
              fallback_image = (variant.images.presence || product.images).first
              if fallback_image&.attachment&.blob.present?
                image_url = Rails.application.routes.url_helpers.rails_blob_url(fallback_image.attachment.blob, only_path: true)
              end
            end

            image_url
          end
        end
      end
    end
  end
end
