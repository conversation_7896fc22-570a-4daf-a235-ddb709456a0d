# frozen_string_literal: true

module Spree
  module Api
    module V2
      module SubscriptionHelper
        extend ActiveSupport::Concern

        class_methods do
          def fetch_listing_title(subscription)
            subscription&.listing&.title
          end

          def fetch_listing_images(subscription)
            subscription&.listing&.product&.images&.map(&:original_url)
          end
        end
      end
    end
  end
end
