# frozen_string_literal: true

module Spree
  module Api
    module V3
      module EasypostSettingsHelper
        def self.field_mapping
          {
            easypost_setting_id: :id,
            customs_declaration_signer: :customs_signer,
            customs_contents_type_description: :customs_contents_type,
            customs_eel_pfc_code: :customs_eel_pfc,
            shipping_carrier_accounts: :carrier_accounts_shipping,
            returns_carrier_accounts: :carrier_accounts_returns,
            endorsement_type_description: :endorsement_type,
            returns_stock_location_id: :returns_stock_location_id,
            auto_buy_postage_when_shipped: :buy_postage_when_shipped,
            enable_easypost_on_frontend: :use_easypost_on_frontend,
            enable_address_validation: :validate_address,
            store_identifier: :store_id,
            creation_timestamp: :created_at,
            last_updated_timestamp: :updated_at,
            api_key: :key,
            name: :name
          }.freeze
        end
      end
    end
  end
end
