# frozen_string_literal: true

module Spree
  module Api
    module V3
      module ProductHelper
        extend ActiveSupport::Concern

        class_methods do
          def product_image(product)
            if product.variants.any? && product.option_types.any?
              variants_images(product)
            else
              master_variant_images(product)
            end
          end

          def variants_images(product)
            image_path = []

            product.variants.each do |variant|
              images = variant.images.map do |var_img|
                if var_img
                  {
                    blob_id: var_img.id,
                    image_url: Rails.application.routes.url_helpers.rails_representation_url(var_img.url(:small), only_path: true)
                  }
                end
              end.compact

              image_path.push(
                {
                  is_master: false,
                  variant_id: variant.id,
                  image_url_list: images
                }
              )
            end

            image_path.concat(master_variant_images(product))
          end

          def master_variant_images(product)
            master_images = product.master.images.map do |mas_img|
              if mas_img
                {
                  blob_id: mas_img.id,
                  image_url: Rails.application.routes.url_helpers.rails_representation_url(mas_img.url(:small), only_path: true)
                }
              end
            end.compact

            [
              {
                is_master: true,
                variant_id: product.master.id,
                image_url_list: master_images
              }
            ]
          end
        end
      end
    end
  end
end
