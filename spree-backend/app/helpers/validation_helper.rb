# frozen_string_literal: true
#
# This module contains helper methods for any kind of data validation

module ValidationHelper
  include ActionController
  class WrongParams < StandardError; end

  def check_expected_params(data, required_keys)
    return if required_keys.empty?

    # Log the type and details of the data for debugging
    Rails.logger.debug("[ValidationHelper] Data type: #{data.class}, Data: #{data.inspect}")

    # Convert data to a hash with string keys, based on the type of object
    data_hash = if data.is_a?(Hash)
                  data.transform_keys(&:to_s)
                elsif data.respond_to?(:attributes) && data.attributes.is_a?(Hash)
                  # Handle ActiveRecord-like objects with attributes
                  data.attributes.transform_keys(&:to_s)
                elsif data.respond_to?(:to_h)
                  # Handle objects responding to `to_h` (convertible to hashes)
                  data.to_h.transform_keys(&:to_s)
                elsif data.is_a?(ActionController::Parameters)
                  # Handle ActionController::Parameters (e.g., from API input)
                  data.to_unsafe_h.transform_keys(&:to_s)
                else
                  raise WrongParams.new("[ValidationHelper] Invalid data type: #{data.class}. Expected Hash, object with attributes, or ActionController::Parameters.")
                end

    # Check for missing keys within the data_hash
    missing_keys = required_keys.reject do |key|
      data_hash.key?(key) && !data_hash[key].nil? && !data_hash[key].to_s.strip.empty?
    end

    # Raise an error if there are any missing or invalid keys
    if missing_keys.any?
      raise WrongParams.new("Missing or invalid required parameters: #{missing_keys.join(', ')}")
    end
  end


  def check_request_expected_params(data, required_keys)
    return if required_keys.empty?

    # Log the type and details of the data for debugging
    Rails.logger.debug("[ValidationHelper] Data type: #{data.class}, Data: #{data.inspect}")

    # Convert data to a hash with string keys, based on the type of object
    data_hash = if data.is_a?(Hash)
                  data.transform_keys(&:to_s)
                elsif data.respond_to?(:attributes) && data.attributes.is_a?(Hash)
                  # Handle ActiveRecord-like objects with attributes
                  data.attributes.transform_keys(&:to_s)
                elsif data.respond_to?(:to_h)
                  # Handle objects responding to `to_h` (convertible to hashes)
                  data.to_h.transform_keys(&:to_s)
                elsif data.is_a?(ActionController::Parameters)
                  # Handle ActionController::Parameters (e.g., from API input)
                  data.to_unsafe_h.transform_keys(&:to_s)
                else
                  raise WrongParams.new("[ValidationHelper] Invalid data type: #{data.class}. Expected Hash, object with attributes, or ActionController::Parameters.")
                end

    # Check for missing keys within the data_hash
    missing_keys = required_keys.reject do |key|
      data_hash.key?(key) && !data_hash[key].nil? && !data_hash[key].to_s.strip.empty?
    end

    # Raise an error if there are any missing or invalid keys
    if missing_keys.any?
      raise ActionController::BadRequest.new("Missing required parameters: #{missing_keys.join(', ')}")
    end
  end
end
