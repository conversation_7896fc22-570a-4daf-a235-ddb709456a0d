module Spree
    module LineItems
      class FindByVariant
        def execute(order:, variant:, options: {})
          listing_id = options[:listing_id].present? ? options[:listing_id]&.to_i : nil
          order.line_items.detect do |line_item|
            next unless line_item.variant_id == variant.id && line_item.listing_id == listing_id
  
            Spree::Dependencies.cart_compare_line_items_service.constantize.call(order: order, line_item: line_item, options: options).value
          end
        end
      end
    end
  end
