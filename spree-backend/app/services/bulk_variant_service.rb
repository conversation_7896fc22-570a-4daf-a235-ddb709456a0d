# frozen_string_literal: true

class BulkVariantService
  attr_reader :product, :variants_data, :current_user, :errors

  def initialize(product:, variants_data:, current_user: nil)
    @product = product
    @variants_data = variants_data
    @current_user = current_user
    @errors = []
  end

  def create_variants
    return { success: false, errors: ['No variants data provided'] } if variants_data.blank?

    variants = []

    ActiveRecord::Base.transaction do
      # Preload option types and values to avoid N+1 queries
      option_value_ids = variants_data.flat_map { |v| v[:option_value_ids] }.compact.uniq
      option_values = Spree::OptionValue.includes(:option_type).where(id: option_value_ids).index_by(&:id)

      # Ensure product has all required option types
      ensure_product_option_types(option_values.values)

      # Use bulk insert for better performance
      if use_bulk_insert?
        variants = create_variants_bulk(option_values)
      else
        # Fallback to individual creation for complex validations
        variants_data.each do |variant_data|
          variant = build_variant(variant_data, option_values)

          if variant.save
            variants << variant
            record_activity_log(variant) if current_user
          else
            @errors.concat(variant.errors.full_messages)
            raise ActiveRecord::Rollback
          end
        end
      end

      # Bulk create stock items for all variants at once
      create_stock_items_bulk(variants) if variants.any?

      # Touch product once at the end instead of for each variant
      product.touch
    end

    if @errors.empty?
      { success: true, variants: variants }
    else
      { success: false, errors: @errors }
    end
  rescue StandardError => e
    { success: false, errors: [e.message] }
  end

  def update_variants
    return { success: false, errors: ['No variants data provided'] } if variants_data.blank?

    variants = []
    
    ActiveRecord::Base.transaction do
      # Preload existing variants and option values
      variant_ids = variants_data.map { |v| v[:id] }.compact
      existing_variants = product.variants.includes(:option_values).where(id: variant_ids).index_by(&:id)
      
      option_value_ids = variants_data.flat_map { |v| v[:option_value_ids] }.compact.uniq
      option_values = Spree::OptionValue.includes(:option_type).where(id: option_value_ids).index_by(&:id)
      
      # Ensure product has all required option types
      ensure_product_option_types(option_values.values)
      
      variants_data.each do |variant_data|
        variant = existing_variants[variant_data[:id]]
        next unless variant

        update_variant_attributes(variant, variant_data, option_values)

        if variant.save
          variants << variant
          record_activity_log(variant) if current_user
        else
          @errors.concat(variant.errors.full_messages)
          raise ActiveRecord::Rollback
        end
      end

      # Ensure stock items exist for updated variants (in case new stock locations were added)
      ensure_stock_items_exist(variants) if variants.any?

      # Touch product once at the end
      product.touch if variants.any?
    end

    if @errors.empty?
      { success: true, variants: variants }
    else
      { success: false, errors: @errors }
    end
  rescue StandardError => e
    { success: false, errors: [e.message] }
  end

  private

  def build_variant(variant_data, option_values)
    variant = product.variants.build(variant_attributes(variant_data))
    
    # Set option values
    if variant_data[:option_value_ids].present?
      variant.option_values = variant_data[:option_value_ids].map { |id| option_values[id] }.compact
    end
    
    variant
  end

  def update_variant_attributes(variant, variant_data, option_values)
    variant.assign_attributes(variant_attributes(variant_data))
    
    # Update option values if provided
    if variant_data[:option_value_ids].present?
      variant.option_values = variant_data[:option_value_ids].map { |id| option_values[id] }.compact
    end
  end

  def variant_attributes(variant_data)
    variant_data.except(:id, :option_value_ids).merge(product_id: product.id)
  end

  def ensure_product_option_types(option_values)
    return if option_values.blank?
    
    option_types = option_values.map(&:option_type).uniq
    existing_option_type_ids = product.option_type_ids
    
    option_types.each do |option_type|
      unless existing_option_type_ids.include?(option_type.id)
        product.option_types << option_type
      end
    end
  end

  def create_stock_items_bulk(variants)
    # Get all stock locations that propagate variants
    stock_locations = Spree::StockLocation.where(propagate_all_variants: true)
    return if stock_locations.empty?

    stock_items_data = []
    variants.each do |variant|
      stock_locations.each do |stock_location|
        stock_items_data << {
          variant_id: variant.id,
          stock_location_id: stock_location.id,
          count_on_hand: 0,
          backorderable: stock_location.backorderable_default
        }
      end
    end

    # Bulk insert stock items
    Spree::StockItem.insert_all(stock_items_data) if stock_items_data.any?
  end

  def ensure_stock_items_exist(variants)
    # Get all stock locations that propagate variants
    stock_locations = Spree::StockLocation.where(propagate_all_variants: true)
    return if stock_locations.empty?

    # Find existing stock items to avoid duplicates
    variant_ids = variants.map(&:id)
    existing_stock_items = Spree::StockItem.where(
      variant_id: variant_ids,
      stock_location_id: stock_locations.pluck(:id)
    ).pluck(:variant_id, :stock_location_id)

    existing_combinations = existing_stock_items.map { |variant_id, location_id| "#{variant_id}-#{location_id}" }.to_set

    stock_items_data = []
    variants.each do |variant|
      stock_locations.each do |stock_location|
        combination_key = "#{variant.id}-#{stock_location.id}"

        # Only create if stock item doesn't already exist
        unless existing_combinations.include?(combination_key)
          stock_items_data << {
            variant_id: variant.id,
            stock_location_id: stock_location.id,
            count_on_hand: 0,
            backorderable: stock_location.backorderable_default,
            created_at: Time.current,
            updated_at: Time.current
          }
        end
      end
    end

    # Bulk insert only missing stock items
    Spree::StockItem.insert_all(stock_items_data) if stock_items_data.any?
  end

  def use_bulk_insert?
    # Use bulk insert when all variants have simple attributes and no complex validations
    variants_data.all? do |variant_data|
      variant_data.keys.all? { |key| [:option_value_ids, :sku, :price, :weight].include?(key.to_sym) }
    end
  end

  def create_variants_bulk(option_values)
    # Prepare data for bulk insert
    variants_attributes = variants_data.map.with_index do |variant_data, index|
      {
        product_id: product.id,
        sku: variant_data[:sku],
        price: variant_data[:price] || product.master.price,
        weight: variant_data[:weight],
        position: index + 1,
        is_master: false,
        track_inventory: true,
        created_at: Time.current,
        updated_at: Time.current
      }.compact
    end

    # Bulk insert variants
    result = Spree::Variant.insert_all(variants_attributes, returning: [:id])
    variant_ids = result.rows.flatten

    # Load the created variants
    variants = Spree::Variant.where(id: variant_ids).includes(:option_values)

    # Bulk create option value associations
    create_option_value_associations_bulk(variants, option_values)

    variants
  end

  def create_option_value_associations_bulk(variants, option_values)
    associations_data = []

    variants.each_with_index do |variant, index|
      variant_data = variants_data[index]
      next unless variant_data[:option_value_ids].present?

      variant_data[:option_value_ids].each do |option_value_id|
        associations_data << {
          variant_id: variant.id,
          option_value_id: option_value_id,
          created_at: Time.current,
          updated_at: Time.current
        }
      end
    end

    # Bulk insert option value associations
    if associations_data.any?
      # Use raw SQL for the join table since Rails doesn't have a model for it
      connection = ActiveRecord::Base.connection
      table_name = 'spree_option_value_variants'

      columns = associations_data.first.keys
      values = associations_data.map { |data| columns.map { |col| connection.quote(data[col]) } }

      sql = "INSERT INTO #{table_name} (#{columns.join(', ')}) VALUES #{values.map { |v| "(#{v.join(', ')})" }.join(', ')}"
      connection.execute(sql)
    end
  end

  def record_activity_log(variant)
    return unless current_user

    Spree::ActivityLog.create!(
      loggable: variant,
      user_id: current_user.id,
      action_name: "Bulk variant #{variant.persisted? ? 'update' : 'create'}",
      role: current_user.spree_roles.first&.name,
      date: Time.current,
      email: current_user.email,
      action_place: 'API',
      action: variant.persisted? ? 'update' : 'create',
      product_id: product.id
    )
  rescue StandardError => e
    # Don't fail the whole operation if activity log fails
    Rails.logger.error "Failed to create activity log: #{e.message}"
  end
end
