// First Approach: FormValidation
// https://formvalidation.io/guide/plugins/declarative/
// https://github.com/form-validation/form-validation
// <form data-controller="form" data-form-validation="fv"></form>

// Third Approach: Stimulus + HTML5
// https://www.jorgemanrubia.com/2019/02/16/form-validations-with-html5-and-modern-rails/
// https://github.com/jgorman/rails-form-validation#2-tag-the-form-html-with-the-server-validation-messages
// https://github.com/jgorman/rails-form-validation
// https://gist.github.com/danielvlopes/756fc2e699a16ad4b59fb320695cd424
// <form data-controller="form"></form>

import { Controller } from '@hotwired/stimulus'
const FormValidation = require('../lib/formvalidation/dist/es6')
const Bootstrap = require('../lib/formvalidation/dist/es6/plugins/Bootstrap').default

const FIELD_INVALID_CLASS = 'is-invalid'
const FIELD_ERROR_MESSAGE_CONTAINER_CLASS = 'invalid-feedback'

export default class extends Controller {
  connect () {
    const validation = this.data.get('validation')

    if (validation === 'fv') {
      this.fv = this.initializeFormValidation()
    } else {
      this.fv = this.initializeSimpleValidation()
    }
  }

  disconnect () {
    this.fv?.destroy()
  }

  addField () {
    this.element.querySelectorAll('input, select, textarea').forEach((field) => {
      if (!field.name) return
      if (field.type && field.type.toString().toLowerCase() === 'hidden') return
      if (field.type && field.type.toString().toLowerCase() === 'submit') return
      if (field.type && field.type.toString().toLowerCase() === 'checkbox') return

      try {
        this.fv?.addField(field.name, {})
      } catch (e) {
        console.log(e)
      }
    })
  }

  initializeFormValidation () {
    // Plugins
    const declarative = new FormValidation.plugins.Declarative()
    const trigger = new FormValidation.plugins.Trigger()
    const submitButton = new FormValidation.plugins.SubmitButton({ aspNetButton: true })
    const bootstrap = new Bootstrap({})

    const options = {
      plugins: {
        declarative,
        trigger,
        submitButton,
        bootstrap
      }
    }

    const fv = FormValidation.formValidation(this.element, options)

    const inputs = this.element.querySelectorAll('input[data-fv-identical___compare]')
    inputs.forEach((input) => {
      const compare = input.dataset.fvIdentical___compare
      if (compare.startsWith('#') || compare.startsWith('.')) {
        const fn = () => this.element.querySelector(compare).value
        fv.updateValidatorOption(input.name, 'identical', 'compare', fn)
      }
    })

    return fv
  }

  initializeSimpleValidation () {
    const fv = {}

    this.element.setAttribute('novalidate', true)
    this.element.addEventListener('blur', this.onBlur, true)
    this.element.addEventListener('submit', this.onSubmit)
    this.element.addEventListener('ajax:beforeSend', this.onSubmit)
    this.element.addEventListener('turbo:submit-start', this.onSubmit)

    fv.destroy = () => {
      this.element.removeEventListener('blur', this.onBlur)
      this.element.removeEventListener('submit', this.onSubmit)
      this.element.removeEventListener('ajax:beforeSend', this.onSubmit)
      this.element.removeEventListener('turbo:submit-start', this.onSubmit)
    }

    fv.addField = (field, options) => {}

    return fv
  }

  onBlur = (event) => {
    const field = event.target

    const isValid = this.isFieldValid(field)
    field.classList.toggle(FIELD_INVALID_CLASS, !isValid)
    this.refreshErrorForInvalidField(field, isValid)
  }

  onSubmit = (event) => {
    this.formFields.forEach((field) => {
      const isValid = this.isFieldValid(field)
      field.classList.toggle(FIELD_INVALID_CLASS, !isValid)
      this.refreshErrorForInvalidField(field, isValid)
    })

    if (this.isFormInvalid()) {
      event.preventDefault()
      this.firstInvalidField.focus()
    }
  }

  isFormInvalid () {
    return this.formFields.some((field) => this.shouldValidateField(field) && !this.isFieldValid(field))
  }

  isFieldValid (field) {
    return !this.shouldValidateField(field) || field.checkValidity()
  }

  shouldValidateField (field) {
    return !field.disabled && !['file', 'reset', 'submit', 'button', undefined].includes(field.type) && !field.dataset.skipNumberType
  }

  refreshErrorForInvalidField (field, isValid) {
    this.removeExistingErrorMessage(field)
    if (!isValid) this.showErrorForInvalidField(field)
  }

  removeExistingErrorMessage (field) {
    const fieldContainer = field.parentNode
    if (!fieldContainer) return

    const existingErrorMessageElement = fieldContainer.querySelector(`.${FIELD_ERROR_MESSAGE_CONTAINER_CLASS}`)
    if (existingErrorMessageElement) existingErrorMessageElement.parentNode.removeChild(existingErrorMessageElement)
  }

  showErrorForInvalidField (field) {
    const container = field.parentNode
    const messageContainer = container.querySelector(`.${FIELD_ERROR_MESSAGE_CONTAINER_CLASS}`) || this.buildFieldErrorHtml(container)
    messageContainer.innerHTML = field.validationMessage
  }

  buildFieldErrorHtml (container) {
    const node = document.createElement('div')
    node.classList.add(FIELD_ERROR_MESSAGE_CONTAINER_CLASS)
    container.append(node)
    return node
  }

  get formFields () {
    return Array.from(this.element.elements)
  }

  get firstInvalidField () {
    return this.formFields.find(field => !field.checkValidity())
  }
}
