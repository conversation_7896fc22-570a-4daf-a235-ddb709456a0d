import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect () {
  }

  expand (e) {
    const orderElements = document.getElementsByClassName('expand-all')
    for (let i = 0; i < orderElements.length; i++) {
      const element = orderElements[i]
      element.classList.remove('d-none')
    }
  }

  collapse (e) {
    const orderElements = document.getElementsByClassName('expand-all')

    for (let i = 0; i < orderElements.length; i++) {
      const element = orderElements[i]
      element.classList.add('d-none')
    }
  }

  accordion (e) {
    const orderId = event.currentTarget.dataset.orderId
    const element = document.getElementById(`order_${orderId}`)
    if (element.classList.contains('d-none')) {
      element.classList.remove('d-none')
    } else {
      element.classList.add('d-none')
    }
  }
}
