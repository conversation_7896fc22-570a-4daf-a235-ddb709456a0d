import NestedForm from 'stimulus-rails-nested-form'

export default class extends NestedForm {
  add (e) {
    e.preventDefault()

    const template = this.sanitizedTemplate()
    const id = new Date().getTime().toString()
    const replacedWith = `$1${id}$2`

    let regexp = /(stock_items\[)(\d*)(\]*?)/g
    let content = template.innerHTML.replace(regexp, replacedWith)

    regexp = /(stock_items%5B)(\d*)((%5D)*?)/g
    content = content.replace(regexp, replacedWith)

    const html = `<div data-batch-add-stock-target="template" class="border-top border-secondary pt-3 mt-3">${content}</div>`

    this.targetTarget.insertAdjacentHTML('beforebegin', html)
    this.dispatch('add')
  }

  sanitizedTemplate () {
    const template = this.templateTargets.at(-1).cloneNode(true)

    template.querySelectorAll('.select2-container').forEach(it => it.remove())
    template.querySelectorAll('input').forEach(it => {
      switch (it.type?.toString().toLowerCase()) {
        case 'text':
          it.setAttribute('value', it.value)
          break
        case 'number':
          it.setAttribute('value', it.value)
          break
        case 'date':
          it.setAttribute('value', it.value)
          break
        case 'hidden':
          it.setAttribute('value', it.value)
          break
        case 'checkbox':
          if (it.checked) it.setAttribute('checked', 'checked')
          break
        case 'radio':
          if (it.checked) it.setAttribute('checked', 'checked')
          break
      }
    })

    return template
  }
}
