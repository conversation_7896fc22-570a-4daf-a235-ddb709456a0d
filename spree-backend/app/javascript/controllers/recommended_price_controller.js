import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['quantity']

  edit (e) {
    e.preventDefault()
    e.currentTarget.parentElement.classList.add('d-none')
    e.currentTarget.parentElement.nextElementSibling.classList.remove('d-none')
    e.currentTarget.parentElement.nextElementSibling.children[0].classList.remove('d-none')
  }

  save (e) {
    e.preventDefault()
    const td = e.currentTarget.closest('td')
    const tr = e.currentTarget.closest('tr')
    const variantId = e.currentTarget.dataset.variantId
    const listingInventoryId = e.currentTarget.dataset.inventoryId
    const itemId = e.currentTarget.dataset.itemId
    const itemsList = e.currentTarget.dataset.itemsList
    const quantity = this.quantityTarget.value
    const url = '/old_admin/variants/' + variantId + '/recommended_prices/update_item'
    const hdr = {}
    hdr['Content-Type'] = 'application/json'
    fetch(url, {
      method: 'PUT',
      headers: hdr,
      body: JSON.stringify({ item_id: itemId, listing_inventory_id: listingInventoryId, quantity, tab: itemsList })
    }).then(response => {
      if (response.ok) {
        $(tr).find('span.quantity').html(quantity)
        td.children[0].children[0].value = quantity
        td.classList.add('d-none')
        td.previousElementSibling.classList.remove('d-none')
      }
    })
  }

  cancel (e) {
    e.preventDefault()
    const td = e.currentTarget.closest('td')
    td.children[0].children[0].value = e.currentTarget.dataset.initialValue
    td.classList.add('d-none')
    td.previousElementSibling.classList.remove('d-none')
  }
}
