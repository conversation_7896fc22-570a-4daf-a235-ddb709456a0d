import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect () {
  }

  disableButton (event) {
    const checkboxes = document.querySelectorAll('[id^="checkbox_name_"][class*="non-order-package"]')
    let isChecked = false
    let isShipChecked = false
    const shippingCategories = []
    let shippingCategory = false

    checkboxes.forEach(function (checkbox) {
      if (checkbox.checked) {
        isChecked = true
        isShipChecked = true
        checkboxes.forEach(function (ob) {
          if (ob.checked && ob.dataset.nonEasypostShippingCategoryTarget === 'true') {
            isChecked = false
            isShipChecked = true
          }
        })
        shippingCategories.push(checkbox.dataset.shippingCategory) // Collecting shipping categories
      }
    })

    if (shippingCategories.length > 0) {
      const firstCategory = Array.from(shippingCategories)[0]
      for (const category of shippingCategories) {
        if (category !== firstCategory) {
          shippingCategory = true
        }
      }
    }

    if (isChecked) {
      $('.ship-postage-rate').eq(0).removeClass('disabled')
      $('.walmart-ship-postage-rate').eq(0).removeClass('disabled')
      $('.amazon-ship-postage-rate').eq(0).removeClass('disabled')
      $('.order-ship').eq(0).removeClass('disabled')
    } else {
      $('.ship-postage-rate').eq(0).addClass('disabled')
      $('.walmart-ship-postage-rate').eq(0).addClass('disabled')
      $('.amazon-ship-postage-rate').eq(0).addClass('disabled')
      $('.order-ship').eq(0).addClass('disabled')
    }
    if (isShipChecked) {
      $('.order-ship').eq(0).removeClass('disabled')
    } else {
      $('.order-ship').eq(0).addClass('disabled')
    }

    if (shippingCategory) {
      $('.order-ship').eq(0).addClass('disabled')
    }
  }

  confirmDelete () {
    const shipmentNumber = event.currentTarget.dataset.shipmentNumber
    const hdr = Spree.apiV2Authentication() /* global Spree */
    $.ajax({
      type: 'PATCH',
      url: '/api/v2/platform/shipments/' + shipmentNumber + '/delete_shipping_label',
      headers: hdr,
      data: {
        shipmentNumber
      },
      success: function (response) {
        if (response.message === 'success') {
          window.location.reload()
        } else {
          alert(response.error)
        }
      }
    })
  }
}
