import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [ 'length', 'width', 'height', 'weight']

  connect () {
    this.element.addEventListener('click', (event) => {
      if (event.target.classList.contains('amazon-buy-postage')) {
        this.handleBuyPostageClick(event)
      }
    })

    const rateList = document.getElementById('amazon-shipping-rates-list')

    if (rateList) {
      rateList.addEventListener('click', (event) => {
        const listItem = event.target.closest('.list-group-rate-item')
        const confirmButton = $('#amazon-buy-postage-confirm')[0]

        if (listItem) {
          const radioButton = listItem.querySelector('.form-check-input')

          if (radioButton) {
            radioButton.checked = true
            confirmButton.classList.remove('disabled')
          }
        } else {
          confirmButton.classList.toggle('disabled', !listItem)
        }
      })
    }
  }

  openModal (event) {
    const shipmentIdArray = this.createShipmentIdArray(event)

    $.ajax({
      type: 'GET',
      url: "/old_admin/orders/undefined/validate_stock_location",
      headers: Spree.apiV2Authentication(),
      data: {
          shipment_ids: shipmentIdArray
      }
    }).done(function(response) {
      if (response.data.error) {
        alert("Selected shipments do not belong to the same stock location");
        return
      } else {
        $('#upc_text_scan_modal').modal('show')
      }
    })
  }

  createShipmentIdArray (event) {
    const orderPackageNumber = event.target.dataset.orderPackageNumber
    const checkboxes = document.querySelectorAll(`[id^="checkbox_name_"][class*="${orderPackageNumber}"]`)
    const shipmentIdArray = []
    checkboxes.forEach(function (checkbox) {
      if (checkbox.checked) {
        const shipmentId = checkbox.dataset.shipmentIdTarget
        shipmentIdArray.push(shipmentId)
      }
    })
    return shipmentIdArray
  }

  appendDimensionValues (event) {
    const selectedOption = event.currentTarget.options[event.currentTarget.selectedIndex]
    const { length, width, height } = selectedOption.dataset

    if (!length || !width || !height) {
      console.error('Missing package dimensions (length, width, height) in the selected option.')
      return
    }

    const $lengthField = $('#amazon_length')
    const $widthField = $('#amazon_width')
    const $heightField = $('#amazon_height')

    $lengthField.val(length)
    $widthField.val(width)
    $heightField.val(height)
  }

  validateFields () {
    const length = this.lengthTarget.value
    const width = this.widthTarget.value
    const height = this.heightTarget.value
    const weight = this.weightTarget.value

    if ( !length || !width || !height || !weight) {
      return false
    }
    return true
  }

  renderShippingRates (event) {
    if (!this.validateFields()) {
      alert('Please fill in all required fields: Dimensions, and Weight.')
      return
    }

    const shipmentNumber = event.target.dataset.shipmentNumber
    const refresh = event.target.dataset.refresh
    const url = Spree.url(Spree.routes.shipments_api_v2 + '/' + shipmentNumber + '/shipping_rates')
    const shipmentIdArray = this.createShipmentIdArray(event)

    const weightValue = $('#amazon_weight').val()
    const weightUnit = $('#weight_unit').val()
    const length = $('#amazon_length').val()
    const width = $('#amazon_width').val()
    const height = $('#amazon_height').val()
    const dimensionUnit = $('#dimension_unit').val()

    const hdr = Spree.apiV2Authentication() /* global Spree */
    hdr['Content-Type'] = 'application/json'

    fetch(url, {
      method: 'PUT',
      headers: hdr,
      body: JSON.stringify({
        refresh,
        weight: weightValue,
        weight_unit: weightUnit,
        width,
        length,
        height,
        dimension_unit: dimensionUnit,
        buy_postage: 'amazon',
        shipment_ids: shipmentIdArray
      })
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`)
        }
        return response.json()
      })
      .then(responseData => {
        const rateListDiv = document.getElementById('amazon-shipping-rates-list')
        const shippingMethodRow = rateListDiv.closest('.row')
        const allRatesHTML = responseData.data
          .map(data => this.generateRadioButton(data))
          .join('')

        if (rateListDiv && shippingMethodRow) {
          rateListDiv.innerHTML = allRatesHTML
          shippingMethodRow.classList.remove('d-none')
          $('#buy-postage-confirm').removeClass('disabled')
        }
      })
      .catch(error => {
        console.error('Error:', error)
        this.showFlash('error', error.message)
        window.hideLoading()
      })
  }

  generateRadioButton (data) {
    const dt = data.attributes

    return `
      <div class="list-group-rate-item" data-id="${data.id}">
        <div class="form-check" style="display: flex;">
          <input class="form-check-input" type="radio" name="${dt.shipping_method_admin_name}" id="radio-${data.id}" value="${data.id}" data-target-carrierServiceType="${dt.name}" data-target-carrier="${dt.code}">
          <strong style="width: 40%; max-width: 40%;">${dt.shipping_method_admin_name}</strong>
          <strong style="width: 40%; max-width: 40%; margin-left:20px;">${dt.shipping_method_admin_name}</strong>
          <span class="form-check-label" style="float: right;margin-left: 20px;">${dt.display_cost}</span>
        </div>
      </div>`
  }

  handleBuyPostageClick (event) {
    const shipmentIdArray = this.createShipmentIdArray(event)

    let carrier
    let carrierServicetype
    let shippingRateId
    const link = event.target

    if (link.classList.contains('disabled')) {
      return
    }

    Array.from(document.querySelectorAll('.list-group-rate-item')).forEach(item => {
      if (item.querySelector('input').checked) {
        carrier = item.querySelector('input').dataset.targetCarrier
        carrierServicetype = item.querySelector('input').dataset.targetCarrierservicetype
        shippingRateId = item.querySelector('input').value
      }
    })

    const shipmentNumberValue = link.dataset.shipmentNumber
    const url = Spree.url(Spree.routes.shipments_api_v2 + '/' + shipmentNumberValue + '/buy_postage')
    const url2 = Spree.url(Spree.routes.shipments_api_v2 + '/' + shipmentNumberValue + '/attach_amazon_pdf')

    const shipmentEmailId = document.getElementById('shipment_email_id')?.value || ''
    const customMessage = document.getElementById('custom_message')?.value || ''
    const insure = document.getElementById('insure_element')?.checked || false
    const insureForAmount = document.getElementById('insure_for_amount_element')?.value || ''

    const weight = $('#amazon_weight').val()
    const { length, width, height } = this.getDimensions()
    if ((length || width || height) && !(length && width && height)) {
      this.showFlash('error', 'Dimension length/width/height should be all filled or all empty')
      return
    }

    const dimensionsMatch = this.checkDimensions(weight)
    this.buyPostage(url, shipmentIdArray, shipmentEmailId, customMessage, insure, insureForAmount, weight, length, width, height, carrier, carrierServicetype, shippingRateId, url2)
  }

  getDimensions () {
    return {
      length: $('#amazon_length').val(),
      width: $('#amazon_width').val(),
      height: $('#amazon_height').val()
    }
  }

  checkDimensions (weight) {
    const lengthTarget = $('#amazon_length').data('length')
    const widthTarget = $('#amazon_width').data('width')
    const heightTarget = $('#amazon_height').data('height')

    const lengthValue = $('#amazon_length').val()
    const widthValue = $('#amazon_width').val()
    const heightValue = $('#amazon_height').val()

    return (
      lengthValue === lengthTarget &&
      widthValue === widthTarget &&
      heightValue === heightTarget
    )
  }

  buyPostage (url, shipmentIdArray, shipmentEmailId, customMessage, insure, insureForAmount, weight, length, width, height, carrier, carrierServicetype, shippingRateId, url2) {
    const dimensionUnit = $('#dimension_unit').val()
    const weightUnit = $('#weight_unit').val()

    $.ajax({
      type: 'PUT',
      url,
      data: {
        selected_shipping_rate_id: shippingRateId,
        shipment_email_id: shipmentEmailId,
        custom_message: customMessage,
        insure,
        insure_for_amount: insureForAmount,
        weight,
        width,
        length,
        height,
        shipment_ids: shipmentIdArray,
        carrier,
        carrier_service_type: carrierServicetype,
        buy_postage: 'amazon',
        dimension_unit: dimensionUnit,
        weight_unit: weightUnit
      },
      headers: Spree.apiV2Authentication()
    })
      .done(() => {
        this.attachPdf(url2)
      })
      .fail(msg => {
        alert(msg.responseJSON.error)
      })
  }

  attachPdf (attachPdfUrl) {
    $.ajax({
      type: 'GET',
      url: attachPdfUrl,
      headers: Spree.apiV2Authentication()
    })
      .done(() => window.location.reload())
      .fail(msg => {
        alert(msg.responseJSON.error)
      })
  }

  showDimensionError () {
    const errorDiv = document.getElementById('buy-alert')
    if (errorDiv) {
      errorDiv.textContent = 'Weight/Dimensions have been changed. Please wait until shipping rates are being refreshed.'
      errorDiv.style.display = 'block'
      alert(errorDiv.textContent)
    }
    const refreshButton = document.querySelector('button.refresh-rates')
    if (refreshButton) {
      refreshButton.click()
    }
  }
}
