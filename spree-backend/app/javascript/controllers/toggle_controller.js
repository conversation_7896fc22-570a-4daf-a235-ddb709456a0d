import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [
    'toggle',
    'target',
    'expiry',
    'expiryField',
    'date',
    'select',
    'dateField',
    'expiryType',
    'expiryImage',
    'naImage',
    'estimatedImage',
    'dateFields',
    'dropdown',
    'na'
  ]

  connect () {
    if (this.hasToggleTarget) this.toggle()
  }

  toggle () {
    if (this.toggleTarget.checked) {
      this.targetTarget.disabled = true
    } else {
      this.targetTarget.disabled = false
    }
  }

  toggleExpiry (event) {
    const isChecked = event.target.checked
    if (isChecked) {
      this.dateFieldsTarget.classList.add('disabled')
      this.dateTarget.disabled = true
      this.selectTarget.disable = true
      this.dropdownTarget.classList.add('disabled-dropdown')
    } else {
      this.dateFieldsTarget.classList.remove('disabled')
      this.dateTarget.disabled = false
      this.dropdownTarget.disable = false
      this.dropdownTarget.classList.remove('disabled-dropdown')
    }
  }

  showdate () {
    this.targetTarget.classList.remove('d-none')
    this.expiryTarget.classList.add('d-none')
  }

  changeDateFormat (date) {
    if (date === '') return date
    date = new Date(date)
    return ('0' + date.getDate()).slice(-2) + '/' + ('0' + (date.getMonth() + 1)).slice(-2) + '/' + date.getFullYear()
  }

  toggleDate (event) {
    const selectedOption = event.currentTarget.getAttribute('data-value')
    const dateTarget = this.dateTarget
    const naTarget = this.naTarget
    if (selectedOption === 'fixed') {
      this.handleExpiryField(dateTarget, naTarget)
      this.handleExpiryIcon(this.expiryImageTarget)
      this.expiryTypeTarget.value = selectedOption
    } else if (selectedOption === 'estimated') {
      this.handleExpiryField(dateTarget, naTarget)
      this.handleExpiryIcon(this.estimatedImageTarget)
      this.expiryTypeTarget.value = selectedOption
    } else if (selectedOption === 'na') {
      this.handleExpiryField(naTarget, dateTarget)
      this.handleExpiryIcon(this.naImageTarget)
      this.expiryTypeTarget.value = selectedOption
    }
  }

  revertVendorInfo (event) {
    const uniqueField = event.currentTarget.dataset.field
    const field = document.getElementsByClassName(uniqueField)[0]
    field.value = field.dataset.initialValue
    const expiryTypeField = document.getElementsByClassName(`${uniqueField}_type`)[0]
    expiryTypeField.value = expiryTypeField.dataset.initialValue.includes('na') ? 'N/A' : expiryTypeField.dataset.initialValue
    if (expiryTypeField.value === 'na') {
      this.handleExpiryField(this.naTarget, field)
      this.handleExpiryIcon(this.naImageTarget)
      this.expiryTypeTarget.value = expiryTypeField.dataset.initialValue
    } else {
      this.handleExpiryField(field, this.naTarget)
      expiryTypeField.value === 'estimated' ? this.handleExpiryIcon(this.estimatedImageTarget) : this.handleExpiryIcon(this.expiryImageTarget)
      this.expiryTypeTarget.value = expiryTypeField.value
    }
  }

  handleExpiryIcon (showIconOf) {
    const icons = [this.naImageTarget, this.estimatedImageTarget, this.expiryImageTarget]
    showIconOf.classList.remove('d-none')
    icons.filter(icon => icon !== showIconOf).forEach(icon => icon.classList.add('d-none'))
  }

  handleExpiryField (fieldToShow, fieldToHide) {
    fieldToShow.classList.remove('d-none')
    fieldToHide.classList.add('d-none')
  }
}
