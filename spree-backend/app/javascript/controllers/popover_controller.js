import { Controller } from '@hotwired/stimulus'

const TEMPLATE = `
  <div class="popover" role="tooltip" style="max-width: 450px; padding: 0 0.75rem;">
    <div class="arrow"></div>
    <div class="popover-body">
    </div>
  </div>
`

export default class extends Controller {
  static targets = [
    'target',
    'content'
  ]

  static values = {
    url: String,
    trigger: { type: String, default: 'hover' },
    placement: { type: String, default: 'auto' }
  }

  connect () {
    this.popoverTarget = this.hasTargetTarget ? this.targetTarget : this.element
    this.popover = this.initializePopover()
    this.popoverTarget.addEventListener('click', () => this.popover.popover('hide'))
  }

  initializePopover () {
    const options = {
      html: true,
      sanitize: false,
      trigger: this.triggerValue,
      content: this.content(),
      template: TEMPLATE,
      placement: this.placementValue,
      container: 'body',
      popperConfig: {
        positionFixed: true,
        strategy: 'fixed',
        placement: this.placementValue
      }
    }

    return $(this.popoverTarget).popover(options)
  }

  content () {
    if (this.hasContentTarget) {
      const content = this.contentTarget.outerHTML
      this.contentTarget.remove()
      return content
    } else {
      const frame = document.createElement('turbo-frame')
      frame.id = 'popover-content'
      frame.src = this.urlValue
      frame.loading = 'lazy'
      frame.innerHTML = '<p>Loading...</p>'
      frame.addEventListener('turbo:frame-load', () => this.popover.popover('update'))
      return frame
    }
  }
}
