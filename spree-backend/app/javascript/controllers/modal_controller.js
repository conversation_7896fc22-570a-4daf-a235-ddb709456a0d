import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static values = {
    refreshOnClose: { type: Boolean, default: false }
  }

  connect () {
  }

  disconnect () {
    const turboFrame = this.element.closest('turbo-frame')
    if (turboFrame) turboFrame.innerHTML = ''
  }

  close () {
    const turboFrame = this.element.closest('turbo-frame')
    if (turboFrame) turboFrame.innerHTML = ''

    if (this.refreshOnCloseValue) Turbo.visit(window.location)
  }

  handleKeyup (e) {
    if (e.code === 'Escape') this.close()
  }
}
