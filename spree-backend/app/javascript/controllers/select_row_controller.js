import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['row']

  select (event) {
    Array.from(this.element.children[1].children).forEach((child) => {
      if (child.className === 'selected') {
        child.className = ''
        child.style = ''
      }
    })
    event.currentTarget.classList.add('selected')
    event.currentTarget.style.backgroundColor = '#a7c0db42'
    const variantId = event.currentTarget.dataset.variantId
    const nextButton = document.getElementById('next_button')
    nextButton.href = '/old_admin/products/scan_vin_to_add_stock?variant=' + variantId
    nextButton.classList.remove('disabled')
  }
}
