import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [
    'item',
    'scan',
    'nextbutton'
  ]

  connect () {
  }

  nextStep (event) {
    const checkedItems = this.itemTargets.filter((item) => item.checked)
    checkedItems.forEach(item => {
      const elementToAdd = item.parentElement.parentElement
      const clonedElement = elementToAdd.cloneNode(true)
      clonedElement.children[0].remove()
      const tableSecondBody = elementToAdd.parentElement.parentElement.nextElementSibling.children[1]
      tableSecondBody.appendChild(clonedElement)
    })

    const tableSeconds = document.querySelectorAll('.table-2')
    const tableFirsts = document.querySelectorAll('.table-1')
    tableFirsts.forEach(table => {
      table.classList.add('d-none')
    })
    tableSeconds.forEach(table => {
      table.classList.remove('d-none')
    })
    const tableSecondbtn = document.getElementById('btn-second-table')
    const tableFirstbtn = document.getElementById('btn-first-table')
    tableFirstbtn.classList.add('d-none')
    tableSecondbtn.classList.remove('d-none')

    this.scanTarget.classList.add('d-none')
    const tbodies = document.querySelectorAll('.table-2 tbody')
    tbodies.forEach(function (tbody) {
      if (tbody && tbody.children.length === 0) {
        tbody.closest('.table12-header').classList.add('d-none')
      }
    }
    )
  }

  backStep (event) {
    const stockLocationId = event.currentTarget.dataset.locationId;
    if (stockLocationId){
      document.querySelectorAll('[class*="remove_stock_unit_"]').forEach(el => {
        el.classList.add('d-none');
      });
      document.querySelectorAll(`tr.remove_stock_unit_${stockLocationId}`).forEach(el => {
        el.classList.remove('d-none');
      });
      const selectElement = document.getElementById("stock-location-select");
      if (selectElement) {
        selectElement.value = stockLocationId;
        selectElement.dispatchEvent(new Event("change"));
      }
    }
    const tableSeconds = document.querySelectorAll('.table-2')
    const tableFirsts = document.querySelectorAll('.table-1')
    tableFirsts.forEach(table => {
      table.classList.remove('d-none')
    })
    tableSeconds.forEach(table => {
      table.classList.add('d-none')
    })
    const tableSecondbtn = document.getElementById('btn-second-table')
    const tableFirstbtn = document.getElementById('btn-first-table')
    tableFirstbtn.classList.remove('d-none')
    tableSecondbtn.classList.add('d-none')
    tableSeconds.forEach(table => {
      const tableSecondBody = table.children[1]
      while (tableSecondBody.firstChild) {
        tableSecondBody.removeChild(tableSecondBody.firstChild)
      }
    })

    this.scanTarget.classList.remove('d-none')
    const tbodies = document.querySelectorAll('.table-2 tbody')
    tbodies.forEach(function (tbody) {
      if (tbody && tbody.children.length === 0) {
        tbody.closest('.table12-header')?.classList?.remove('d-none')
      }
    }
    )
  }

  removeStockCount (event) {
    const selectedItemCount = this.itemTargets.filter((item) => item.checked).length
    if (document.getElementById('checkedSum')) {
      document.getElementById('checkedSum').innerHTML = selectedItemCount
    }

    this.nextbuttonTarget.disabled = !(selectedItemCount && selectedItemCount > 0)

    if (event.target.checked) {
      document.getElementById('before_select')?.classList?.add('d-none')
      document.getElementById('after_select')?.classList?.remove('d-none')
    } else if (selectedItemCount === 0) {
      document.getElementById('before_select')?.classList?.remove('d-none')
      document.getElementById('after_select')?.classList?.add('d-none')
    }
  }
}
