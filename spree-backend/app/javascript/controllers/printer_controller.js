import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['target']

  print (e) {
    e.preventDefault()
    e.stopPropagation()

    const url = this.buildUrl(e)
    // const location = window.location

    const iframe = document.createElement('iframe')
    iframe.src = url
    iframe.hidden = true

    document.body.appendChild(iframe)
    iframe.contentWindow.addEventListener('afterprint', () => {
      iframe.remove()
      // Turbo.visit(location)
    })
    iframe.onload = () => iframe.contentWindow.print()
  }

  buildUrl (e) {
    const url = e.target.dataset.url || e.target.action
    const params = e.target.tagName === 'FORM' ? new URLSearchParams(new FormData(e.target)) : new URLSearchParams()
    const reprintParams = e.currentTarget.dataset
    if (reprintParams.stockItemUnitId) {
      params.append('stock_item_unit_id', reprintParams.stockItemUnitId)
    } else {
      this.targetTargets.forEach((it) => {
        if (it.checked) {
          params.append(it.name, it.value)
        }
      })
    }
    return url.includes('?') ? `${url}&${params}` : `${url}?${params}`
  }
}
