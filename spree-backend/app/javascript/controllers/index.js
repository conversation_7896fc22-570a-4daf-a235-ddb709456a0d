import PopoverController from './popover_controller'
import ModalC<PERSON>roller from './modal_controller'
import Select2<PERSON><PERSON>roller from './select2_controller'
import StockLocationSectionController from './stock_location_section_controller'
import ModalFrameController from './modal_frame_controller'
import CheckboxController from './checkbox_controller'
import FlatpickrController from './flatpickr_controller'
import NestedForm from 'stimulus-rails-nested-form'
import BatchAddStock from './batch_add_stock_controller'
import PrinterController from './printer_controller'
import BatchRestoreStock from './batch_restore_stock_controller'
import FormController from './form_controller'
import ProductSelectorController from './product_selector_controller'
import ToggleController from './toggle_controller'
import DropdownController from './dropdown_controller'
import VinInputController from './vin_input_controller'
import UpdateOrderController from './update_order_controller'
import ExpandCollapseController from './expand_collapse_controller'
import Batch<PERSON>ock<PERSON><PERSON>roller from './batch_lock_controller'
import Listing<PERSON>ontroller from './listing_controller'
import AddStockController from './add_stock_controller'
import SelectRowController from './select_row_controller'
import AddImageController from './add_image_controller'
import EasypostController from './easypost_controller'
import BuyPostageController from './buy_postage_controller'
import ConfirmController from './confirm_controller'
import ShippingMethodController from './shipping_method_controller'
import OrderController from './order_controller'
import ProductController from './product_controller'
import RecommendedPriceController from './recommended_price_controller'
import RecommendedPricesController from './recommended_prices_controller'
import WalmartBuypostageController from './walmart_buy_postage_controller'
import AmazonBuypostageController from './amazon_buy_postage_controller'

SpreeDashboard.application.register('popover', PopoverController)
SpreeDashboard.application.register('modal', ModalController)
SpreeDashboard.application.register('select2', Select2Controller)
SpreeDashboard.application.register('stock-location-section', StockLocationSectionController)
SpreeDashboard.application.register('modal-frame', ModalFrameController)
SpreeDashboard.application.register('checkbox', CheckboxController)
SpreeDashboard.application.register('flatpickr', FlatpickrController)
SpreeDashboard.application.register('nested-form', NestedForm)
SpreeDashboard.application.register('batch-add-stock', BatchAddStock)
SpreeDashboard.application.register('printer', PrinterController)
SpreeDashboard.application.register('batch-restore-stock', BatchRestoreStock)
SpreeDashboard.application.register('form', FormController)
SpreeDashboard.application.register('toggle', ToggleController)
SpreeDashboard.application.register('dropdown', DropdownController)
SpreeDashboard.application.register('product-selector', ProductSelectorController)
SpreeDashboard.application.register('vin-input', VinInputController)
SpreeDashboard.application.register('update-order', UpdateOrderController)
SpreeDashboard.application.register('expand-collapse', ExpandCollapseController)
SpreeDashboard.application.register('batch-lock', BatchLockController)
SpreeDashboard.application.register('listing', ListingController)
SpreeDashboard.application.register('add-stock', AddStockController)
SpreeDashboard.application.register('select-row', SelectRowController)
SpreeDashboard.application.register('addImage', AddImageController)
SpreeDashboard.application.register('easypost', EasypostController)
SpreeDashboard.application.register('buy-postage', BuyPostageController)
SpreeDashboard.application.register('confirm', ConfirmController)
SpreeDashboard.application.register('shipping-method', ShippingMethodController)
SpreeDashboard.application.register('order', OrderController)
SpreeDashboard.application.register('product', ProductController)
SpreeDashboard.application.register('recommended-price', RecommendedPriceController)
SpreeDashboard.application.register('recommended-prices', RecommendedPricesController)
SpreeDashboard.application.register('walmart-buy-postage', WalmartBuypostageController)
SpreeDashboard.application.register('amazon-buy-postage', AmazonBuypostageController)
