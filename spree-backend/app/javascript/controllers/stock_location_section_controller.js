import Select2Controller from './select2_controller'

export default class extends Select2Controller {
  searchParameters (params) {
    const term = params.term
    const ransack = this.element.dataset.ransack || this.controlTarget.dataset.ransack || 'term'
    const q = {}

    q[ransack] = term
    q.stock_location_id_eq = this.targetValue('stock_location')

    return {
      q,
      page: params.page || 1
    }
  }
}
