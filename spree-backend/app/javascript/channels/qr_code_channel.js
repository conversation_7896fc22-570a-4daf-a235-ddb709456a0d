import consumer from './consumer'

const openModal = (response, saleChannelId) => {
  // Removing the error message from the upc number search modal
  const upcSearchErrorMessageBlock = document.getElementById('productNotFound')
  upcSearchErrorMessageBlock.classList.add('d-none')

  // Closing the upc search modal
  const closeTextSearchModalButton = document.getElementById('ebay-upc-number-search')
  closeTextSearchModalButton?.click()

  const buttonElement = document.getElementById('openUpcProductModalButton')
  buttonElement.click()

  const ebayRecommendedItemSpecificBody = document.getElementById('upcModalItemSpecific')
  ebayRecommendedItemSpecificBody.innerHTML = ''

  const headerTag = document.createElement('p')
  headerTag.classList.add('font-weight-bold', 'text-center')
  headerTag.textContent = 'Select the item that is close to your product and will prefill the recommended item details in your listing draft'
  ebayRecommendedItemSpecificBody.appendChild(headerTag)

  const tableTag = document.createElement('table')
  tableTag.classList.add('table', 'w-100', 'table-borderless')
  ebayRecommendedItemSpecificBody.appendChild(tableTag)

  const tableBodyTag = document.createElement('tbody')
  tableTag.appendChild(tableBodyTag)

  response.itemSummaries.forEach((product, index) => {
    const tableTr = document.createElement('tr')
    tableBodyTag.appendChild(tableTr)

    let tableTd = document.createElement('td')
    const image = document.createElement('img')
    image.style.width = '100px'
    image.style.height = '100px'
    image.src = product?.image?.imageUrl
    tableTd.appendChild(image)
    tableTr.appendChild(tableTd)

    tableTd = document.createElement('td')
    tableTd.textContent = product.title
    tableTr.appendChild(tableTd)

    tableTd = document.createElement('td')
    const buttonTag = document.createElement('button')
    buttonTag.classList.add('btn', 'btn-primary')
    buttonTag.textContent = 'Select'
    buttonTag.onclick = function () {
      const leafCategoryIds = product.leafCategoryIds[0]
      const categoryName = product.categories.find(category => category.categoryId === leafCategoryIds)?.categoryName
      getProduct(product.epid, categoryName, leafCategoryIds, saleChannelId)
    }
    tableTd.appendChild(buttonTag)
    tableTr.appendChild(tableTd)
  })
}

document.getElementById('nextBtntoken')?.addEventListener('click', makeNextForm)
document.getElementById('prevBtntoken')?.addEventListener('click', makeNextForm)

function makeNextForm (event) {
  event.preventDefault()
  const url = '/old_admin/listings/amazon_catalog_search'
  const params = {
    sale_channel_id: this.dataset.saleChannelId,
    keyword: this.dataset.keyword,
    token: this.dataset.nextToken,
    brand_name: this.dataset.brand_name
  }
  const queryString = new URLSearchParams(params).toString()
  const fullUrl = `${url}?${queryString}`

  fetch(fullUrl, {
    method: 'GET', // or 'GET'
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

Array.from(document.getElementsByClassName('amazon_image_field')).forEach(input => {
  input.addEventListener('change', function (event) {
    if (event.target.files.length > 0) {
      const file = event.target.files[0]
      const imgElement = document.querySelector(`.upload-image-${event.target.id.replace('file-input-', '')}`)
      const reader = new FileReader()

      reader.onload = function (e) {
        imgElement.src = e.target.result
      }

      reader.readAsDataURL(file)
    }
  })
})

const openAmazonCatalogSearchModal = (response, saleChannelId, keyword, brandName) => {
  // Removing the error message from the upc number search modal
  const upcSearchErrorMessageBlock = document.getElementById('amazonProductNotFound')
  upcSearchErrorMessageBlock.classList.add('d-none')

  const upcProductModalCloseButton = document.getElementById('upcProductModalCloseButton')
  upcProductModalCloseButton?.click()

  // Closing the upc search modal
  const closeTextSearchModalButton = document.getElementById('amazon_catalog_search_close_button')
  closeTextSearchModalButton?.click()

  const buttonElement = document.getElementById('openUpcProductModalButton')
  buttonElement.click()
  const prevBtn = document.getElementById('prevBtntoken')
  const nextBtn = document.getElementById('nextBtntoken')
  if (response?.pagination?.previousToken == null) {
    prevBtn.classList.add('d-none')
  } else {
    prevBtn.classList.remove('d-none')
    prevBtn.dataset.nextToken = response.pagination.previousToken
    prevBtn.dataset.keyword = keyword
    prevBtn.dataset.brand_name = brandName
  }

  if (response?.pagination?.nextToken == null) {
    nextBtn.classList.add('d-none')
  } else {
    nextBtn.classList.remove('d-none')
    nextBtn.dataset.nextToken = response.pagination.nextToken
    nextBtn.dataset.keyword = keyword
    nextBtn.dataset.brand_name = brandName
  }

  const ebayRecommendedItemSpecificBody = document.getElementById('upcModalItemSpecific')
  ebayRecommendedItemSpecificBody.innerHTML = ''

  const headerTag = document.createElement('p')
  headerTag.classList.add('font-weight-bold', 'text-center')
  headerTag.textContent = 'Select the item that is close to your product and will prefill the recommended item details in your listing draft'
  ebayRecommendedItemSpecificBody.appendChild(headerTag)

  const tableTag = document.createElement('table')
  tableTag.classList.add('table', 'w-100', 'table-border')
  ebayRecommendedItemSpecificBody.appendChild(tableTag)

  const tableBodyTag = document.createElement('tbody')
  tableTag.appendChild(tableBodyTag)
  response.items.forEach((product, index) => {
    const tableTr = document.createElement('tr')
    tableBodyTag.appendChild(tableTr)
    let tableTd = document.createElement('td')
    const image = document.createElement('img')
    image.style.width = '100px'
    image.style.height = '100px'
    image.src = product.images[0]?.images[0]?.link
    tableTd.appendChild(image)
    tableTr.appendChild(tableTd)

    tableTd = document.createElement('td')
    tableTd.textContent = product.summaries[0].itemName
    tableTr.appendChild(tableTd)

    tableTd = document.createElement('td')
    tableTd.textContent = product.identifiers[0]?.identifiers[0]?.identifier
    tableTr.appendChild(tableTd)

    tableTd = document.createElement('td')
    const buttonTag = document.createElement('button')
    buttonTag.classList.add('btn', 'btn-primary')
    buttonTag.textContent = 'Select'
    buttonTag.value = product.asin
    buttonTag.dataset.listingId = window.location.href.split('/')[5]
    buttonTag.onclick = function () {
      const asin = this.value
      const listingId = this.dataset.listingId
      getCatalogItem(asin, listingId)
    }
    tableTd.appendChild(buttonTag)
    tableTr.appendChild(tableTd)
  })
}

const getCatalogItem = (asin, listingId) => {
  const url = `/old_admin/listings/${listingId}/get_catalog_items`
  const params = { asin }

  $.ajax({
    url,
    type: 'GET',
    data: params,
    dataType: 'json',
    success: function (response) {
      // Close catelog product listing modal
      const modal = document.querySelector('#openUpcProductModal')
      const closeButton = modal.querySelector('.close')
      closeButton.click()
      const dropdown = document.getElementById('listing_taxon')
      const option = document.createElement('option')

      option.value = response.categoryId
      option.text = response.categoryName
      option.selected = true
      dropdown.add(option)
      const productTypeUrl = '/old_admin/listings/load_product_types'
      const formParams = {
        category_id: response.categoryId,
        id: response.listingId,
        sale_channel_id: response.sale_channel_id,
        catalog_item_payload: response.payload
      }
      $.ajax({
        url: productTypeUrl,
        type: 'POST',
        data: formParams,
        dataType: 'script',
        success: function (response) {
          // Handle the success response here
        },
        error: function (xhr, status, error) {
          // Handle the error response here
        }
      })
    }
  })
}

const getProduct = (epid, categoryName, leafCategoryIds, saleChannelId) => {
  const url = '/old_admin/scanned_items/get_product_with_epid'
  const params = { epid, sale_channel_id: saleChannelId }

  $.ajax({
    url,
    type: 'GET',
    data: params,
    dataType: 'json',
    success: function (response) {
      // Close catelog product listing modal
      const modal = document.getElementById('openUpcProductModal')
      const closeButton = modal.querySelector('.close')
      closeButton.click()

      // Add category_id to the category field
      const dropdown = document.getElementById('listing_taxon')
      const option = document.createElement('option')

      option.value = leafCategoryIds + '_' + categoryName
      option.text = categoryName
      option.selected = true
      dropdown.add(option)

      const result = {}
      for (const item of response.aspects) {
        result[item.localizedName] = item.localizedValues
      }
      // Handle the success response here
      const payload = { category_id: leafCategoryIds, scanned_product_aspects: result, sale_channel_id: saleChannelId }
      window.getItemSpecifics(payload)
    },
    error: function (xhr, status, error) {
      alert('There is no specifics on this item in the catalog.')
      const button = $('#upcProductModalCloseButton')[0]
      button.click()
    }
  })
}

const showErrorMessage = (data) => {
  const upcSearchErrorMessageBlock = document.querySelector('#productNotFound')
  if (upcSearchErrorMessageBlock) {
    upcSearchErrorMessageBlock.classList.remove('d-none')
  }
  const amazonSearchErrorMessageBlock = document.querySelector('#amazonProductNotFound')
  if (amazonSearchErrorMessageBlock) {
    amazonSearchErrorMessageBlock.classList.remove('d-none')
  }
}

const closeModal = (data) => {
  let closeButton = document.querySelector('.close.qr-code')
  closeButton?.click()

  closeButton = document.querySelector('#upc-scan-close-button')
  closeButton?.click()
}

const selectStock = (data) => {
  const checkbox = document.querySelector(`input[type="checkbox"][data-scan-number="${data.number}"]`)

  if (checkbox && !checkbox.checked) {
    checkbox.checked = true
    checkbox.dispatchEvent(new Event('input'))
  }
}

const restoreStock = (data) => {
  const { number } = data
  window.dispatchEvent(new CustomEvent('restore-stock', { detail: { number } }))
}

const checkStock = (data) => {
  const { number } = data
  const url = `/old_admin/stock_item_units/${number}`
  Turbo.visit(url)
}

const input = (data) => {
  const field = document.querySelector(data.selector)
  if (field) field.value = data.value
}

const inputVin = (data) => {
  const field = document.activeElement?.closest(data.selector) || document.querySelector(data.selector) || document.getElementsByClassName('active-green')[0].children[0]
  if (field) {
    field.value = data.vin
    field.dispatchEvent(new Event('input', { bubbles: true }))
  }
}

const addStock = (data) => {
  const button = document.getElementById('vin-value')
  button.value = data.id

  const form = document.getElementById('submit-vin-value')
  form.click()
  document.getElementById('scan_vin_image').classList.add('d-none')
  setTimeout(() => { document.getElementById('modal_highlight').firstElementChild.innerHTML = 'You can scan more VIN codes' }, 3000)
}

const upcSearch = (data) => {
  $.ajax({
    url: '/old_admin/products/scan_and_fetch_upc',
    type: 'GET',
    data: {
      variant_upc: data.id
    },
    success: function (response) {
      const variantId = response.variant_id
      const nextButton = document.getElementById('next_button')
      if (variantId != null) {
        nextButton.href = '/old_admin/products/scan_vin_to_add_stock?variant=' + variantId
        nextButton.click()
      } else {
        const button = document.getElementById('variant-upc-value')
        button.value = data.id

        const form = document.getElementById('submit-variant-upc-value')
        form.click()
      }
    },
    error: function (error) {
      console.error('Error:', error)
    }
  })
}

const inputUpc = (data) => {
  const field = document.getElementById(data.selector)
  if (field) {
    field.value = data.vin
  }
}

consumer.subscriptions.create('QrCodeChannel', {
  connected () {
    console.log('QrCodeChannel connected!')
  },

  // Called when the subscription has been terminated by the server
  disconnected () {
  },

  // Called when there's incoming data on the websocket for this channel
  received (data) {
    switch (data.type) {
      case 'ebay_catalog':
        openModal(data.response, data.sale_channel_id)
        break
      case 'amazon_catalog':
        openAmazonCatalogSearchModal(data.response, data.sale_channel_id, data.keyword, data.brand_name)
        break
      case 'show_error_message':
        showErrorMessage(data)
        break
      case 'close_modal':
        closeModal(data)
        break
      case 'refresh':
        Turbo.visit(window.location.href)
        break
      case 'select_stock':
        selectStock(data)
        break
      case 'restore_stock':
        restoreStock(data)
        break
      case 'check_stock':
        checkStock(data)
        break
      case 'input':
        input(data)
        break
      case 'scan_vin':
        inputVin(data)
        break
      case 'add_stock':
        addStock(data)
        break
      case 'upc_search':
        upcSearch(data)
        break
      case 'search_product_vin':
        inputUpc(data)
        break
    }
  }
})
