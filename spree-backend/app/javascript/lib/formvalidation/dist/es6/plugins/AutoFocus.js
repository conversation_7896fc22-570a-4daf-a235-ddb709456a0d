import t from"../core/Plugin";import i from"./FieldStatus";export default class s extends t{constructor(t){super(t);this.fieldStatusPluginName="___autoFocusFieldStatus";this.opts=Object.assign({},{onPrefocus:()=>{}},t);this.invalidFormHandler=this.onFormInvalid.bind(this)}install(){this.core.on("core.form.invalid",this.invalidFormHandler).registerPlugin(this.fieldStatusPluginName,new i)}uninstall(){this.core.off("core.form.invalid",this.invalidFormHandler).deregisterPlugin(this.fieldStatusPluginName)}onFormInvalid(){const t=this.core.getPlugin(this.fieldStatusPluginName);const i=t.getStatuses();const s=Object.keys(this.core.getFields()).filter((t=>i.get(t)==="Invalid"));if(s.length>0){const t=s[0];const i=this.core.getElements(t);if(i.length>0){const s=i[0];const e={firstElement:s,field:t};this.core.emit("plugins.autofocus.prefocus",e);this.opts.onPrefocus(e);s.focus()}}}}