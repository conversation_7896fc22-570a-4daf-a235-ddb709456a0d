import e from"../utils/classSet";import t from"./Framework";export default class n extends t{constructor(e){super(Object.assign({},{formClass:"fv-plugins-tachyons",messageClass:"small",rowInvalidClass:"red",rowPattern:/^(.*)fl(.*)$/,rowSelector:".fl",rowValidClass:"green"},e))}onIconPlaced(t){const n=t.element.getAttribute("type");const s=t.element.parentElement;if("checkbox"===n||"radio"===n){s.parentElement.insertBefore(t.iconElement,s.nextSibling);e(t.iconElement,{"fv-plugins-icon-check":true})}}}