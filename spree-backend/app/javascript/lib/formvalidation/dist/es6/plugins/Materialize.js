import e from"../utils/classSet";import l from"./Framework";export default class t extends l{constructor(e){super(Object.assign({},{eleInvalidClass:"validate invalid",eleValidClass:"validate valid",formClass:"fv-plugins-materialize",messageClass:"helper-text",rowInvalidClass:"fv-invalid-row",rowPattern:/^(.*)col(\s+)s[0-9]+(.*)$/,rowSelector:".row",rowValidClass:"fv-valid-row"},e))}onIconPlaced(l){const t=l.element.getAttribute("type");const a=l.element.parentElement;if("checkbox"===t||"radio"===t){a.parentElement.insertBefore(l.iconElement,a.nextSibling);e(l.iconElement,{"fv-plugins-icon-check":true})}}}