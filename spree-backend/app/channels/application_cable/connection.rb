# frozen_string_literal: true

module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user
    identified_by :current_tenant

    def connect
      reject_unauthorized_connection unless current_user
    end

    def current_user
      @current_user ||= authenticate
    end

    def current_tenant
      @current_tenant ||= parse_tenant_domain
    end

    private

    def parse_tenant_domain
      # elevator = Apartment::Elevators::CustomStoreSubdomain.new(nil)
      elevator = Apartment::Elevators::CustomStoreDomain.new(nil)
      elevator.parse_tenant_name(request)
    end

    def authenticate
      Apartment::Tenant.switch(current_tenant) do
        if request.params[:user_token].present?
          Spree::User.find_by(user_token: request.params[:user_token])
        else
          env["warden"].user
        end
      end
    end
  end
end
