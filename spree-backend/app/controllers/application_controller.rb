# frozen_string_literal: true

class ApplicationController < ActionController::Base
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :exception
  before_action :set_time_zone
  before_action :print_logs

  def print_logs
    Rails.logger.debug("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ Apartment Name:")
    Rails.logger.debug { "Apartment: #{Apartment::Tenant.current}" }

    Rails.logger.debug { "Currect User: [#{spree_current_user&.email}, #{spree_current_user&.id}]" }

    # Print session parameters
    Rails.logger.debug("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ Session Parameters:")
    session.each do |key, value|
      Rails.logger.debug("#{key}: #{value}")
    end

    # Print cookies
    Rails.logger.debug("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ Cookies:")
    cookies.each do |key, value|
      Rails.logger.debug("#{key}: #{value}")
    end
  end

  private

  def set_time_zone
    Time.zone = cookies["timezone"] || config.time_zone || "UTC"
  rescue ArgumentError => e # ArgumentError (Invalid Timezone
    Rails.logger.info("set_time_zone Exception #{e}")
    Time.zone = config.time_zone || "UTC"
  end
end
