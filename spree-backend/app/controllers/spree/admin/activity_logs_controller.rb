# frozen_string_literal: true

module Spree
  module Admin
    class ActivityLogsController < ResourceController
      layout "spree/layouts/admin"

      def index
        @permissions = current_user_permissions.find_permission
        if @permissions&.view_activity_log
        else
          message = "You are not authorized to do this action"
          flash[:error] = message
          redirect_to(admin_users_path)
          return
        end
        @search = Spree::ActivityLog.ransack(params[:q])
        @activity_logs = @search.result.order(created_at: :desc)
        @activity_logs = @activity_logs.order(params[:order]) if params[:order].present?
        @activity_logs = @activity_logs.page(params[:page]).per(15)
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end
    end
  end
end
