# frozen_string_literal: true

module Spree
  module Admin
    class OptionsController < Spree::Admin::BaseController
      SUPPORTED_RESOURCES = [
        "stock_location",
        "stock_location_section",
        "sale_channel",
        "variant",
      ]

      FETCHERS = SUPPORTED_RESOURCES.each_with_object({}.with_indifferent_access) do |resource, m|
        fetcher = "fetch_#{resource}_options"
        m[resource] = fetcher
      end

      def index
        @options = send(fetcher)
        fetch_option_values if params[:resource] == "variant"
        render(json: @options, status: :ok)
      end

      private

      def fetch_option_values
        @options[:options].reject! do |option|
          variant = Spree::Variant.find_by(id: option[:id])
          next true if variant.blank?

          names = variant.option_values.pluck(:name).join(",")
          next true if names.blank?

          option[:text] = names
          false
        end
      end

      def fetcher
        resource = params[:resource].to_s.downcase
        FETCHERS.fetch(resource) { raise "Unsupported resource: #{resource}" }
      end

      def fetch_variant_options
        fetch(Spree::Variant)
      end

      def fetch_sale_channel_options
        fetch(Spree::SaleChannel)
      end

      def fetch_stock_location_options
        fetch(Spree::StockLocation)
      end

      def fetch_stock_location_section_options
        fetch(StockLocationSection)
      end

      def fetch(relation, &block)
        block ||= ->(it) { it.to_option }

        if params[:selected].present?
          collection = relation.where(id: params[:selected]).all
          return { options: collection.map(&block).flatten, more: false }
        end

        q = relation.ransack(params[:q])
        collection = q.result.all

        { options: collection.map(&block).flatten, more: false }
      end
    end
  end
end
