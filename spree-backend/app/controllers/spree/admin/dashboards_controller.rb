# frozen_string_literal: true

module Spree
  module Admin
    class DashboardsController < Spree::Admin::BaseController
      def index
        @permissions = current_user_permissions.find_permission
        scope = Spree::Order.for_store(current_store).where.not(completed_at: nil)
        scope = scope.where(shipment_state: :ready)
        @new_orders = scope.count

        @inventory_alert_products = Spree::StockLocation.for_store(current_store).joins(:variants)
          .where("inventory_threshold is not null and count_on_hand < inventory_threshold and track_inventory = ?", true).count
        ebay_app = current_store.sale_channels.for_brand('eBay').first&.oauth_application
        @ebay_message_count = nil
        if ebay_app.present?
          @ebay_message_count = 10
        end
        @revenue_in_7days = revenue_query(Date.current.prev_day(7))
        @profit_in_7days = profit_query(Date.current.prev_day(7))
        @announcements = Spree::Announcement.for_store(current_store).order(updated_at: :desc).all
      end

      def payment_method_chart
        time_scale = :daily
        start_date = case params[:time_range]
        when "1d"
          time_scale = :hourly
          Time.zone.today
        when "10d"
          Time.zone.today.prev_day(9)
        when "30d"
          Time.zone.today.prev_day(29)
        end
        chart_period = start_date.beginning_of_day..Time.zone.today.end_of_day
        query_sql = sales_chart_query(time_scale, chart_period).to_sql
        chart_data = ActiveRecord::Base.connection.exec_query(query_sql)
        respond_to do |format|
          format.html { render(:index) }
          format.json { render(json: chart_data) }
        end
      end

      def financial_chart
        time_scale = :daily
        start_date = case params[:time_scale_type]
        when "1d"
          time_scale = :hourly
          Time.zone.today
        when "3d"
          Time.zone.today.prev_day(2)
        when "5d"
          Time.zone.today.prev_day(4)
        when "7d"
          Time.zone.today.prev_day(6)
        when "14d"
          Time.zone.today.prev_day(13)
        when "30d"
          Time.zone.today.prev_day(29)
        end
        revenue_data = revenue_query(start_date, time_scale)
        profit_data = profit_query(start_date, time_scale)
        respond_to do |format|
          format.html { render(:index) }
          format.json { render(json: { revenue_data: revenue_data, profit_data: profit_data }) }
        end
      end

      private

      def revenue_query(start_date, scale = :daily)
        time_scale_columns = financial_time_scale_columns(scale)
        time_scale_columns_select = time_scale_columns.collect do |time_scale_column|
          ::Spree::Report::QueryFragments.public_send(time_scale_column, "created_at")
        end
        query_sql = Spree::Report::QueryFragments
          .from_subquery(Spree::Order.for_store(current_store).where(created_at: start_date.beginning_of_day..Time.zone.today.end_of_day)
          .where.not(payment_state: "void"))
          .group(*time_scale_columns.collect(&:to_s))
          .order(*time_scale_columns)
          .project(
            *time_scale_columns_select,
            "SUM(total) as order_amount",
          ).to_sql
        ActiveRecord::Base.connection.exec_query(query_sql)
      end

      def profit_query(start_date, scale = :daily)
        order_with_line_items_ar = Arel::Table.new(:order_with_line_items)
        time_scale_columns = financial_time_scale_columns(scale)
        zero = Arel::Nodes.build_quoted(0.0)
        query_sql = Spree::Report::QueryFragments
          .from_subquery(order_with_line_items(
            start_date.beginning_of_day..Time.zone.today.end_of_day,
            time_scale_columns,
          ),
            as: :order_with_line_items)
          .group(*time_scale_columns.collect(&:to_s))
          .order(*time_scale_columns)
          .project(
            *time_scale_columns,
            Spree::Report::QueryFragments.if_null(
              Spree::Report::QueryFragments.sum(order_with_line_items_ar[:profit_loss]), zero
            ).as("profit_loss"),
          ).to_sql
        ActiveRecord::Base.connection.exec_query(query_sql)
      end

      def financial_time_scale_columns(time_scale)
        case time_scale
        when :hourly
          [:day, :hour]
        when :daily
          [:year, :month, :day]
        end
      end

      def order_with_line_items(period, time_scale_columns)
        line_item_ar = Spree::LineItem.arel_table
        Spree::Order.for_store(current_store)
          .where.not(completed_at: nil)
          .where(created_at: period)
          .where.not(payment_state: "void")
          .joins(:line_items)
          .group("spree_orders.id", *time_scale_columns.collect(&:to_s))
          .select(
            *time_scale_columns.collect do |column|
              ::Spree::Report::QueryFragments.public_send(column, "spree_orders.created_at")
            end,
            "(spree_orders.item_total - SUM(#{Spree::Report::QueryFragments.if_null(
              line_item_ar[:cost_price],
              line_item_ar[:price],
            ).to_sql} * spree_line_items.quantity)) as profit_loss",
          )
      end

      def payment_chart_query(time_scale, chart_period)
        time_scale_columns = Spree::Report::QueryTimeScale.time_scale_columns(time_scale)
        Spree::Report::QueryFragments
          .from_subquery(payments(time_scale, chart_period))
          .group(*time_scale_columns.collect(&:to_s), "payment_method_name")
          .order(*time_scale_columns)
          .project(
            *time_scale_columns,
            "payment_method_name",
            "SUM(payment_amount) as payment_amount",
          )
      end

      def payments(time_scale, chart_period)
        Spree::Payment.for_store(current_store)
          .joins(:payment_method)
          .where(spree_payments: { created_at: chart_period })
          .where("spree_payments.state NOT IN (?, ?, ?) and spree_payments.amount > 0", "void", "failed", "invalid")
          .joins(:order)
          .where.not(spree_orders: { payment_state: "void" })
          .select(
            *Spree::Report::QueryTimeScale.select(time_scale, "spree_payments"),
            "spree_payment_methods.name  as payment_method_name",
            "spree_payments.amount       as payment_amount",
          )
      end

      def sales_chart_query(time_scale, chart_period)
        time_scale_columns = Spree::Report::QueryTimeScale.time_scale_columns(time_scale)
        Spree::Report::QueryFragments
          .from_subquery(orders(time_scale, chart_period))
          .group(*time_scale_columns.collect(&:to_s), "payment_method_name")
          .order(*time_scale_columns)
          .project(
            *time_scale_columns,
            "CASE WHEN application_name IS NULL THEN channel ELSE channel || '-' || application_name END as payment_method_name",
            "SUM(payment_amount) as payment_amount",
          )
      end

      def orders(time_scale, chart_period)
        Spree::Order.for_store(current_store)
          .joins("left join spree_sale_channels on spree_orders.sale_channel_id = spree_sale_channels.id")
          .joins("left join spree_oauth_applications on spree_sale_channels.id = spree_oauth_applications.sale_channel_id")
          .where(spree_orders: { created_at: chart_period })
          .where(spree_orders: { state: "complete" })
          .select(
            *Spree::Report::QueryTimeScale.select(time_scale, "spree_orders"),
            "spree_oauth_applications.name  as application_name",
            "spree_orders.total             as payment_amount",
            "spree_orders.channel           as channel",
          )
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end
    end
  end
end
