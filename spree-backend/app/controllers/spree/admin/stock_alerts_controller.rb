# frozen_string_literal: true

module Spree
  module Admin
    class StockAlertsController < Spree::Admin::BaseController
      def index
        @permissions = current_user_permissions.find_permission
        @stock_items = Spree::StockItem
          .joins(:stock_location, variant: [:product])
          .includes(:stock_location, variant: { product: [:master, :translations] })
          .where(spree_stock_locations: { store: current_store })
          .where(spree_variants: { track_inventory: true })
          .where.not(spree_stock_items: { inventory_threshold: nil })
          .where("spree_stock_items.count_on_hand < spree_stock_items.inventory_threshold")
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end
    end
  end
end
