# frozen_string_literal: true

module Spree
  module Admin
    class RecommendedPricesController < BaseController
      include Spree::Admin::ProductConcern

      skip_before_action :verify_authenticity_token, only: [:update_item]
      before_action :find_variant
      before_action :find_listing_inventory
      before_action :load_recommended_price

      layout -> { false if turbo_frame_request? }

      def settings
      end

      def show
        # Fix old records without listing id
        if @recommended_price&.persisted?
          @recommended_price&.update(listing_inventory_id: params[:listing_inventory_id]) unless @recommended_price&.listing_inventory_id
        end

        unless @recommended_price&.persisted?
          # First invoke for this variant
          @recommended_price = PriceAnalytics::Request.new(
            store: current_store,
            user: spree_current_user,
            variant: @variant,
            listing_inventory: @listing_inventory,
          ).execute
        end
      end

      def update
        @recommended_price.update(update_params)
      end

      def refresh
        if @recommended_price&.wait?
          PriceAnalytics::Process.new(
            store: current_store,
            user: spree_current_user,
            recommended_price: @recommended_price,
          ).execute
        else
          unless params[:new_request] == "0"
            PriceAnalytics::Request.new(
              store: current_store,
              user: spree_current_user,
              variant: @variant,
              listing_inventory: @listing_inventory,
              recommended_price: @recommended_price,
            ).execute
          end
        end
      end

      def apply
        if @recommended_price.active?
          idx = params[:strategy].to_i
          @recommended_price.apply_indexed_price!(idx)
        end
      end

      def recalc
        @recommended_price = PriceAnalytics::Recalc.new(
          store: current_store,
          user: spree_current_user,
          recommended_price: @recommended_price,
        ).execute
      end

      def ignore_item
        item = @recommended_price.items.detect { |item| item[:id] == params[:item_id] }
        if item
          PriceAnalytics::ChangeItemStatus.new(
            store: current_store,
            user: spree_current_user,
            recommended_price: @recommended_price,
            item: item,
            state: "ignored",
          ).execute
        end
      end

      def accept_item
        item = @recommended_price.items.detect { |item| item[:id] == params[:item_id] }
        if item
          PriceAnalytics::ChangeItemStatus.new(
            store: current_store,
            user: spree_current_user,
            recommended_price: @recommended_price,
            item: item,
            state: "accepted",
          ).execute
        end
      end

      def update_item
        item = @recommended_price.items.detect { |item| item[:id] == params[:item_id] }
        if item
          PriceAnalytics::ChangeItemQuantity.new(
            store: current_store,
            user: spree_current_user,
            recommended_price: @recommended_price,
            item: item,
            quantity: params[:quantity],
          ).execute
        end
      end

      protected

      def find_variant
        # TODO: Check cancan
        @variant = Spree::Variant.find(params[:variant_id])
      end

      def find_listing_inventory
        @listing_inventory = Spree::ListingInventory.find_by(id: params[:listing_inventory_id])
      end

      def load_recommended_price
        @recommended_price = @listing_inventory&.recommended_price || @listing_inventory&.build_recommended_price(variant: @variant)
      end

      def update_params
        params.require(:recommended_price).permit(:auto_update, :auto_update_period)
      end
    end
  end
end
