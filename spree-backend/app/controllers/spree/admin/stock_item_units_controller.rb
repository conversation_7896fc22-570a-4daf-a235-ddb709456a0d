# frozen_string_literal: true

module Spree
  module Admin
    class StockItemUnitsController < Spree::Admin::ResourceController
      before_action :check_permission, only: [:batch_lock, :batch_create]

      def index
        @product = current_store.products.friendly.find(params[:product_id])
        @search = @product.stock_item_units.includes(:stock_item).ransack(params[:q])
        @stock_item_units = @search.result.group_by(&:stock_item)
      end

      def show
        @permission = spree_current_user&.spree_roles&.first&.permission&.view_product_cost_price
        @stock_item_unit = find_resource
        if @stock_item_unit.present?
          respond_to do |format|
            format.html
            format.json { render(json: @stock_item_unit.to_json) }
          end
        else
          @stock_item_units = model_class.where(vendor_inventory_number: params[:id])

          if @stock_item_units.present?
            render(:show_stock_items)
          else
            handle_resource_not_found
          end
        end
      end

      def edit
        @details_hash = params.dig("initial_value")
        @stock_item_unit = Spree::StockItemUnit.includes(stock_item: :variant).find(params[:id])
      end

      def update
        @stock_item_unit = Spree::StockItemUnit.includes(stock_item: :variant).find(params[:id])
        @details_hash = {
          vendor_receipt_number: (params["vendor_receipt_number"] || @stock_item_unit.vendor_receipt_number)&.to_s&.strip,
          vendor_inventory_number: (params["vendor_inventory_number"] || @stock_item_unit.vendor_inventory_number)&.to_s&.strip,
          vendor_receipt_date: params["vendor_receipt_date"] || @stock_item_unit.vendor_receipt_date,
          expiry_date: params["expiry_date"] || @stock_item_unit.expiry_date,
          vendor_inventory_cost: (params["vendor_inventory_cost"] || @stock_item_unit.vendor_inventory_cost)&.to_s&.strip,
          remark: (params["remark"] || @stock_item_unit.remark)&.to_s&.strip,
          pack_size: (params["pack_size"] || @stock_item_unit.pack_size)&.to_s&.strip,
          expiry_date_type: params["expiry_type"] || @stock_item_unit.expiry_type,
        }
        if @stock_item_unit.update(stock_item_unit_params)
          record_activity_log(@stock_item_unit)
          redirect_to(admin_stock_item_unit_path(@stock_item_unit)) if params["field_type"].blank?
        else
          render(:edit, status: :unprocessable_entity)
        end

        @updated_hash = {}
        if params["vin_scanned"].present?
          vin_related_keys = [
            :vendor_inventory_number,
            :vendor_receipt_number,
            :vendor_receipt_date,
            :vendor_inventory_cost,
            :expiry_date,
          ]
          vin_related_keys.each { |key| @updated_hash[key] = stock_item_unit_params[key.to_s] }
        elsif params["field_type"] == "expiry_date"
          @updated_hash[params["field_type"]] = params.dig("stock_item_unit", params["field_type"])
          @expiry_type = params["stock_item_unit"]["expiry_type"]
        else
          @updated_hash[params["field_type"]] = params.dig("stock_item_unit", params["field_type"])
        end
      end

      def batch_create
        @product = current_store.products.friendly.find(params[:product_id])
        @command = ::Admin::BatchCreateStockItemUnitCommand.call(
          stock_item_unit_params: params[:stock_item_units],
          current_user: spree_current_user,
          action_place: @product ? stock_admin_product_path(@product) : 'N/A',
        )
        @stock_item_units = @command.result

        if @command.success?
          notify_customers
          flash[:success] = "Stock added successfully!"
        else
          flash[:error] = "Failed to add stock: #{@command.errors.full_messages}"
        end
      end

      def batch_update
        @product = current_store.products.friendly.find(params[:product_id])
        command = ::Admin::BatchUpdateStockItemUnitCommand.call(
          product: @product,
          stock_item_unit_id: params[:stock_item_unit_id],
          attributes: batch_update_params,
          current_user: spree_current_user,
          action_place: @product ? stock_admin_product_path(@product) : 'N/A',
        )

        if command.success?
          notify_customers
          flash[:success] = "Stock updated successfully!"
        else
          flash[:error] = command.errors.full_messages.join(", ")
        end

        redirect_back(fallback_location: stock_admin_product_path(@product))
      end

      def batch_restore
        @product = current_store.products.friendly.find(params[:product_id])
      end

      def batch_lock
        @shipment = Spree::Shipment.find_by(id: params[:shipment_id])
        @line_item = Spree::LineItem.find_by(id: params[:line_item_id])
        @order = Spree::Order.find_by(id: params[:order_id])
        @selected_qty = @shipment.stock_item_units.select do |siu|
          siu.line_item_id == @line_item.id && siu.state == "locked"
        end.sum(&:pack_size)
        @product = current_store.products.friendly.find(params[:product_id])
        @variant = @line_item.variant
        @stock_locations = @variant.stock_items.with_active_stock_location.in_stock.map(&:stock_location)
        @search = @product.stock_item_units.includes(:stock_item).ransack(params[:q])

        available = @search.result.stock

        params[:quantity] = update_pack_size(@order, @line_item, params[:quantity]) unless @line_item.storefront_sale_channel?
        locked = @search.result.where(spree_stock_item_units: {
          state: :locked,
          shipment_id: params[:shipment_id],
        }) if params[:shipment_id].present? && params[:line_item_id].present?

        scope = locked ? available.or(locked) : available
        @stock_item_units = scope.group_by(&:stock_item)
      end

      def batch_shipped
        @shipment = ::Spree::Shipment.find(params[:shipment_id])
        @shipped_stock_items = []

        @shipment.order_package.shipments.each do |shipment|
          stock_item_units = shipment.stock_item_units.where(state: "shipped").order(created_at: :asc)

          shipped_stock_items_data = stock_item_units.group_by { |siu| siu.stock_item.variant_id }.transform_values do |sius|
            sius.map do |siu|
              {
                id: siu.id,
                number: siu.number,
                printed: siu.printed,
                vendor_receipt_date: siu.vendor_receipt_date&.strftime("%m/%d/%Y"),
                vendor_receipt_number: siu.vendor_receipt_number,
                vendor_inventory_number: siu.vendor_inventory_number,
                vendor_inventory_cost: siu.vendor_inventory_cost,
                expiry_date: siu.expiry_date&.strftime("%m/%d/%Y"),
                remark: siu.remark,
                remarks: siu.remarks,
                state: siu.state,
                shipment_id: siu.shipment_id,
                line_item_id: siu.line_item_id,
                variant_name: siu.stock_item.variant.name,
                variant_sku: siu.stock_item.variant.sku,
              }
            end
          end

          @shipped_stock_items << shipped_stock_items_data
        end
        @permission = spree_current_user&.spree_roles&.first&.permission&.view_product_cost_price
        @shipped_stock_items
      end

      def print
        @stock_item_units = Spree::StockItemUnit.includes(stock_item: :variant).where(id: params[:stock_item_unit_id]).all
        @stock_item_units.each { |it| it.update!(printed: true) }
        render(layout: "qr_print")
      end

      def check
      end

      def number_info
        if params[:number].empty?
          handle_resource_not_found
        else
          redirect_to(spree.admin_stock_item_unit_path(params[:number]))
        end
      end

      def pricecompare_info
        command = ::Admin::GetPricecompareInfoCommand.call(vin: params[:vin])
        render(json: command.result)
      end

      def divide_units
        load_stock_item_unit_data

        stock_item_unit = Spree::StockItemUnit.find(@stock_item_unit_id)
        pack_size = stock_item_unit.pack_size

        @units = []

        (1...pack_size).each do |index|
          new_item = stock_item_unit.dup
          new_item.number = nil
          new_item.printed = false

          new_item = duplicate_stock_item_unit(new_item, index)
          @units << new_item if new_item&.valid?
        end

        duplicate_stock_item_unit(stock_item_unit, pack_size)
        @units = stock_item_unit.stock_item.stock_item_units.on_hand.order(updated_at: :desc)
      end

      def divide_pack_size
        @product = Spree::Product.find_by(id: params[:product_id])
        @stock_item_unit = Spree::StockItemUnit.find_by(id: params[:id])
        @line_item_quantity = params[:line_item_quantity]
        @request_from = @line_item_quantity.present? ? "orders" : "products"
        @vic_value = (@stock_item_unit.vendor_inventory_cost.to_f / @stock_item_unit.pack_size).round(2)
      end

      private

      def notify_customers
        variant = case params.dig("action")
        when "batch_create"
          ::Spree::Variant.find_by(id: @stock_item_units.keys.first.variant_id)
        when "batch_update"
          return if params.dig("state") == "removed"

          stock_item_unit_id = params.dig(:stock_item_unit_id)&.first
          ::Spree::StockItemUnit.find_by(id: stock_item_unit_id)&.stock_item&.variant
        end

        return unless variant

        action = "outOfStock"
        customers_to_notify = ::Spree::ProductNotification.where(variant_id: variant.id, notify: true, action: action)

        return if customers_to_notify.blank?

        customers_email = customers_to_notify.pluck(:email)
        CustomerNotifyJob.perform_later(::Spree::Store.default, customers_email, variant, action)

        customers_to_notify.update_all(notify: false) # rubocop:disable Rails/SkipsModelValidations
      end

      def update_pack_size(order, line_item, quantity)
        quantity = quantity.to_i
        pack_size = line_item&.pack_size
        quantity *= pack_size if pack_size.present? && pack_size.positive?
        quantity
      end

      def check_permission
        @permission = spree_current_user&.spree_roles&.first&.permission&.view_product_cost_price
      end

      def load_stock_item_unit_data
        @line_item_quantity = params[:line_item_quantity]
        @product_id = params[:product_id]
        @stock_item_unit_id = params[:stock_item_unit_id]
      end

      def find_resource
        model_class.find_by(number: params[:id]) || model_class.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        nil
      end

      def handle_resource_not_found
        flash[:notice] = "Item not found. Please check the item number / VIN number and try again."
        redirect_to(spree.check_admin_stock_item_units_path)
      end

      def batch_update_params
        params.permit(:state, :line_item_id, :shipment_id)
      end

      def duplicate_stock_item_unit(new_item, index)
        new_item.vendor_inventory_cost = params[:vic].to_f.round(2)
        new_item.pack_size_counter = "#{index}/#{new_item.pack_size}"
        new_item.pack_size = 1

        new_item.save
        record_activity_log(new_item)

        new_item
      end

      def stock_item_unit_params
        params.require(:stock_item_unit).permit(
          :vendor_receipt_number,
          :vendor_inventory_number,
          :vendor_inventory_cost,
          :vendor_receipt_date,
          :expiry_date,
          :remark,
          :pack_size,
          :expiry_type,
        )
      end

      def record_activity_log(stock_item_unit)
        product = stock_item_unit&.stock_item&.product

        ::Spree::ActivityLog.create!(
          loggable: stock_item_unit,
          user_id: spree_current_user.id,
          action: ::Spree::ActivityLogActions.get(:stock_item_unit, :edit),
          action_name: stock_item_unit.number,
          role: spree_current_user.spree_roles.first&.name,
          date: Time.current,
          email: spree_current_user.email,
          action_place: product ? stock_admin_product_path(product) : 'N/A',
          product_id: product&.id
        )
      end
    end
  end
end
