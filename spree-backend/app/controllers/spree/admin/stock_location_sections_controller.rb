# frozen_string_literal: true

module Spree
  module Admin
    class StockLocationSectionsController < ::Spree::Admin::BaseController
      before_action :set_stock_location
      before_action :set_stock_location_section, only: [:edit, :update, :destroy]

      def index
      end

      def new
        @stock_location_section = @stock_location.sections.new
      end

      def edit
      end

      def create
        @stock_location_section = @stock_location.sections.new(stock_location_section_params)

        if @stock_location_section.save
          flash[:notice] = t(".success")
          redirect_to(spree.admin_stock_locations_path)
        else
          flash[:error] = t(".failure")
          render(:new)
        end
      end

      def update
        if @stock_location_section.update(stock_location_section_params)
          flash[:notice] = t(".success")
          redirect_back(fallback_location: spree.admin_stock_locations_path)
        else
          flash[:error] = t(".failure")
          render(:edit)
        end
      end

      def destroy
        if @stock_location_section.destroy
          flash[:notice] = t(".success")
        else
          flash[:error] = t(".failure")
        end

        redirect_back(fallback_location: spree.admin_stock_locations_path)
      rescue ActiveRecord::InvalidForeignKey
        handle_exception_invalidforeignkey
      end

      private

      def handle_exception_invalidforeignkey
        flash[:error] = t(".location_section_still_using")
        redirect_back(fallback_location: spree.admin_stock_locations_path)
      end

      def set_stock_location
        @stock_location = Spree::StockLocation.find(params[:stock_location_id])
      end

      def stock_location_section_params
        params.require(:stock_location_section).permit(:name, :description)
      end

      def set_stock_location_section
        @stock_location_section = @stock_location.sections.find(params[:id])
      end
    end
  end
end
