# frozen_string_literal: true

module Spree
  module Admin
    class SubscriberBehaviorsController < Spree::Admin::BaseController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = Spree::SubscriberBehavior.where(subscriber_id: params[:subscriber_id]).ransack(params[:q]).result.order(created_at: :desc, id: :desc).page(params[:page]).per(per_page)
        @subscriber_behaviors = collection
      end
    end
  end
end
