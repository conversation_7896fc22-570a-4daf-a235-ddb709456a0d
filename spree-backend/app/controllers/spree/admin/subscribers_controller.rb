# frozen_string_literal: true

module Spree
  module Admin
    class SubscribersController < Spree::Admin::BaseController
      def index
        session[:subscriber_query] = {}
        params[:q] ||= {}
        params[:q][:level_in] ||= Spree::Subscriber.levels.keys - ["subscriber","customer","buyer","VIP"]

        permitted_params = params.require(:q).permit(
          :email_cont,
          level_in: [],
        )

        session[:subscriber_query] = session[:subscriber_query].merge(permitted_params)

        per_page = params[:per_page].presence || Spree::Backend::Config[:admin_products_per_page]

        @search = Spree::Subscriber.all.ransack(session[:subscriber_query])
        @subscribers = @search.result.order(created_at: :desc, id: :desc).page(params[:page]).per(per_page)
      end
    end
  end
end
