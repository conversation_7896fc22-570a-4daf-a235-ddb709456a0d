# frozen_string_literal: true

module Spree
  module Admin
    module Shortener
      class ShortenedUrlsController < ActionController::Metal
        include ActionController::StrongParameters
        include ActionController::Redirecting
        include ActionController::Instrumentation
        include Rails.application.routes.url_helpers
        include Shortener

        def show
          token = ::Shortener::ShortenedUrl.extract_token(params[:id])
          track = ::Shortener.ignore_robots.blank? || request.human?
          url   = ::Shortener::ShortenedUrl.fetch_with_token(token: token, additional_params: params, track: track)
          redirect_to(url[:url], status: :moved_permanently)
        end
      end
    end
  end
end
