# frozen_string_literal: true

module Spree
  module Admin
    class MetabaseReportSettingsController < ResourceController
      def new
        @organisations = Spree::Organisation.where(name: Apartment::Tenant.current)
      end

      def edit
        @organisations = Spree::Organisation.where(name: Apartment::Tenant.current)
      end

      def create
        @metabase_report_setting = Spree::MetabaseReportSetting.new(metabase_report_setting_params)
        if @metabase_report_setting.save!
          redirect_to(
            admin_metabase_report_settings_path,
            flash: { success: "Metabase report setting created successfully" },
          )
        else
          redirect_back(
            fallback_location: edit_admin_metabase_report_setting_path,
            flash: { error: @metabase_report_setting.errors.full_messages.join(", ") },
          )
        end
      end

      def update
        if @metabase_report_setting.update(metabase_report_setting_params)
          redirect_to(
            admin_metabase_report_settings_path,
            flash: { success: "Metabase report setting updated successfully" },
          )
        else
          redirect_back(
            fallback_location: edit_admin_metabase_report_setting_path,
            flash: { error: @metabase_report_setting.errors.full_messages.join(", ") },
          )
        end
      end

      def destroy
        if @metabase_report_setting.destroy!
          flash[:success] = flash_message_for(@metabase_report_setting, :successfully_removed)
        else
          flash[:error] = @metabase_report_setting.errors.full_messages.join(", ")
        end
        respond_with(@metabase_report_setting) do |format|
          format.html { redirect_to(location_after_destroy) }
          format.js   { render_js_for_destroy }
        end
      end

      private

      def collection
        current_tenant = ::Spree::Organisation.where(name: Apartment::Tenant.current)
        @collection ||= Spree::MetabaseReportSetting.where(organisation: current_tenant)
      end

      def metabase_report_setting_params
        params.require(:metabase_report_setting).permit(
          :organisation_id,
          :store_id,
          :metabase_sharing_type,
          :metabase_sharing_data,
          :root_menu_name,
          :report_menu_name,
        )
      end
    end
  end
end
