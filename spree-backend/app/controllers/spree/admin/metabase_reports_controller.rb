# frozen_string_literal: true

module Spree
  module Admin
    class MetabaseReportsController < Spree::Admin::BaseController
      def metabase_report
        @permissions = current_user_permissions.find_permission
        if @permissions&.sale_and_finance_report
        else
          message = "You are not authorized to do this action"
          flash[:error] = message
          redirect_to(admin_users_path)
          nil
        end
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end
    end
  end
end
