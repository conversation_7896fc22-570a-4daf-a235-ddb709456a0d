# frozen_string_literal: true

module Spree
  module Admin
    class OrganisationsController < ResourceController
      def create
        organisation = Spree::Organisation.new(organisation_params)
        if organisation.save
          redirect_to(
            admin_organisations_path,
            flash: { success: "Organisation #{organisation.name} created successfully" },
          )
        else
          redirect_back(
            fallback_location: new_admin_organisation_path,
            flash: { error: organisation.errors.full_messages.join(", ") },
          )
        end
      end

      def destroy
        if @organisation.destroy
          flash[:success] = flash_message_for(@organisation, :successfully_removed)
        else
          flash[:error] = @organisation.errors.full_messages.join(", ")
        end
        respond_with(@organisation) do |format|
          format.html { redirect_to(location_after_destroy) }
          format.js   { render_js_for_destroy }
        end
      end

      private

      def organisation_params
        params.require(:organisation).permit(:name, :admin_email, :custom_domain, :subdomain)
      end
    end
  end
end
