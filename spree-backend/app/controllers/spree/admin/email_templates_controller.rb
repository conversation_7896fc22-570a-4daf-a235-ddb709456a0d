# frozen_string_literal: true

require "English"
module Spree
  module Admin
    class EmailTemplatesController < ResourceController
      before_action :load_data, except: :index
      def index
        # @email_templates = Spree::EmailTemplate.current_store(current_store.id)
      end

      def create
        @email_template.sale_channel_ids = params[:sale_channel_ids]
        super
      end

      def update
        @email_template.sale_channel_ids = params[:sale_channel_ids]
        super

        # Spree::SaleChannel.where(id: params[:sale_channel_ids]).update_all(email_template_id: @email_template.id)
      end

      def test_mail
        if current_store.email_setting&.intercept_email.present?
          if @email_template.mailer_class == "Spree::CustomerMailer"
            if Spree::CustomerMailer.new.send_test_customer_mail(@email_template.subject, @email_template.body)
              flash[:success] = ::Spree.t(:email_sent_successfully, scope: :template)
            else
              flash[:error] = ::Spree.t(:unable_to_send, scope: :template)
            end
          elsif @email_template.send_test_email
            flash[:success] = ::Spree.t(:email_sent_successfully, scope: :template)
          else
            flash[:error] = ::Spree.t(:unable_to_send, scope: :template)
          end
        else
          flash[:error] = ::Spree.t(:intercept_email_not_present, scope: :template)
        end

        redirect_to(edit_admin_email_template_path(@email_template))
      end

      def test_new_mail
        success = ""
        error = ""
        subject = params[:subject]
        body = params[:body]
        if subject.empty? || body.empty?
          error = "Template subject or body is empty."
        elsif current_store.email_setting&.intercept_email.present?
          if Spree::CustomerMailer.new.send_test_customer_mail(subject, body)
            success = ::Spree.t(:email_sent_successfully, scope: :template)
          else
            error = ::Spree.t(:unable_to_send, scope: :template)
          end
        else
          error = ::Spree.t(:intercept_email_not_present, scope: :template)
        end

        render(json: { success: success, error: error }, status: :ok, content_type: "json")
      end

      def search
        params[:q] ||= {}
        list = scope.where(
          mailer_class: "Spree::CustomerMailer",
          active: true,
        ).ransack(params[:q]).result.distinct.limit(10)
        render(
          json: list.map do |t|
            { id: t.id, name: t.template_name, subject: t.subject, body: t.body }
          end,
          status: :ok,
        )
      end

      def customer_email_body
        order = ::Spree::Order.find(params[:order_id])
        email_body = Spree::CustomerMailer.new.build_email_detail(order, @email_template.body)
        render(json: { text: email_body }, status: :ok)
      rescue StandardError
        Rails.logger.error("mail body aborted: #{$ERROR_INFO.class}: #{$ERROR_INFO}\n  #{$ERROR_INFO.backtrace.join("\n  ")}")
      end

      def compose
        @permissions = current_user_permissions.find_permission
        if @permissions&.send_email
        else
          message = "You are not authorized to do this action"
          flash[:error] = message
          redirect_to(admin_users_path)
          return
        end
        @email = {
          to: "",
        }
        if params[:order_id]
          @order = ::Spree::Order.find(params[:order_id])
          @email = {
            to: @order.email,
          }
        elsif params[:user_id]
          user_ids = params[:user_id].split(",")
          users = ::Spree::User.where(id: user_ids).pluck("email")
          @email[:to] = users.join(", ")
        end
        # @templates = scope.where(mailer_class: 'Spree::CustomerMailer', active: true).limit(10).
        #  map { |t| {id: t.id, name: t.template_name, subject: t.subject, body: t.body }}
        # respond_with(@order) do |format|
        #  format.html do
        #    render 'compose'
        #  end
        #  format.js { render layout: false }
        # end
      end

      def send_mail
        if params[:body]&.empty? || params[:to]&.empty? || params[:subject]&.empty?
          flash[:error] = "Email subject or body or to is empty"
          @email = {
            template_id: params[:template_id],
            to: params[:to],
            cc: params[:cc],
            subject: params[:subject],
            body: params[:body],
          }
          render(:compose, status: :unprocessable_entity)
        else
          ::Spree::CustomerMailer.new.send_customer_mail(
            current_store,
            params[:body],
            params[:to],
            params[:subject],
            params[:cc],
          )
          flash[:success] = "Email sent successfully"
          if params[:order_id]
            redirect_to(edit_admin_order_path(::Spree::Order.find(params[:order_id])))
          else
            redirect_to(admin_users_path)
          end
        end
      end

      protected

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end

      def scope
        current_store.email_templates
      end

      def find_resource
        scope.find(params[:id])
      end

      def collection
        return @collection if @collection.present?

        params[:q] ||= {}
        @search = scope.ransack(params[:q])
        @collection = @search.result.page(params[:page]).per(params[:per_page])
      end

      def load_data
        @customer_mailer_variables = [
          "username",
          "user_email",
          "order_number",
          "order_total",
          "tax",
          "subtotal",
          "store_name",
          "line_item_details",
          "line_items_table",
          "tracking",
        ]
        @tags = current_store.tags
      end
    end
  end
end
