# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Platform
        class StockItemUnitsController < Spree::Api::V2::Platform::ResourceController
          def numbers
            item_numbers = []
            params[:quantity].to_i.times do
              item_numbers.push(Spree::StockItemUnit.number_generator.generate_permalink(Spree::StockItemUnit))
            end
            render(json: item_numbers)
          end

          def printed_numbers
            Spree::StockItemUnit.where(number: params[:numbers]).find_each { |it| it.update!(printed: true) }
            render(json: { success: true })
          end

          def number_tracking_setting
            current_store.update(number_tracking: params[:checked])
            render(json: { success: true })
          end

          private

          def authorize_spree_user
            return if spree_current_user.nil?

            case action_name
            when "create"
              spree_authorize!(:create, model_class)
            when "destroy"
              spree_authorize!(:destroy, resource)
            when "index"
              spree_authorize!(:read, model_class)
            when "show"
              spree_authorize!(:read, resource)
            end
          end

          def model_class
            Spree::StockItemUnit
          end

          def resource
            @resource ||= if defined?(resource_finder)
              resource_finder.new(scope: scope, params: finder_params).execute
            else
              scope.find_by(number: params[:id]) || scope.find(params[:id])
            end
          end
        end
      end
    end
  end
end
