# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class BlogPostsController < ::Spree::Api::V2::BaseController

          def index
            render_serialized_payload { serialize_blog_posts(paginated_collection) }
          end

          def show
            resource.increment!(:views_count)
            render_serialized_payload { serialize_blog_post(resource) }
          end

          def like
            return render json: { errors: "You are not authorized to perform this action." }, status: :unprocessable_entity if spree_current_user.nil?

            return render_serialized_payload { serialize_blog_post(resource) } if resource.likes.exists?(user_id: spree_current_user.id)

            like = resource.likes.new(user: spree_current_user)
            if like.save
              resource.increment!(:likes_count)
              render_serialized_payload { serialize_blog_post(resource) }
            else
              render json: { errors: like.errors.full_messages }, status: :unprocessable_entity
            end
          end

          def unlike
            return render json: { errors: "You are not authorized to perform this action." }, status: :unprocessable_entity if spree_current_user.nil?

            like = resource.likes.find_by(user: spree_current_user)
            if like
              like.destroy
              resource.decrement!(:likes_count)
              render_serialized_payload { serialize_blog_post(resource) }
            end
          end

          protected

          def resource
            @resource ||= scope.find_by(slug: params[:id])
          end

          def serialize_blog_post(resource)
            collection_serializer.new(resource, { params: { current_user: spree_current_user } }).serializable_hash
          end

          def serialize_blog_posts(paginated_collection)
            collection_serializer.new(
              paginated_collection, 
              {
                meta: collection_meta(paginated_collection),
                params: { current_user: spree_current_user }
              }
            ).serializable_hash
          end

          def paginated_collection
            @paginated_collection ||= collection_paginator.new(collection, params).call
          end

          def collection_serializer
            Spree::V2::Storefront::BlogPostSerializer
          end

          def collection
            @collection ||= scope.ransack(params[:filter]).result
          end

          def scope
            @scope ||= Spree::BlogPost.for_store(current_store).friendly.visible.published
          end

          def collection_meta(collection)
            {
              count: collection.size,
              total_count: collection.total_count,
              total_pages: collection.total_pages
            }
          end
        end
      end
    end
  end
end
