# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class BlogCategoriesController < ::Spree::Api::V2::BaseController

          def index
            render_serialized_payload { serialize_blog_categories(paginated_collection) }
          end

          protected

          def paginated_collection
            @paginated_collection ||= collection_paginator.new(collection, params).call
          end

          def collection
            @collection ||= scope.ransack(params[:filter]).result&.distinct
          end

          def scope
            @scope ||= Spree::BlogCategory.for_store(current_store).friendly.visible
          end

          def collection_serializer
            Spree::V2::Storefront::BlogCategorySerializer
          end

          def serialize_blog_categories(paginated_collection)
            collection_serializer.new(paginated_collection, { meta: collection_meta(paginated_collection) }).serializable_hash
          end

          def collection_meta(collection)
            {
              count: collection.size,
              total_count: collection.total_count,
              total_pages: collection.total_pages
            }
          end
        end
      end
    end
  end
end
