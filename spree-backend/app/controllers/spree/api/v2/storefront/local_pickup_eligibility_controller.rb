# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class LocalPickupEligibilityController < ::Spree::Api::V2::BaseController
          def cart_eligible_for_local_pickup
            order = Spree::Order.last
            order.line_items.any? do |line_item|
              line_item.listing&.shipping_category&.shipping_methods&.any?(&:local_pickup)
            end
          end
        end
      end
    end
  end
end
