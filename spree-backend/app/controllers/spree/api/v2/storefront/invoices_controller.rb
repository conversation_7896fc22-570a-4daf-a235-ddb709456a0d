# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class InvoicesController < ::Spree::Api::V2::BaseController
          # after_action :allow_iframe, only: :show
          respond_to :html
          def show
            @invoice = Spree::OrderInvoice.find_by(number: params[:id])
            respond_with(@invoice) do |format|
              format.pdf do
                send_data(
                  Spree::OrderInvoice.pdf(@invoice, current_store),
                  type: "application/pdf",
                  disposition: "inline",
                )
              end
            end
          end

          def allow_iframe
            response.headers.except!("X-Frame-Options")
          end
        end
      end
    end
  end
end
