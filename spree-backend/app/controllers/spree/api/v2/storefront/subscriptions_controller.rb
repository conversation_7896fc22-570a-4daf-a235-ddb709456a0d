# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class SubscriptionsController < ::Spree::Api::V2::BaseController
          before_action :require_spree_current_user
          def index
            return render(json: []) if spree_current_user.blank?

            subscriptions = spree_current_user.subscriptions.order(created_at: :desc, id: :desc)
            render_serialized_payload { serialize_collection(subscriptions) }
          end

          def show
            subscription = Spree::Subscription.find_by(id: params[:id])
            if subscription.nil?
              render(json: { error: Spree.t("admin.subscription.subscription_not_found") }, status: :not_found) and return
            end

            render_serialized_payload { serialize_resource(subscription) }
          end

          def edit
            subscription = Spree::Subscription.find_by(id: params[:id])
            if subscription.nil?
              render(json: { error: Spree.t("admin.subscription.subscription_not_found") }, status: :not_found) and return
            end

            render_serialized_payload { serialize_resource(subscription) }
          end

          def update
            subscription = Spree::Subscription.find_by(id: params[:id])
            if subscription.nil?
              render(json: { error: Spree.t("admin.subscription.subscription_not_found") }, status: :not_found) and return
            end
            
            unless subscription.is_active
              render(json: { error: Spree.t("admin.subscription.inactive") }, status: :unprocessable_entity) and return
            end

            if params[:next_delivery_date].present?
              next_delivery_date = Date.strptime(params[:next_delivery_date], "%m/%d/%Y")
              updated_params = subscription_params.merge(next_delivery_date: )
            else
              updated_params = subscription_params
            end

            if subscription.update(updated_params)
              render_serialized_payload { serialize_resource(subscription) }
            else
              render_serialized_payload(422) { { errors: subscription.errors.full_messages.join(", ") } }
            end
          end

          def revise_status
            subscription = Spree::Subscription.find_by(id: params[:id])
            if subscription.nil?
              render(json: { error: Spree.t("admin.subscription.subscription_not_found") }, status: :not_found) and return
            end

            unless subscription.is_active
              render(json: { error: Spree.t("admin.subscription.inactive") }, status: :unprocessable_entity) and return
            end

            new_status = subscription.status == "subscribe" ? "unsubscribe" : "subscribe"
            if subscription.update(status: new_status)
              render(
                json: {
                  message: Spree.t(
                    "admin.subscription.subscription_operation_message",
                    message: new_status.downcase,
                  ),
                },
                status: :ok,
              )
            else
              render(json: { errors: subscription.errors.full_messages.join(", ") }, status: :unprocessable_entity)
            end
          end

          def fetch_subscription_orders
            subscription = Spree::Subscription.find_by(id: params[:id])
            return render(json: []) if subscription.blank?

            orders = subscription.orders
            render_serialized_payload { serialize_collection_order(orders) }
          end

          def fetch_delivery_date
            subscription = Spree::Subscription.find_by(id: params[:id])
            if subscription.nil?
              render(json: { error: Spree.t("admin.subscription.subscription_not_found") }, status: :not_found) and return
            end

            next_date = subscription.next_delivery_date.presence || subscription.created_at
            interval = subscription.interval.presence || '1_week'
            date = fetch_next_order_date(next_date, interval)&.to_date

            render(json: { skip_delivery_date: next_date&.to_date, next_delivery_date: date }, status: :ok)
          end

          def fetch_delivery_intervals
            subscription = Spree::Subscription.find_by(id: params[:id])
            intervals = Spree::Subscription::DELIVERY_INTERVALS

            response_data = intervals.map do |key, value|
              { label: key, value: value, selected: value == subscription.interval }
            end

            render(json: { intervals: response_data }, status: :ok)
          end

          private

          def fetch_next_order_date(current_date, interval)
            numeric_part = interval.split("_")[0].to_i
            interval = interval.split("_")[1]
            date = current_date

            # Determine the appropriate interval method
            case interval
            when "day", "days"
              date + numeric_part.days
            when "week", "weeks"
              date + numeric_part.weeks
            when "month", "months"
              date + numeric_part.months
            when "year", "years"
              date + numeric_part.years
            end
          end

          def serialize_resource(resource)
            Spree::Api::V2::Storefront::SubscriptionSerializer.new(resource).serializable_hash
          end

          def serialize_collection(collection)
            Spree::Api::V2::Storefront::SubscriptionSerializer.new(collection).serializable_hash
          end

          def serialize_collection_order(collection)
            Spree::V2::Storefront::OrderSerializer.new(collection).serializable_hash
          end

          def subscription_params
            params.require(:subscription).permit(
              :user_id,
              :line_item_id,
              :interval,
              :next_delivery_date,
              :status,
              :quantity,
              :initial_price,
              :recurring_price,
            )
          end
        end
      end
    end
  end
end
