# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class StripeController < ::Spree::Api::V2::BaseController
          include Spree::Api::V2::Storefront::OrderConcern

          def confirm
            require "stripe"
            unless spree_current_order
              return render(json: { message: "order already confirmed" }, status: :ok)
            end

            spree_authorize!(:update, spree_current_order, order_token)
            payment = Spree::Payment.find_by(response_code: params[:paymentIntent])
            unless payment
              return render_error_payload("stripe payment not found.")
            end
            unless payment.order
              render(json: { message: "order already confirmed" }, status: :ok)
            end

            payment_method = payment.payment_method
            Stripe.api_key = payment_method.get_preference(:secret_key)
            Stripe.api_version = '2022-11-15'

            payment_intent = Stripe::PaymentIntent.retrieve(params[:paymentIntent])
            if payment_intent[:status] != "succeeded" && payment_intent[:status] != "processing"
              return render_error_payload(payment_intent[:status])
            end

            # Create payment source in AM using details from payment_intent response
            create_payment_source(payment_intent)

            result = complete_service.call(order: payment.order)
            render_result(result)
          rescue => e
            Rails.logger.error((["#{self.class} - #{e.class}: #{e.message}"] + e.backtrace).join("\n"))
            throw(e)
          end

          def webhook
            require "stripe"

            payload = request.body.read
            event = nil

            begin
              event = Stripe::Event.construct_from(
                JSON.parse(payload, symbolize_names: true),
              )
            rescue JSON::ParserError => e
              Rails.logger.error("Webhook error while parsing basic request. #{e.message})")
              return render_error_payload("Invalid payload", 400)
            end
            # Check if webhook signing is configured.
            # endpoint_secret = ::Spree::Gateway::StripeElementsGateway&.active.first.get_preference(:endpoint_secret)
            # if endpoint_secret
            #  # Retrieve the event by verifying the signature using the raw body and secret.
            #  signature = request.env['HTTP_STRIPE_SIGNATURE'];
            #  begin
            #    event = Stripe::Webhook.construct_event(
            #      payload, signature, endpoint_secret
            #    )
            #  rescue Stripe::SignatureVerificationError
            #    puts "Webhook signature verification failed. #{err.message})"
            #    status 400
            #  end
            # end

            # Handle the event
            case event.type
            when "payment_intent.succeeded"
              payment_intent = event.data.object # contains a Stripe::PaymentIntent
              Rails.logger.info("Payment for #{payment_intent[:id]} with amount #{payment_intent["amount"]} succeeded.")

              payment = Spree::Payment.find_by(response_code: payment_intent["id"])

              if payment
                payment_method = payment.payment_method
                Stripe.api_key = payment_method.get_preference(:secret_key)
                Stripe.api_version = '2022-11-15'
                # Find payment details (from stripe payment element) and payment in spree
                stripe_payment_method = Stripe::PaymentMethod.retrieve(payment_intent[:payment_method])
                # Create source using payment details from stripe payment element
                if payment.source.blank? && payment_method.try(:payment_source_class)
                  card = stripe_payment_method[:card] || stripe_payment_method[:card_present]
                  payment.source = payment_method.payment_source_class.create!(
                    {
                      gateway_payment_profile_id: stripe_payment_method.id,
                      cc_type: card && card[:brand],
                      month: card && card[:exp_month],
                      year: card && card[:exp_year],
                      last_digits: card && card[:last4],
                      payment_method: payment_method,
                    }
                  )
                end

                # Update payment to pending if authorised only, and completed if auto capture enabled
                if payment_intent["capture_method"] == "manual"
                  payment.update!(state: "pending")
                else
                  payment.complete!
                end

                if payment.order
                  # Update order status to complete if order not completed
                  result = complete_service.call(order: payment.order)
                  Rails.logger.info(result)
                end
              else
                Rails.logger.info("Payment for intent #{payment_intent["id"]} does not exist.")
              end
            else
              Rails.logger.info("Unhandled event type: #{event.type}")
            end
            render(json: { message: "stripe webhook handled successfully" }, status: :ok)
          end

          def resource_serializer
            Spree::Api::Dependencies.storefront_cart_serializer.constantize
          end

          def complete_service
            Spree::Api::Dependencies.storefront_checkout_complete_service.constantize
          end

          def create_payment_source(payment_intent)
            payment = Spree::Payment.find_by(response_code: params[:paymentIntent])

            if payment
              payment_method = payment.payment_method
              if payment.source.blank? && payment_method.try(:payment_source_class)
                stripe_payment_method = Stripe::PaymentMethod.retrieve(payment_intent[:payment_method])
                card = stripe_payment_method[:card]
                cardholder_name = stripe_payment_method[:billing_details][:name]

                payment.source = payment_method.payment_source_class.create!(
                  {
                    gateway_payment_profile_id: payment_intent[:payment_method],
                    gateway_customer_profile_id: payment_intent[:customer],
                    cc_type: card[:brand],
                    month: card[:exp_month],
                    year: card[:exp_year],
                    last_digits: card[:last4],
                    name: cardholder_name,
                    payment_method_id: payment_method.id,
                  })
              end

              new_state = payment_intent["capture_method"] == "manual" ? "pending" : "completed"
              payment.update!(state: new_state)
            else
              Rails.logger.info("Payment for intent #{payment_intent["id"]} does not exist.")
            end
          end

        end

      end
    end
  end
end

# https://github.com/spree/spree_gateway/pull/409/files
# https://github.com/spree/spree_gateway/issues/330  https://github.com/spree/spree_gateway/commit/5402979ec13b9428ffdcb32df4e98cfcd223305f
