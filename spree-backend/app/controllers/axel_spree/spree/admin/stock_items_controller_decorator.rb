# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module StockItemsControllerDecorator
        class << self
          def prepended(base)
            base.before_action(:load_product, only: [:batch_new, :batch_create])
            base.after_action(:set_location_section_id_null, only: :destroy)
            base.before_action(:check_permission, only: [:batch_create, :batch_new, :units])
          end
        end

        def batch_new
        end

        def batch_create
          @command = ::Admin::BatchCreateStockItemCommand.call(
            product: @product,
            stock_item_params: params[:stock_items],
            current_user: spree_current_user,
            action_place: @product ? stock_admin_product_path(@product) : 'N/A',
          )
          @stock_items = @command.result

          unless @command.success?
            flash[:error] = @command.errors.full_messages
            render(:batch_new)
          end
        end

        def units
          @stock_item = ::Spree::StockItem.find(params[:id])
          @stock_item_units = @stock_item.stock_item_units.includes(:remarks).ransack(params[:q]).result.order(updated_at: :desc)
        end

        def update
          if params[:stock_item].present? && !params[:stock_item][:inventory_threshold].nil?
            inventory_threshold = params[:stock_item][:inventory_threshold]
            stock_item.inventory_threshold = (inventory_threshold.presence)
          end

          changes_before_save = stock_item.changed?

          stock_item.save
          record_activity_log(stock_item) if changes_before_save

          respond_to do |format|
            format.html { redirect_back(fallback_location: spree.stock_admin_product_url(stock_item.product)) }
            format.js { head(:ok) }
          end
        end

        def destroy
          command = ::Admin::StockRemovalCheckCommand.call(
            product: stock_item.product,
            stock_item_units: stock_item.stock_item_units.stock,
          )

          unless command.success?
            flash[:error] = command.errors.full_messages.join(", ")
            redirect_to(spree.stock_admin_product_url(stock_item.product))
            return
          end

          stock_item_units = stock_item.stock_item_units.to_a

          if stock_item.destroy
            stock_item_units.each do |unit|
              record_activity_log(unit)
            end
            record_activity_log(stock_item)
          else
            flash[:error] = stock_item.errors.full_messages.to_sentence
          end

          respond_with(@stock_item) do |format|
            format.html { redirect_back(fallback_location: spree.stock_admin_product_url(stock_item.product)) }
            format.js
          end
        end

        def load_product
          @product = current_store.products.friendly.find(params[:product_id])
        end

        private

        def check_permission
          @permission = spree_current_user&.spree_roles&.first&.permission&.view_product_cost_price
          @col_class = @permission ? "col-3" : "col-4"
        end

        def set_location_section_id_null
          if flash[:error].blank?
            stock_item = ::Spree::StockItem.deleted.find(params[:id])
            stock_item.stock_location_section_id = nil
            stock_item.save
          end
        end

        def record_activity_log(resource)
          user = spree_current_user
          is_unit = resource.is_a?(::Spree::StockItemUnit)
          product = is_unit ? resource.stock_item&.product : resource.product

          object_type = is_unit ? :stock_item_unit : :stock_item

          action_name_value = is_unit ? resource.number : product&.name

          ::Spree::ActivityLog.create!(
            loggable: resource,
            user_id: user.id,
            action: ::Spree::ActivityLogActions.get(object_type, resolve_stock_item_action),
            action_name: action_name_value || "Unknown Event",
            role: user.spree_roles.first&.name,
            date: Time.current,
            email: user.email,
            action_place: product ? stock_admin_product_path(product) : 'N/A',
            product_id: product&.id
          )
        end

        def resolve_stock_item_action
          case action_name
          when "update"  then :edit
          when "destroy" then :remove
          else :default
          end
        end

        ::Spree::Admin::StockItemsController.prepend(self)
      end
    end
  end
end
