# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module AnalysisControllerDecorator
        include ::Spree::BaseHelper

        def self.prepended(base)
          base.before_action(:set_current_store, only: [:show, :download, :print_expiration])
          base.before_action(:ensure_report_exists, :set_default_pagination, only: [:show, :download, :print_expiration])
          base.before_action(:set_reporting_period, only: [:index, :show, :download, :print_expiration])
        end

        def orders_report
          filter_params(params)
        end

        def download_report
          filter_params(params)
          col_sep = params[:format] == "xls" ? "\t" : ","

          if params[:format] == "xls"
            package = SalesReport::Order.new(@orders, col_sep).generate_xlsx
          else
            csv_string = SalesReport::Order.new(@orders, col_sep).perform_report
            @csv_data = CSV.parse(csv_string, col_sep: col_sep, headers: true)
          end
          respond_to do |format|
            format.csv do
              send_data(csv_string, type: :csv, filename: "sales_report.csv", disposition: "attachment")
            end
            format.xls do
              send_data(
                package.to_stream.read,
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                disposition: "attachment",
                filename: "sales_report.xlsx",
              )
            end
            format.text do
              send_data(csv_string, type: :txt, filename: "sales_report.txt", disposition: "attachment")
            end
            format.pdf do
              render(
                pdf: "sales_report",
                disposition: "attachment",
                layout: "spree/layouts/pdf",
                orientation: "Landscape",
              )
            end
          end
        end

        def print
          filter_params(params)
          @start_date = params[:q][:created_at_gt].to_date
          @end_date = params[:q][:created_at_lt].to_date
          col_sep = params[:format] == "xls" ? "\t" : ","
          csv_string = SalesReport::Order.new(@orders, col_sep).perform_report
          @csv_data = CSV.parse(csv_string, col_sep: col_sep, headers: true)
          render(layout: "orders_report_print")
        end

        def print_expiration
          @start_date = params.dig(:search, :start_date)
          @end_date = params.dig(:search, :end_date)
          report = ::Spree::ReportGenerationService.generate_report(params.dig(:id), params.merge(@pagination_hash))
          report.controller = self
          col_sep = params[:format] == "xls" ? "\t" : ","
          csv_string = ::Spree::ReportGenerationService.download(report)
          @csv_data = CSV.parse(csv_string, col_sep: col_sep, headers: true)
          render(layout: "expiration_report_print")
        end

        private

        def filter_params(params)
          params[:q] ||= {}

          if params[:q].blank?
            params[:q][:created_at_gt] = Time.zone.yesterday.beginning_of_day.to_s
            params[:q][:created_at_lt] = Time.zone.today.end_of_day.to_s
          end
          params[:q][:completed_at_not_null] ||= "1" if ::Spree::Backend::Config[:show_only_complete_orders_by_default]
          @show_only_completed = params[:q][:completed_at_not_null] == "1"
          params[:q][:s] ||= @show_only_completed ? "completed_at desc" : "created_at desc"
          params[:q][:completed_at_not_null] = "" unless @show_only_completed
          params[:q].delete(:inventory_units_shipment_id_null) if params[:q][:inventory_units_shipment_id_null] == "0"

          if params[:q][:created_at_gt].present?
            params[:q][:created_at_gt] = begin
              Time.zone.parse(params[:q][:created_at_gt]).beginning_of_day
            rescue StandardError
              ""
            end
          end

          if params[:q][:created_at_lt].present?
            params[:q][:created_at_lt] = begin
              Time.zone.parse(params[:q][:created_at_lt]).end_of_day
            rescue StandardError
              ""
            end
          end
          @search = scope.preload(:user).accessible_by(current_ability, :index).ransack(params[:q])
          @orders = if params[:action] == "print" || params[:action] == "download_report"
            @search.result(distinct: true)
          else
            @search.result(distinct: true)
              .page(params[:page])
              .per(params[:per_page])
          end
        end

        def scope
          current_store.orders.accessible_by(current_ability, :index)
        end

        def set_current_store
          params[:store] = current_store
        end

        def show
          @report = ::Spree::ReportGenerationService.generate_report(@report_name, params.merge(@pagination_hash))
          @report.controller = self

          @report_data = shared_data.merge(@report.to_h)
          respond_to do |format|
            format.html { render(:index) }
            format.json { render(json: @report_data) }
          end
        end

        def download
          @report = ::Spree::ReportGenerationService.generate_report(@report_name, params.merge(@pagination_hash))
          @report.controller = self

          respond_to do |format|
            format.csv do
              send_data(::Spree::ReportGenerationService.download(@report), filename: "#{@report_name}.csv")
            end
            format.xls do
              if params.dig(:id) == "expiration"
                package = ::Spree::ReportGenerationService.download_xlsx(@report, { col_sep: "\t", report_name: @report_name.to_s })
                send_data(
                  package.to_stream.read,
                  type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                  disposition: "attachment",
                  filename: "#{@report_name}.xlsx",
                )
              else
                send_data(::Spree::ReportGenerationService.download(@report, { col_sep: "\t" }), filename: "#{@report_name}.xls")
              end
            end
            format.text do
              send_data(::Spree::ReportGenerationService.download(@report), filename: "#{@report_name}.txt")
            end
            format.pdf do
              render(
                pdf: @report_name.to_s,
                disposition: "attachment",
                layout: "spree/layouts/pdf",
              )
            end
          end
        end

        def set_reporting_period
          if params[:search].present?
            params[:search][:start_date] =
              params[:search][:start_date] == "" ? nil : (params[:search][:start_date].presence || session[:search_start_date])

            params[:search][:end_date] =
              params[:search][:end_date] == "" ? nil : (params[:search][:end_date].presence || session[:search_end_date])

            params[:search][:type] =
              params[:search][:type] == "" ? nil : (params[:search][:type].presence || session[:search_type])
          else
            params[:search] = {}
            params[:search][:start_date] = session[:search_start_date]
            params[:search][:end_date] = session[:search_end_date]
            params[:search][:type] = session[:search_type]
          end
          session[:search_start_date] = params[:search][:start_date]
          session[:search_end_date] = params[:search][:end_date]
          session[:search_type] = params[:search][:type]
        end
      end
    end
  end
end

Spree::Admin::AnalysisController.prepend(AxelSpree::Spree::Admin::AnalysisControllerDecorator)
