# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module PricesControllerDecorator
        def create
          params[:vp].each do |variant_id, prices|
            next unless variant_id

            variant = parent.variants_including_master.find(variant_id)
            next unless variant

            supported_currencies_for_all_stores.each do |currency|
              price = variant.price_in(currency.iso_code)

              price.price = prices[currency.iso_code]["price"].presence || nil
              price.compare_at_price = prices[currency.iso_code]["compare_at_price"].presence || nil
              price.compare_to_price = prices[currency.iso_code]["compare_to_price"].presence || nil

              is_new = price.new_record?

              if (is_new && price.price.present?) || (!is_new && price.changed?)
                price.save!

                action_type = is_new ? :add : :edit
                action = ::Spree::ActivityLogActions.get(:price, action_type)

                record_activity_log(price, action, variant)
              end
            end
          end

          flash[:success] = ::Spree.t("notice_messages.prices_saved")
          redirect_to(spree.admin_product_path(parent))
        end

        private

        def record_activity_log(price, action, variant)
          user = spree_current_user
          product = ::Spree::Product.find_by(slug: params[:product_id])
          return unless product && user

          ::Spree::ActivityLog.create!(
            loggable: price,
            user_id: user.id,
            action: action,
            role: user.spree_roles.first&.name,
            date: Time.current,
            email: user.email,
            action_place: edit_admin_product_path(product),
            action_name: variant.descriptive_name,
            product_id: product.id
          )
        end
      end
    end
  end
end

Spree::Admin::PricesController.prepend(AxelSpree::Spree::Admin::PricesControllerDecorator)
