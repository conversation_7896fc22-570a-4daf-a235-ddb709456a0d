# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module MenuItemsControllerDecorator
        def self.prepended(base)
          base.before_action(:update_resource_and_listing_id, only: :update)
        end

        def update_resource_and_listing_id
          return unless permitted_resource_params[:linked_resource_type] == "Spree::Product" && @object.linked_resource_type == "Spree::Product"
          listing_id = params[:menu_item][:linked_resource_id]
          listing = ::Spree::Listing.find(listing_id)
          product_id = listing.product_id
          params[:menu_item][:linked_resource_id] = product_id
          params[:menu_item][:listing_id] = listing_id
          params
        end
      end
    end
  end
end

Spree::Admin::MenuItemsController.prepend(AxelSpree::Spree::Admin::MenuItemsControllerDecorator)
