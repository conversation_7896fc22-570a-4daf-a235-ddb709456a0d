# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module CmsSectionsControllerDecorator
        def self.prepended(base)
          base.before_action(:update_params, only: [:update])
        end

        def update_params
          if permitted_resource_params[:type] == "Spree::Cms::Sections::SideBySideImages" || permitted_resource_params[:type] == "Spree::Cms::Sections::ImageGallery"
            if permitted_resource_params[:link_type_one] == @object.content[:link_type_one] && permitted_resource_params[:link_type_one] == "Spree::Product"
              permitted_resource_params[:link_one] = update_link_value(permitted_resource_params[:link_one])
            end

            if permitted_resource_params[:link_type_two] == @object.content[:link_type_two] && permitted_resource_params[:link_type_two] == "Spree::Product"
              permitted_resource_params[:link_two] = update_link_value(permitted_resource_params[:link_two])
            end

            if permitted_resource_params[:link_type_three] == @object.content[:link_type_three] && permitted_resource_params[:link_type_three] == "Spree::Product"
              permitted_resource_params[:link_three] = update_link_value(permitted_resource_params[:link_three])
            end
          elsif permitted_resource_params[:type] == "Spree::Cms::Sections::HeroImage" || permitted_resource_params[:type] == "Spree::Cms::Sections::FeaturedArticle"
            return unless permitted_resource_params[:linked_resource_type] == "Spree::Product" && @object.linked_resource_type == "Spree::Product"
            listing_id = permitted_resource_params[:linked_resource_id]
            listing = ::Spree::Listing.find(listing_id)
            product_id = listing.product_id
            permitted_resource_params[:linked_resource_id] = product_id
            permitted_resource_params[:listing_id] = listing_id
            params
          end
          permitted_resource_params
        end

        def update_link_value(link)
          link.present? && link.to_i > 0 ? ::Spree::Listing.find(link)&.title : link
        end
      end
    end
  end
end
Spree::Admin::CmsSectionsController.prepend(AxelSpree::Spree::Admin::CmsSectionsControllerDecorator)
