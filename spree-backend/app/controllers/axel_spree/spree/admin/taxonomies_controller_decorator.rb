# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module TaxonomiesControllerDecorator
        def flash_message_for(object, event_sym)
          resource_desc = "Categories"

          if (object.persisted? || object.destroyed?) &&
              object.respond_to?(:name) &&
              object.name.present? &&
              !object.is_a?(::Spree::Order)
            resource_desc += " \"#{object.name}\""
          end

          ::Spree.t(event_sym, resource: resource_desc)
        end

        ::Spree::Admin::TaxonomiesController.prepend(self)
      end
    end
  end
end
