# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Admin
      module UsersControllerDecorator
        def self.prepended(base)
          base.before_action(:set_permissions, only: [:index, :new, :destroy, :edit])
        end

        def index
          @users = @collection.where(axel_admin: false)
        end

        def create
          user_create_params = user_params
          # only need set current_store when creating user, keep store_ids when updating
          if user_create_params[:spree_store_ids].nil?
            user_create_params = user_create_params.merge(spree_store_ids: [])
          end
          if user_create_params[:spree_store_ids].exclude?(current_store.id)
            user_create_params[:spree_store_ids] << current_store.id
          end
          if user_create_params[:spree_store_ids].exclude?(::Spree::Store.default.id)
            user_create_params[:spree_store_ids] << ::Spree::Store.default.id
          end
          @user = ::Spree.user_class.new(user_create_params)

          if @user.save
            flash[:success] = flash_message_for(@user, :successfully_created)
            redirect_to(spree.edit_admin_user_path(@user))
          else
            render(:new, status: :unprocessable_entity)
          end
        end

        def new
          if @permissions&.create_and_edit_users
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_users_path)
            return
          end
          invoke_callbacks(:new_action, :before)
          respond_with(@object) do |format|
            format.html { render(layout: !request.xhr?) }
            format.js   { render(layout: false) } if request.xhr?
          end
        end

        def edit
          if @permissions&.create_and_edit_users
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_users_path)
            return
          end
          respond_with(@object) do |format|
            format.html { render(layout: !request.xhr?) }
            format.js   { render(layout: false) } if request.xhr?
          end
        end

        def destroy
          if @permissions&.delete_users
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            respond_with(@object) do |format|
              format.html { redirect_to(collection_url) }
              format.js { render_js_for_destroy }
            end
            return
          end
          invoke_callbacks(:destroy, :before)
          if @object.destroy
            invoke_callbacks(:destroy, :after)
            flash[:success] = flash_message_for(@object, :successfully_removed)
          else
            invoke_callbacks(:destroy, :fails)
            flash[:error] = @object.errors.full_messages.join(", ")
          end

          respond_with(@object) do |format|
            format.html { redirect_to(location_after_destroy) }
            format.js   { render_js_for_destroy }
          end
        end

        def addresses
          if request.put?
            params[:user][:bill_address_attributes][:user_id] = @user.id if params[:user][:bill_address_attributes].present?
            params[:user][:ship_address_attributes][:user_id] = @user.id if params[:user][:ship_address_attributes].present?
            if @user.update(user_params)
              flash[:success] = ::Spree.t(:account_updated)
              redirect_to(spree.addresses_admin_user_path(@user))
            else
              render(:addresses, status: :unprocessable_entity)
            end
          end
        end

        def set_permissions
          @permissions = current_user_permissions.find_permission
        end

        def current_user_permissions
          @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
        end

        def user_params
          query_params = params.require(:user).permit(permitted_user_attributes |
                                       [
                                         :use_billing,
                                         spree_role_ids: [],
                                         spree_store_ids: [],
                                         ship_address_attributes: permitted_address_attributes,
                                         bill_address_attributes: permitted_address_attributes,
                                       ])
          role_users = []
          query_params[:spree_role_ids]&.each do |role_id|
            next if role_id.empty?

            role_user = ::Spree::RoleUser.new
            role_user.role_id = role_id
            role_user.store_id = current_store.id
            role_users << role_user
          end
          query_params = query_params.merge(role_users: role_users)
          query_params.extract!(:spree_role_ids)
          query_params
        end
      end
    end
  end
end

Spree::Admin::UsersController.prepend(AxelSpree::Spree::Admin::UsersControllerDecorator)
