# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module TaxjarSettingsControllerDecorator
        def update
          taxjar_setting = ::Spree::TaxjarSetting.where(store: current_store).first_or_initialize
          taxjar_setting.taxjar_api_key = params[:taxjar_api_key]
          taxjar_setting.taxjar_enabled = params[:taxjar_enabled]
          taxjar_setting.taxjar_debug_enabled = params[:taxjar_debug_enabled]
          taxjar_setting.taxjar_sandbox_environment_enabled = params[:taxjar_sandbox_environment_enabled]

          if taxjar_setting.save
            flash[:success] = ::Spree.t(:taxjar_settings_updated)
            redirect_to(edit_admin_taxjar_settings_path)
          else
            flash[:error] = "taxjar setting updated failed."
            render(:edit, status: :unprocessable_entity)
          end
        end

        def refresh
          ::SpreeTaxjar::Categories.refresh(current_store)
          flash[:success] = "Tax categories refreshed by taxjar."
          redirect_to(edit_admin_taxjar_settings_path)
        end
      end
    end
  end
end

Spree::Admin::TaxjarSettingsController.prepend(AxelSpree::Spree::Admin::TaxjarSettingsControllerDecorator)
