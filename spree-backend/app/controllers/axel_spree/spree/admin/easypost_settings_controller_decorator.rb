# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module EasypostSettingsControllerDecorator
        def self.prepended(base)
          base.before_action(:load_stock_locations, only: [:edit, :update])
          base.before_action(:load_easypost_setting, only: [:edit, :update])
        end

        def edit
        end

        def update
          if @easypost_setting.update(easypost_settings_params)
            # update_easypost_settings
            redirect_to(edit_admin_easypost_setting_path)
          end
        end

        private

        def load_stock_locations
          @stock_locations = ::Spree::StockLocation.for_store(current_store)
        end

        def load_easypost_setting
          @easypost_setting = ::Spree::EasypostSetting.where(store: current_store).first_or_initialize
        end

        def easypost_settings_params
          params.permit(
            :buy_postage_when_shipped,
            :validate_address,
            :use_easypost_on_frontend,
            :customs_signer,
            :customs_contents_type,
            :customs_eel_pfc,
            :carrier_accounts_shipping,
            :carrier_accounts_returns,
            :endorsement_type,
            :returns_stock_location_id,
            :key,
          )
        end
      end
    end
  end
end

Spree::Admin::EasypostSettingsController.prepend(AxelSpree::Spree::Admin::EasypostSettingsControllerDecorator)
