# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module StockTransfersControllerDecorator
        def index
          @q = current_store.stock_transfers.ransack(params[:q])

          @stock_transfers = @q.result
            .includes(stock_movements: { stock_item: :stock_location })
            .order(created_at: :desc)
            .page(params[:page])
        end

        def create
          if params[:variant].nil?
            flash.now[:error] = ::Spree.t("stock_transfer.errors.must_have_variant")
            render(:new, status: :unprocessable_entity)
          elsif any_missing_variants?(params[:variant])
            flash.now[:error] = ::Spree.t("stock_transfer.errors.variants_unavailable", stock: source_location.name)
            render(:new, status: :unprocessable_entity)
          else
            variants = Hash.new(0)
            params[:variant].each_with_index do |variant_id, i|
              variants[variant_id] += params[:quantity][i].to_i
            end

            command = ::Admin::TransferStockCommand.call(
              store: current_store,
              source_location: source_location,
              destination_location: destination_location,
              reference: params[:reference],
              variants: variants,
            )

            stock_transfer = command.result

            flash[:success] = ::Spree.t(:stock_successfully_transferred)
            redirect_to(spree.admin_stock_transfer_path(stock_transfer))
          end
        end

        private

        def load_stock_locations
          @stock_locations = current_store.stock_locations.active.order_default
        end
      end
    end
  end
end

Spree::Admin::StockTransfersController.prepend(AxelSpree::Spree::Admin::StockTransfersControllerDecorator)
