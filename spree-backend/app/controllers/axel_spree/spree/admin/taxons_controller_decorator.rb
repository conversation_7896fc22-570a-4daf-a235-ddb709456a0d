# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Admin
      module TaxonsControllerDecorator
        def permitted_taxon_attributes
          [
            :name,
            :parent_id,
            :position,
            :icon,
            :description,
            :permalink,
            :hide_from_nav,
            :taxonomy_id,
            :meta_description,
            :meta_keywords,
            :meta_title,
            :child_index,
            :exclude_from_data_feed,
          ]
        end

        def flash_message_for(object, event_sym)
          resource_desc = "Category"

          if (object.persisted? || object.destroyed?) &&
              object.respond_to?(:name) &&
              object.name.present? &&
              !object.is_a?(::Spree::Order)
            resource_desc += " \"#{object.name}\""
          end

          ::Spree.t(event_sym, resource: resource_desc)
        end

        ::Spree::Admin::TaxonsController.prepend(self)
      end
    end
  end
end
