# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module PaymentsControllerDecorator
            def update
              render_error_payload("Once a PayPal payment has been initiated, it cannot be modified. If you need to make changes, please initiate a new one with the desired details.") and return if @resource.payment_method.paypal?
              super
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Platform::PaymentsController.prepend(AxelSpree::Spree::Api::V2::Platform::PaymentsControllerDecorator)
