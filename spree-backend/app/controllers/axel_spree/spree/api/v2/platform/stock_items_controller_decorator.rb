# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module StockItemsControllerDecorator
            private

            def authorize_spree_user
              return if spree_current_user.nil?

              case action_name
              when "create"
                spree_authorize!(:create, model_class)
              when "destroy"
                spree_authorize!(:destroy, resource)
              when "index"
                spree_authorize!(:read, model_class)
              when "show"
                spree_authorize!(:read, resource)
              end
            end

            ::Spree::Api::V2::Platform::StockItemsController.prepend(self)
          end
        end
      end
    end
  end
end
