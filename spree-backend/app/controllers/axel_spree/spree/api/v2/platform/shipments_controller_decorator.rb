# frozen_string_literal: true
require 'net/http'
require 'base64'

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module ShipmentsControllerDecorator
            SHIPMENT_STATES = ["ready", "ship", "cancel", "resume", "pend"]
            def self.prepended(base)
              base.include Rails.application.routes.url_helpers
              base.before_action(:update_order_package, only: [:update])
            end

            def update_order_package
              if params[:id].present? && params.dig(:shipment, :tracking).present?
                order_package = ::Spree::Shipment.find_by(number: params[:id]).order_package
                order_package.update(tracking: params.dig(:shipment, :tracking))
                order_package.shipments.update(tracking: order_package.tracking)
              end
            end

            def update_selected_shiping_rate_cost
              raise StandardError, "shipment not found" if resource.blank?

              cost = params.dig(:cost)
              raise StandardError, "cost field is required" if cost.blank?

              order_package = resource.order_package
              raise StandardError, "order_package not found" if order_package.blank?

              selected_shipping_rate = order_package.tracking? ? order_package.selected_shipping_rate : order_package.shipments.first.selected_shipping_rate
              raise StandardError, "selected_shipping_rate not found" if selected_shipping_rate.blank?

              selected_shipping_rate.update(cost: cost)

              render(status: :ok)
            rescue StandardError => e
              render_error_payload(e.message)
            end

            def transfer_to_location
              stock_location = ::Spree::StockLocation.find(params.dig(:shipment, :stock_location_id))
              quantity = params.dig(:shipment, :quantity)&.to_i || 1

              if quantity <= 0
                message = "#{I18n.t("spree.api.shipment_transfer_errors_occurred")} \n #{I18n.t("spree.api.negative_quantity")}"
                render_error_payload(message)
                return
              end

              if params.dig(:shipment, :new_variant_id).present?
                new_variant = current_store.variants.find_by(id: params.dig(:shipment, :new_variant_id))
              end
              transfer = resource.transfer_to_location(@variant, quantity, stock_location, new_variant)
              if transfer.valid? && transfer.run!
                render(json: { message: I18n.t("spree.api.shipment_transfer_success") }, status: :created)
              else
                render_error_payload(transfer.errors)
              end
            end

            SHIPMENT_STATES.each do |state|
              define_method state do
                ::ActiveRecord::Base.transaction do
                  if state == "ship" && params[:shipment_ids].present?
                    shipments = ::Spree::Shipment.where(id: params[:shipment_ids])
                    order = shipments.take.order
                    order_package = shipments.first.order_package || order.order_packages.create

                    shipments.update_all(order_package_id: order_package.id) # rubocop:disable Rails/SkipsModelValidations
                    unless non_easypost_shipment(shipments.first)
                      order_package.refresh_rates(::Spree::ShippingMethod::DISPLAY_ON_BACK_END, {})
                      selected_shipping_method = shipments.take.selected_shipping_rate.shipping_method

                      order_package.shipping_rates.each do |rate|
                        if rate.shipping_method.id == selected_shipping_method.id
                          order_package.selected_shipping_rate = rate
                          order_package.save!
                        end
                      end
                      order_package.buy_easypost_rate
                      update_tracking_info(order_package)
                    end

                    shipments.each do |shipment|
                      shipment.buy_postage_shipment = true
                      shipment.refresh_rates(::Spree::ShippingMethod::DISPLAY_ON_BACK_END, {})
                      @ship_result = change_state_service.call(
                        shipment: shipment,
                        state: state,
                        current_user: spree_current_user,
                        action_place: edit_admin_order_path(shipment&.order),
                        action: "#{state.titleize} Order",
                      )

                      raise ::ActiveRecord::Rollback unless @ship_result.success?
                    end
                    # rubocop:disable Rails/SkipsModelValidations

                    if order_package.update_columns(state: "shipped")
                      shipments.each do |shipment|
                        shipment_order = shipment.order
                        store = shipment_order.store
                        shipment.stock_item_units
                          .select(
                            <<~SQL.squish,
                              DISTINCT ON (vendor_inventory_number) *,
                              COUNT(*) OVER (PARTITION BY vendor_inventory_number) AS qty_count
                            SQL
                          )
                          .each do |stock_item_unit|
                            update_price_compare_inventory(stock_item_unit, shipment_order, store)
                          end
                      end
                    end

                    order_package.shipments.update_all(tracking: order_package.tracking, tracking_label: order_package.tracking_label)

                    ::Spree::ShipmentMailer.shipped_email(order_package.shipments.first.id).deliver_later

                  # rubocop:enable Rails/SkipsModelValidations
                  else
                    result = change_state_service.call(
                      shipment: resource,
                      state: state,
                      current_user: spree_current_user,
                      action_place: edit_admin_order_path(resource&.order),
                      action: "#{state.titleize} Order",
                    )
                    raise ::ActiveRecord::Rollback unless result.success?
                  end
                end

                render_result(@ship_result || result)
              end
            end

            def update_tracking_info(order_package)
              return if order_package.shipments.blank?

              order_package.shipments.each do |shipment|
                shipment.buy_postage_shipment = true
                shipment.update(
                  tracking: order_package.tracking,
                  tracking_label: order_package.tracking_label,
                  cost: order_package.selected_shipping_rate.cost
                )
              end
            end

            def non_easypost_shipment(shipment)
              shipment.line_items.any? do |line_item|
                line_item.listing&.shipping_category&.shipping_methods&.any?(&:local_pickup)
              end || shipment.line_items.any? do |line_item|
                       line_item.listing&.shipping_category&.name == "Free Shipping"
                     end
            end

            def shipping_rates
              # ::ActiveRecord::Base.transaction do
                shipment = @resource
                order = shipment.order
                shipment_ids = params[:shipment_ids]
                if shipment_ids
                  shipments = ::Spree::Shipment.where(id: shipment_ids)
                  order_package = shipments&.first&.order_package
                  order_package = new_order_package = order.order_packages.create! if order_package.nil?
                  # rubocop:disable Rails/SkipsModelValidations
                  shipments.update_all(order_package_id: order_package.id)
                  # rubocop:enable Rails/SkipsModelValidations

                  details = "OrderPackage created with number - #{order_package.number}
                            with associated shipmnet ids - #{shipment_ids}"
                  order.tracker_logs.create(details:)
                end

                obj = shipment_ids.present? ? order_package : shipment
                obj.refresh_rates(::Spree::ShippingMethod::DISPLAY_ON_BACK_END, {
                  weight: params[:weight],
                  height: params[:height],
                  length: params[:length],
                  width: params[:width],
                  weight_unit: params[:weight_unit],
                  dimension_unit: params[:dimension_unit],
                  buy_postage: params[:buy_postage],
                })

                render_serialized_payload do
                  if shipment_ids.present?
                    serialize_order_package(obj.reload.shipping_rates.order(:id).where("cost > ?", 0), new_order_package)
                  else
                    serialize_shipping_rates(obj.reload.shipping_rates.order(:id).where("cost > ?", 0))
                  end
                end
              # end
            end

            def serialize_shipping_rates(shipping_rates)
              ::Spree::V2::Storefront::ShippingRateSerializer.new(
                shipping_rates,
              ).serializable_hash
            end

            def serialize_order_package(shipping_rates, order_package = nil)
              ::Spree::V2::Storefront::OrderPackageSerializer.new(
                shipping_rates, params: {order_package_id: order_package&.id}
              ).serializable_hash
            end

            def buy_postage
              ::ActiveRecord::Base.transaction do
                shipment = ::Spree::Shipment.find_by(id: params[:shipment_ids])
                resource = shipment.order_package

                create_ordered_listing_info(params[:shipment_ids])

                @updated_weight = {
                  weight: params["weight"],
                  width: params["width"],
                  length: params["length"],
                  height: params["height"],
                }
                if params[:selected_shipping_rate_id].present? || params[:buy_postage] == "walmart" || params[:buy_postage] == "amazon"
                  postage_via = if params[:buy_postage] == "walmart"
                                  "Walmart BuyPostage"
                                elsif params[:buy_postage] == "amazon"
                                  "Amazon BuyPostage"
                                else
                                  "BuyPostage"
                                end
                  details = "#{postage_via} clicked for order - #{resource.order.number} and order_package - #{resource.number}"
                  resource.order.tracker_logs.create(details:)
                  insure = false
                  insure = true if (params[:insure] || params['insure'] == "true") || (params[:insure] || params['insure'] == true)
                  insure_for_amount = insure ? (params[:insure_for_amount] || params['insure_for_amount']) : nil

                  # rubocop:disable Rails/SkipsModelValidations
                  resource.shipping_rates.update_all(selected: false)
                  # rubocop:enable Rails/SkipsModelValidations
                  resource.shipping_rates.update(params[:selected_shipping_rate_id], selected: true)
                  if resource.class.name == "Spree::OrderPackage"
                    resource.update({
                      selected_shipping_rate_id: params[:selected_shipping_rate_id],
                      insure: insure,
                      insure_for_amount: insure_for_amount,
                    })
                  else
                    resource.shipments.update({
                      selected_shipping_rate_id: params[:selected_shipping_rate_id],
                      buyer_paid_amount: resource.cost,
                      email_template_id: params[:shipment_email_id],
                      custom_message: params[:custom_message],
                      insure: insure,
                      insure_for_amount: insure_for_amount,
                    })
                  end

                  resource.reload
                  resource.update_amounts
                  details = "Successfully updated the shipping cost (#{resource.selected_shipping_rate.cost}) " \
                    "to the shipment corresponding to order package ##{resource.number} " \
                    "using the resource.update_amounts method and updated shipment cost is " \
                    "#{resource.shipments.first.cost}."

                  resource.order.tracker_logs.create(details:)
                  resource.reload
                end

                begin
                  # find_and_update_shipment

                  params[:shipment] ||= {}
                  unless resource.tracking_label?
                    if params[:buy_postage] == "walmart"
                      @parcel = {
                        weight: params["weight"],
                        width: params["width"],
                        length: params["length"],
                        height: params["height"],
                        carrier: params["carrier"],
                        carrier_service_type: params["carrier_service_type"],
                        dimension_unit: params[:dimension_unit],
                        weight_unit: params[:weight_unit],
                      }
                      resource.walmart_create_label(@parcel)
                    elsif params[:buy_postage] == "amazon"
                      @parcel = {
                        weight: params["weight"],
                        width: params["width"],
                        length: params["length"],
                        height: params["height"],
                        carrier: params["carrier"],
                        carrier_service_type: params["carrier_service_type"],
                        dimension_unit: params[:dimension_unit],
                        weight_unit: params[:weight_unit],
                        selected_shipping_rate_id: params[:selected_shipping_rate_id],
                      }
                      resource.amazon_create_label(@parcel)
                    else
                      resource.buy_easypost_rate(@updated_weight)
                    end
                    resource.state = "shipped"

                    if resource.save!
                      resource.shipments.each do |shipment|
                        shipment_order = shipment.order
                        store = shipment_order.store
                        shipment.stock_item_units
                          .select(
                            <<~SQL.squish,
                              DISTINCT ON (vendor_inventory_number) *,
                              COUNT(*) OVER (PARTITION BY vendor_inventory_number) AS qty_count
                            SQL
                          )
                          .each do |stock_item_unit|
                            update_price_compare_inventory(stock_item_unit, shipment_order, store)
                          end
                      end
                    end
                    update_associated_shipments(resource, params)

                    ::Spree::ShipmentMailer.shipped_email(resource.shipments.first.id).deliver_later
                    unless resource.state == "shipped"
                      # This attribute accessor is to skip "before_transition" state machine.
                      resource.buy_postage_shipment = true
                      resource.update(state: "shipped")
                    end
                  end
                  respond_with(resource.reload, default_template: :show)
                rescue EasyPost::Errors => e
                  resource.order.tracker_logs.create(details: "Encountered error while BuyPostage process.
                                                              Error message - #{e.message}")
                  render(json: { error: e.message }, status: :bad_request)
                rescue => e
                  resource.order.tracker_logs.create(details: "Encountered error while BuyPostage process.
                                                              Error message for #{resource.number} is - #{e.message}")
                  render(json: { error: e.message }, status: :bad_request)
                end
              end
            end

            def delete_shipping_label
              shipment = ::Spree::Shipment.find_by(number: params[:id])
              unless shipment
                render(json: { error: "Shipment not found" }) and return
              end

              order_package = shipment.order_package
              shipment_rate = order_package&.selected_shipping_rate

              begin
                if order_package.shipping_source == "walmart shipping"
                  order_package.discard_postage_label
                elsif order_package.shipping_source == "amazon shipping"
                  order_package.discard_amazon_postage_label
                else
                  api_key = shipment.order.store.easypost_setting.key
                  client =  EasyPost::Client.new(api_key: api_key)
                  carrier = order_package&.selected_shipping_rate&.name
                  tracking_code = order_package.tracking

                  client.refund.create(carrier: carrier, tracking_codes: [tracking_code])
                end
                # Create a refund for purchased shipment on easypost
                order_package.shipments.update(
                  tracking: nil,
                  tracking_label: nil,
                  cost: 0.0,
                  buyer_paid_amount: nil,
                  state: "ready",
                  email_template_id: nil,
                  custom_message: "",
                  order_package_id: nil
                )

                details = "Successfully updated the shipping cost (0) " \
                  "to the shipment corresponding to order package ##{resource.number} " \
                  "using the delete_shipping_label method and updated shipment cost is " \
                  "#{order_package.shipments.first.cost}."

                resource.order.tracker_logs.create(details:)

                shipment_rate.update(selected: false)
                order_package.update(tracking: nil, tracking_label: nil, state: "ready")
                revise_stock_item_unit_state(order_package)
                order_package.destroy
                render(json: { message: "success" })
              rescue StandardError => e
                render(json: { error: e.message })
              end
            end

            def add_item
              quantity = update_line_item_quantity(params.dig(:shipment, :line_item_id), params.dig(:shipment, :quantity))
              result = ::Spree::Shipments::AddItem.call(
                shipment: resource,
                variant_id: params.dig(:shipment, :variant_id),
                quantity: quantity,
                options: { listing_id: params.dig(:shipment, :listing_id) }
              )
              render_result(result)
            end

            def remove_item
              quantity = update_line_item_quantity(params.dig(:shipment, :line_item_id), params.dig(:shipment, :quantity))
              result = ::Spree::Shipments::RemoveItem.call(
                shipment: resource,
                variant_id: params.dig(:shipment, :variant_id),
                quantity: quantity,
                options: { listing_id: params.dig(:shipment, :listing_id) }
              )

              if result.success?
                if result.value == :shipment_deleted
                  head 204
                else
                  render_serialized_payload { serialize_resource(result.value) }
                end
              else
                render_error_payload(result.error)
              end
            end

            def attach_pdf
              begin
                shipment = ::Spree::Shipment.find_by(number: params[:id])
                if shipment.nil?
                  render json: { error: "Shipment not found" }, status: :not_found
                  return
                end

                order_package = shipment.order_package
                store_id = order_package.order.store_id || ::Spree::Store.default.id
                carrier_short_name = order_package.selected_shipping_rate.shipping_method.code
                valid_call = order_package.order.channel == "walmart" && order_package.shipping_source == "walmart shipping"
                oauth_application = ::Spree::SaleChannel.find_by(id: order_package.order.sale_channel_id)&.oauth_application

                if oauth_application.nil?
                  render json: { error: "OAuth application not found" }, status: :unprocessable_entity
                  return
                end

                response = ::Walmart::ShippingApis::DownloadLabel.new(store_id, oauth_application.id).call(order_package.tracking, carrier_short_name)

                if valid_call && response.present?
                  attach_pdf_to_shipment(shipment, response.body)
                  order_package.update(tracking_label: shipment.tracking_label)
                  render json: { success: "PDF attached successfully" }, status: :ok
                else
                  render json: { error: "Invalid call or no response received" }, status: :unprocessable_entity
                end
              rescue StandardError => e
                Rails.logger.error("Error in attach_pdf: #{e.message}")
                render json: { error: "An error occurred while processing the request" }, status: :internal_server_error
              end
            end

            def attach_amazon_pdf
              begin
                shipment = ::Spree::Shipment.find_by(number: params[:id])
                if shipment.nil?
                  render json: { error: "Shipment not found - attach_amazon_pdf" }, status: :not_found
                  return
                end

                if shipment.tracking_label.nil?
                  order_package = shipment.order_package
                  store_id = order_package.order.store_id || ::Spree::Store.default.id
                  carrier_short_name = order_package.selected_shipping_rate.shipping_method.code
                  amazon_shipment_id = order_package&.amazon_shipment_detail&.amazon_shipment_id
                  package_client_reference_id = shipment.number
                  valid_call = order_package.order.channel == "amazon" && order_package.shipping_source == "amazon shipping"
                  oauth_application = ::Spree::SaleChannel.find_by(id: order_package.order.sale_channel_id)&.oauth_application

                  if oauth_application.nil?
                    render json: { error: "OAuth application not found - attach_amazon_pdf" }, status: :unprocessable_entity
                    return
                  end

                  response = ::Amazon::ShippingApis::GetShipmentDocuments.new(store_id, oauth_application.id).get_shipment_documents(amazon_shipment_id, package_client_reference_id)
                  if valid_call && response.present?
                    tracking_id = response.dig("packageDocumentDetails", 0, "trackingId")
                    order_package&.amazon_shipment_detail&.update_columns(
                      amazon_shipment_tracking_id: tracking_id
                    ) if tracking_id.present?

                    order_package&.update_columns(
                      tracking: tracking_id
                    ) if tracking_id.present?

                    package_document= response.dig("packageDocumentDetails", 0)
                    pdf_data_bs = package_document.dig("packageDocuments", 0, "contents")
                    pdf_data = if pdf_data_bs
                      Base64.decode64(pdf_data_bs)
                    else
                      nil
                    end
                    attach_pdf_to_shipment(shipment, pdf_data) if package_document.present? && pdf_data.present?
                    order_package.update(tracking_label: shipment&.tracking_label) if shipment&.tracking_label
                    render json: { success: "Amazon PDF attached successfully" }, status: :ok
                  else
                    render json: { error: "Invalid call or no response received - attach_amazon_pdf" }, status: :unprocessable_entity
                  end
                else
                  render json: { success: "Amazon PDF already attached successfully" }, status: :ok
                end
              rescue StandardError => e
                Rails.logger.error("Error in attach_amazon_pdf: #{e.message}")
                render json: { error: "An error occurred while processing the request - attach_amazon_pdf" }, status: :internal_server_error
              end
            end

            private

            def update_line_item_quantity(line_item_id, params_quantity)
              line_item = ::Spree::LineItem.find_by(id: line_item_id)
              # line_item.present? && line_item.storefront_sale_channel? ? (params_quantity&.to_i * (line_item.listing&.pack_size_value || 1)) : params_quantity&.to_i
              line_item.present? && line_item.storefront_sale_channel? ? (params_quantity.to_i * line_item.pack_size) : params_quantity
            end

            def update_price_compare_inventory(stock_item_unit, order, store)
              vin = stock_item_unit.vendor_inventory_number

              # return if vin.blank?

              ::Admin::UpdateInventoryCommand.call(
                vin: vin,
                qty: stock_item_unit.qty_count,
                order: order,
                store: store,
              )
            end

            def update_associated_shipments(resource, params)
              resource.shipments.each do |shipment|
                shipment.buy_postage_shipment = true
                shipment.assign_attributes(
                  tracking: resource.tracking,
                  tracking_label: resource.tracking_label,
                  custom_message: params[:custom_message],
                  email_template_id: params[:shipment_email_id],
                  cost: resource.selected_shipping_rate.cost
                )
                shipment.current_user = spree_current_user
                shipment.action_place = edit_admin_order_path(shipment.order_id)
                shipment.action = 'Buy Postage Order'
                shipment.save!
                shipment.reload.ship!
              end

              log_details = "Successfully updated the shipping cost (#{resource.selected_shipping_rate.cost}) " \
                            "to the shipment corresponding to order package ##{resource.number}. " \
                            "Updated shipment cost is #{resource.shipments.first.cost}."

              resource.order.tracker_logs.create(details: log_details)

              if resource.order.shipments.all? { |shipment| shipment.state == "shipped" }
                resource.order.update(shipment_state: "shipped")
              elsif resource.order.shipments.any? { |shipment| shipment.state == "shipped" }
                resource.order.update(shipment_state: "partial")
              end
            end

            def revise_stock_item_unit_state(order_package)
              order_package.shipments.each do |shipment|
                shipment.stock_item_units.each do |siu|
                  siu.update!(state: :locked)
                  record_activity_log(siu, shipment)
                end
                shipment.inventory_units.update(state: "on_hand")
              end
              order_package.order.update(shipment_state: "ready")
            end

            def record_activity_log(stock_item_unit, shipment)
              order = shipment.order
              ::Spree::ActivityLog.create!(
                loggable: stock_item_unit,
                user_id: spree_current_user.id,
                action_name: order.number,
                role: spree_current_user.spree_roles.first&.name,
                date: Time.current,
                email: spree_current_user.email,
                action_place:  edit_admin_order_path(order),
                action: ::Spree::ActivityLogActions.get(:stock_item_unit, :cancel_label),
                product_id: stock_item_unit.stock_item&.product&.id
              )
            end

            def attach_pdf_to_shipment(shipment, pdf_data)
              shipment.shipping_label.attach(
                io: StringIO.new(pdf_data),
                filename: "shipping_label_#{shipment.id}.pdf",
                content_type: 'application/pdf'
              )
              shipment.update(
                tracking_label: rails_blob_path(
                  shipment.shipping_label,
                  disposition: 'inline',
                  only_path: true
                )
              )
            end

            def create_ordered_listing_info(shipment)

              shipments = ::Spree::Shipment.where(id: params[:shipment_ids])

              accumulated_data = {}

              shipments.each do |shipment|
                shipment.line_items.each do |line_item|
                  accumulated_data[line_item.listing_id] ||= 0
                  accumulated_data[line_item.listing_id] += line_item.quantity
                end
              end

              return nil if accumulated_data.empty? || accumulated_data[nil].present?

              all_ordered_listings_data = accumulated_data.map do |listing_id, total_quantity|
                { listing_id: listing_id, quantity: total_quantity }
              end

              all_ordered_listings_data.sort_by! { |item| item[:listing_id] }
              ordered_listings_json = all_ordered_listings_data.to_json
              ordered_listing_info = ::Spree::OrderedListingsInfo.find_or_initialize_by(listing_quantities: ordered_listings_json)

              ordered_listing_info.assign_attributes(
                weight: params['weight'],
                length: params['length'],
                width: params["width"],
                height: params["height"],
                shipping_method_id: ::Spree::ShippingRate.find(params[:selected_shipping_rate_id]).shipping_method_id
              )

              ordered_listing_info.save!
            end

            ::Spree::Api::V2::Platform::ShipmentsController.prepend(self)
          end
        end
      end
    end
  end
end
