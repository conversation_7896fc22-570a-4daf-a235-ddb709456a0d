# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module ProductsControllerDecorator
            def collection
              es_listing = AxelSpree::Elasticsearch::Listing.new
              query_params = {
                title: params.dig(:filter, :name_i_cont) || params.dig(:filter, :name_or_master_sku_cont),
              }
              ids = es_listing.backend(query_params).product_ids
              @collection = scope.where(id: ids)
              super
            end

            def scope(skip_cancancan: false)
              base_scope = current_store.default ? model_class : model_class.for_store(current_store)
              base_scope = base_scope.with_status(:active)
              base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
              base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == "index"
              base_scope
            end

            private

            def serializer_params
              {
                currency: current_currency,
                locale: current_locale,
                price_options: current_price_options,
                store: current_store,
                user: spree_current_user,
                image_transformation: params[:image_transformation],
                taxon_image_transformation: params[:taxon_image_transformation],
                listing_id: params[:listing_id],
                controller: controller_name,
              }
            end

            def fetch_filtered_products
              sorted_collection.select("spree_products.*, listings.id as listing_id")
                .joins(listings: :sale_channel)
                .where(listings: { status: "Active" })
            end

            def paginated_collection
              @paginated_collection = collection_paginator.new(fetch_filtered_products, params).call
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Platform::ProductsController.prepend(AxelSpree::Spree::Api::V2::Platform::ProductsControllerDecorator)
