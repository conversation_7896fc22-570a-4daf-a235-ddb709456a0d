# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module MenuItemsControllerDecorator
            def reposition
              # This method source from spree_api-4.6.6 [Date: 2025-06-10]
              spree_authorize! :update, resource if spree_current_user.present?

              @new_parent = scope.find(permitted_resource_params[:new_parent_id])
              new_index = permitted_resource_params[:new_position_idx].to_i

              if resource.move_to_child_with_index(@new_parent, new_index)
                resource.touch
                # If successful reposition call the custom method for handling success.
                successful_reposition_actions
              elsif resource.errors.any?
                # If there are errors output them to the response
                render_error_payload(resource.errors.full_messages.to_sentence)
              else
                # If the user drops the re-positioned item in the same location it came from
                # we just render the serialized payload, nothing has changed, we don't need to
                # render any errors, or fire any custom success methods.
                render_serialized_payload { serialize_resource(resource) }
              end
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Platform::MenuItemsController.prepend(AxelSpree::Spree::Api::V2::Platform::MenuItemsControllerDecorator)
