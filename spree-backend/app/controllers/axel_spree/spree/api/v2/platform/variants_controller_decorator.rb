# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module VariantsControllerDecorator
            def index
              render_serialized_payload do
                Rails.cache.fetch(collection_cache_key(paginated_collection), collection_cache_opts) do
                  serialize_collection(paginated_collection)
                end
              end
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Platform::VariantsController.prepend(AxelSpree::Spree::Api::V2::Platform::VariantsControllerDecorator)
