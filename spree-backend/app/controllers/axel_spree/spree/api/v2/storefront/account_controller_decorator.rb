# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Storefront
          module AccountControllerDecorator
            include ::Spree::Admin::Subscribers<PERSON><PERSON><PERSON>
            def self.prepended(base)
              base.before_action(:require_spree_current_user, except: [:create, :resend_confirmation])
            end

            def create
              result = create_service.call(user_params: user_create_params)
              if result.success? && result&.value&.email.present?
                subscriber = ::Spree::Subscriber.find_or_create_by(email: result&.value&.email)
                process_subscriber_action(subscriber, 'registered') if subscriber
              end
              render_result(result)
            end

            def resend_confirmation
              @user = ::Spree::User.find_by(email: params[:email].downcase) if params[:email]
              if @user
                @user.resend_confirmation_instructions(current_store)
                render(json: { message: "Invitation resent successfully." })
              else
                render(
                  json: {
                    errors: ["No user found with #{params[:email]}."],
                  },
                  status: :unprocessable_entity,
                )
              end
            end

            def user_create_params
              customer_role = ::Spree::Role.where(name: :store_customer).first_or_initialize
              user_update_params.except(:bill_address_id, :ship_address_id)
                .merge(spree_stores: current_store.default ? [current_store] : [::Spree::Store.default, current_store])
                .merge(role_users: [::Spree::RoleUser.new(store: current_store, role: customer_role)])
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Storefront::AccountController.prepend(AxelSpree::Spree::Api::V2::Storefront::AccountControllerDecorator)
