# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Storefront
          module IntentsControllerDecorator
            def create
              spree_authorize!(:update, spree_current_order, order_token)
              # enter storefront createPaymentIntent step
              spree_current_order&.payments&.destroy_all # destroy all previous payments of the order

              last_valid_payment = spree_current_order.payments.valid.where.not(intent_client_key: nil).last

              if last_valid_payment.blank?
                if spree_current_order.payments.valid.count == 0
                  create_payment_service.call(
                    order: spree_current_order,
                    params: { payment_method_id: params["methodId"] },
                  )
                end
                # spree_current_order.reload
                spree_current_order.create_payment_intent!
                spree_current_order.reload
                last_valid_payment = spree_current_order.payments.valid.where.not(intent_client_key: nil).last
              end

              if last_valid_payment.present?
                client_secret = last_valid_payment.intent_client_key
                return render(json: { client_secret: client_secret }, status: :ok)
              end

              render_error_payload(I18n.t("spree.no_payment_intent_created"))
            end

            def create_payment_service
              ::Spree::Api::Dependencies.storefront_payment_create_service.constantize
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Storefront::IntentsController.prepend(AxelSpree::Spree::Api::V2::Storefront::IntentsControllerDecorator)
