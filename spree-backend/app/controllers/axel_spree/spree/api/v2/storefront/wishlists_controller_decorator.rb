# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Storefront
          module WishlistsControllerDecorator
            def add_item
              spree_authorize! :create, ::Spree::WishedItem
              if resource.wished_items.present? && resource.wished_items.detect { |wv| wv.variant_id.to_s == params[:variant_id].to_s && wv.listing_id.to_s == params[:listing_id] }.present?
                @wished_item = resource.wished_items.detect { |wi| wi.variant_id.to_s == params[:variant_id].to_s }
                @wished_item.quantity = params[:quantity]
                @wished_item.listing_id = params[:listing_id]
              else
                @wished_item = ::Spree::WishedItem.new(params.permit(:quantity, :variant_id, :listing_id))
                @wished_item.wishlist = resource
                @wished_item.save
              end

              resource.reload

              if @wished_item.persisted?
                render_serialized_payload { serialize_wished_item(@wished_item) }
              else
                render_error_payload(resource.errors.full_messages.to_sentence)
              end
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Storefront::WishlistsController.prepend(AxelSpree::Spree::Api::V2::Storefront::WishlistsControllerDecorator)
