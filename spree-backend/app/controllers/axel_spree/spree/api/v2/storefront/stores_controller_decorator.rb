# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Storefront
          module StoresControllerDecorator
            def index
              render_serialized_payload do
                Rails.cache.fetch(collection_cache_key(collection), collection_cache_opts) do
                  collection_serializer.new(
                    collection,
                  ).serializable_hash
                end
              end
            end

            def collection_serializer
              ::Spree::Api::V2::Storefront::StoreSerializer
            end

            def scope(skip_cancancan: false)
              base_scope = current_store.default ? model_class : model_class.for_store(current_store)
              base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
              base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == "index"
              base_scope
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Storefront::StoresController.prepend(AxelSpree::Spree::Api::V2::Storefront::StoresControllerDecorator)
