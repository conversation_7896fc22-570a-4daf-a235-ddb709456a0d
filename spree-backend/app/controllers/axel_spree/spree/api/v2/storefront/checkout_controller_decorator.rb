# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Storefront
          module CheckoutControllerDecorator
            include ::Spree::Admin::SubscribersHelper
            def self.prepended(base)
              base.before_action(:destroy_previous_payments, only: :update)
              base.before_action(:check_subscriber, only: :update)
            end

            def serialize_shipping_rates(shipments)
              shipping_rates_serializer.new(
                shipments,
                params: serializer_params,
                include: [:shipping_rates, :stock_location, :inventory_units],
              ).serializable_hash
            end

            private

            def destroy_previous_payments
              order_params = params.dig(:order)

              if order_params.present?
                shipments_attributes = order_params.dig(:shipments_attributes)
                if shipments_attributes.present?
                  sh = shipments_attributes[0]
                  sl_shipping_rate_id = sh['selected_shipping_rate_id']
                  if sl_shipping_rate_id.present? # make sure enter storefront saveShippingMethod step
                    spree_current_order&.payments&.destroy_all # destroy all previous payments of the order
                  end
                end
              end
            end

            def check_subscriber
              order_params = params.dig(:order)

              if order_params.present?
                email = order_params.dig(:email)
                if email.present? # make sure enter storefront fill in shipping address step
                  subscriber = ::Spree::Subscriber.find_or_create_by(email: email)
                  process_subscriber_action(subscriber, 'filling in shipping address', spree_current_order) if subscriber
                end
              end
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Storefront::CheckoutController.prepend(AxelSpree::Spree::Api::V2::Storefront::CheckoutControllerDecorator)
