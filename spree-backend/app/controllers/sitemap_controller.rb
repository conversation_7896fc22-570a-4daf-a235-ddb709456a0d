# frozen_string_literal: true

class SitemapController < ApplicationController
  def show
    path = File.expand_path(File.join(allowed_path, "#{code}/sitemap.xml"))

    not_found unless path.match(Regexp.new("^" + Regexp.escape(allowed_path)))
    not_found unless File.exist?(path)
    send_file(path)
  end

  private

  def code
    ActiveStorage::Filename.new(params[:code]).sanitized
  end

  def allowed_path
    Rails.root.join("storage/sitemaps").to_s
  end

  def not_found
    raise ActionController::Routing<PERSON>rror, "Not Found"
  end
end
