module Api
  module V3
    module <PERSON>tchUserTenants
      extend ActiveSupport::Concern

      def fetch_user_tenants(email)
        admin_user = Spree::AdminUser.find_by(email: email)
        return render_error_payload('Invalid credentials: user not found.', :not_found) if admin_user.nil?

        organisations = admin_user.organisations
        return render_error_payload('Organisations not found.', :not_found) if organisations.blank?

        jwt_token = JsonWebToken.encode(email: email)
        two_factor_type = admin_user&.otp_required_for_login ? "google_authenticator_otp" : "email_otp"

        render json: {
          message: "Confirmation Code Verified Successfully",
          access_token: jwt_token,
          email: email,
          tenant_names: organisations.map do |org|
            {
              workplace_name: org.name,
              two_factor_required: org.two_factor_enabled,
              two_factor_type: two_factor_type,
              owned: org.admin_email == email
            }
          end
        }, status: :ok
      end
    end
  end
end
