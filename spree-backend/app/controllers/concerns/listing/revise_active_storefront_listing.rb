# frozen_string_literal: true

module Listing
  class ValidationError < StandardError; end

  module ReviseActiveStorefrontListing
    extend ActiveSupport::Concern

    def revise_active_storefront_listing
      ActiveRecord::Base.transaction do
        @listing = fetch_listing
        @product = fetch_product

        ::Listing::ValidateStorefrontService.new(@listing, @product, params).call
        ::Listing::UpdateStorefrontService.new(@listing, update_listing_params, params, spree_current_user, request).call
        log_action_details(@listing, params[:action])
      end
    rescue Listing::ValidationError => e
      Rails.logger.warn("[VALIDATION ERROR] Listing ID #{params[:id]} - #{e.message}")
      render_error_payload(e.message)
    rescue ActiveRecord::RecordNotFound, ActiveRecord::RecordInvalid => e
      Rails.logger.error("[MODEL ERROR] Listing ID #{params[:id]} - #{e.message}")
      render_error_payload(e.message)
    rescue StandardError => e
      Rails.logger.error("[UNEXPECTED ERROR] Listing ID #{params[:id]} - #{e.message}")
      render_error_payload("An unexpected error occurred. Please try again later.")
    end

    private

    def fetch_listing
      listing = Spree::Listing.find_by(id: params[:id])
      raise ActiveRecord::RecordNotFound, "Listing not found with id - #{params[:id]}" if listing.nil?

      listing
    end

    def fetch_product
      product = Spree::Product.find_by(id: params.dig(:listing, :product_id))
      raise ActiveRecord::RecordNotFound, "Product not found with id - #{params.dig(:listing, :product_id)}" if product.nil?
      raise ActiveRecord::RecordNotFound, "Stock items are missing for Product - #{product.name}" if product.stock_items.blank?

      if params.dig(:listing, :submit_type) == "publish" && !product.active?
        raise ActiveRecord::RecordInvalid, "Please update the product status to active first"
      end

      product
    end

    def update_listing_params
      params.require(:listing).permit(
        :sale_channel_id,
        :product_id,
        :start_time,
        :end_time,
        :status,
        :sku,
        :title,
        :description,
        :pack_size_toggle,
        :pack_size_value,
        :shipping_category_id,
        volume_prices_attributes: [
          :id,
          :variant_id,
          :discount_type,
          :range,
          :amount,
          :position,
          :role_id,
          :name,
          :_destroy,
        ],
      ).tap do |permitted_params|
        permitted_params[:status] = convert_status(permitted_params[:status]) if permitted_params[:status].present?
        permitted_params[:pack_size_value] = permitted_params[:pack_size_toggle] ? permitted_params[:pack_size_value] : 1
      end
    end

    def log_action_details(listing, action)
      Rails.logger.info("Action '#{action}' performed on Listing ID #{listing.id}")
      record_activity_log(listing, "Edit Listing")
    end
  end
end
