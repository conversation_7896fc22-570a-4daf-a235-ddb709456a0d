# frozen_string_literal: true

module Listing
  module ReviseActiveAmazonListing
    extend ActiveSupport::Concern

    def revise_amazon_active_listing
      render_error_payload("Method not support for amazon listing")
    end

    def quantity_price_quick_edit_amazon_listing
      ActiveRecord::Base.transaction do
        listing = find_listing
        product = listing.product
        raise ActiveRecord::RecordNotFound, "Product not found" unless product

        record_action_details(listing, "quantity_price_quick_edit_amazon_listing")
        record_details(listing, "quantity_price_quick_edit_amazon_listing")

        ::Listing::ValidateListingService.call(listing, params)
        ::Listing::UpdateListingService.call(listing, params, request, spree_current_user)
        if listing.status == "Active"
          inventories_attributes = params.dig(:listing, :listing_inventories_attributes)
          quantity = inventories_attributes[0]["quantity"]
          price = inventories_attributes[0]["price"]
          available_quantity = quantity.to_i
          my_price = price.to_f

          oauth_application_id = listing&.sale_channel&.oauth_application&.id
          store_id = listing&.sale_channel&.oauth_application&.store_id

          listing_data = {}
          item_specific_values = {}
          item_specific_values['package_contains_sku-sku'] = listing&.sku
          item_specific_values['fulfillment_availability-fulfillment_channel_code'] = 'DEFAULT'
          item_specific_values['fulfillment_availability-quantity'] = available_quantity
          item_specific_values['fulfillment_availability-marketplace_id'] = listing.sale_channel.oauth_application&.site_id
          listing_data['product_type'] = get_amazon_product_type(listing&.category_id)
          listing_data['item_specifics'] = item_specific_values

          Amazon::ListingApis::PatchListingItem.new(store_id, oauth_application_id).patch_listing_item_only_quantity(listing&.sku, listing_data, listing, available_quantity) if available_quantity >= 0
          sleep(1) # Sleep for a short time to avoid hitting API rate limits

          listing_data = {}
          item_specific_values = {}
          item_specific_values['package_contains_sku-sku'] = listing&.sku
          item_specific_values['purchasable_offer-currency'] = 'USD'
          item_specific_values['purchasable_offer-audience'] = 'ALL'
          our_price = []
          schedule = []
          s_1 = {}
          s_1['value_with_tax'] = my_price
          schedule.push(s_1)
          o_1 = {}
          o_1['schedule'] = schedule
          our_price.push(o_1)
          item_specific_values['purchasable_offer-our_price'] = our_price
          item_specific_values['purchasable_offer-marketplace_id'] = listing.sale_channel.oauth_application&.site_id
          listing_data['product_type'] = get_amazon_product_type(listing&.category_id)
          listing_data['item_specifics'] = item_specific_values

          Amazon::ListingApis::PatchListingItem.new(store_id, oauth_application_id).patch_listing_item_only_price(listing&.sku, listing_data, listing, my_price) if my_price >= 0
        end
        
        render_serialized_payload { serialize_resource(listing) }
      end
    rescue ActiveRecord::RecordNotFound, ActiveRecord::RecordInvalid => e
      render_error_payload(e)
    rescue StandardError => e
      render_error_payload(e)
    end

    private

    def find_listing
      listing = Spree::Listing.find_by(id: params[:id])
      raise ActiveRecord::RecordNotFound, "Listing not found" unless listing

      listing
    end

    def find_product
      product = Spree::Product.find_by(id: params[:listing][:product_id])
      raise ActiveRecord::RecordNotFound, "Product not found" unless product

      product
    end

    def action_type(params)
      submit_type = params.dig(:listing, :submit_type)

      case submit_type
      when 'save to draft', 'publish'
        'update'
      when 'revise', 'update'
        'revise'
      else
        raise StandardError, 'Submit type not defined'
      end
    end

    def get_amazon_product_type(taxon)
      if taxon
        taxon_parts = taxon.split('_')
        product_type = (taxon_parts - [taxon_parts.last]).join('_')
      else
        product_type = ''
      end
    end

  end
end