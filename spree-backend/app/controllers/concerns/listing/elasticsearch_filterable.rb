# frozen_string_literal: true

module Listing
  module ElasticsearchFilterable
    extend ActiveSupport::Concern

    # Method to apply Elasticsearch filtering logic to the collection
    def apply_elasticsearch_filtering

      # Assign the listing collection to `@collection` to avoid redundant method calls.
      @collection = collection

      # Check if Elasticsearch filtering should be applied based on the params
      return unless elasticsearch_query?

      # Setup the default order and adjust based on user input
      order = build_sort_order
      query_params = build_query_params

      @base_collection = scope.ransack(params[:filter]).result.order(start_time: :desc, id: :desc)

      # Initialize and apply the Elasticsearch query to the collection
      es_listing = AxelSpree::Elasticsearch::Listing.new
      es_listing.backend(query_params).order(order).mode(params[:mode] || "standard")

      # Apply the Elasticsearch query to the collection
      @collection.use_elasticsearch(es_listing, @base_collection)
    end

    private

    # Determines if Elasticsearch filtering should be applied based on params
    def elasticsearch_query?
      [:title_cont, :product_name_cont].any? { |key| params.dig(:filter, key).present? }
    end

    # Builds the sorting order for Elasticsearch queries
    def build_sort_order
      default_order = AxelSpree::Elasticsearch::Search::Order.new("updated_at", "asc", "_first").to_hash

      return default_order if params.dig(:filter, :s).blank?

      field, sort_direction = params.dig(:filter, :s).split(" ")
      AxelSpree::Elasticsearch::Search::Order.new(field, sort_direction, sort_direction == "desc" ? "_first" : "_last").to_hash


    end

    # Builds the query parameters for the Elasticsearch query
    def build_query_params
      return @query_params if @query_params.present?

      @query_params = {
        title: params.dig(:filter, :title_cont),
        product_name: params.dig(:filter, :product_name_cont),
        sale_channel_id: parse_sale_channel_ids,
        start_time_between: params.dig(:filter, :start_time_between),
        end_time_between: params.dig(:filter, :end_time_between),
        status: params.dig(:filter, :status_in).presence || [],
        deleted_at_null: parse_deleted_at_null,
        not_discontinued: parse_not_discontinued,
      }

      params[:filter].delete(:title_cont)
      params[:filter].delete(:product_name_cont)

      @query_params
    end

    # Parses sale_channel_id_in from params
    def parse_sale_channel_ids
      Array(params.dig(:filter, :sale_channel_id_in)).compact_blank
    end

    # Parses the deleted_at_null parameter from params
    def parse_deleted_at_null
      params.dig(:listing, :deleted_at_null) == "0" if params.dig(:filter, :deleted_at_null).present?
    end

    # Parses the not_discontinued parameter from params
    def parse_not_discontinued
      params.dig(:listing, :not_discontinued) != "1" if params.dig(:filter, :not_discontinued).present?
    end

    def valid_statuses
      Spree::Listing.statuses.keys - ["draft", "Ended", "Completed", "Inactive", "Incomplete", "Accepted"]
    end

    def update_filter_params
      params[:filter] ||= {}
      session[:listings_query] ||= {}

      params[:filter][:status_in] ||= valid_statuses
      params[:filter][:sale_channel_id_in] ||= []

      session[:listings_query].merge!(permitted_filter_params)
    end

    def convert_status(status)
      status == "draft" ? status : status.titleize
    end

    def permitted_filter_params
      params.require(:filter).permit(
        :s,
        :title_cont,
        :product_name_cont,
        :start_time_between,
        :end_time_between,
        sale_channel_id_in: [],
        status_in: [],
      )
    end
  end
end
