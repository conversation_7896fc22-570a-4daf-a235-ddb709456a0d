# frozen_string_literal: true

module Api
  module V3
    class ScanFormsController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = Spree::ScanForm.all.ransack(params[:filter]).result.page(params[:page]).per(per_page)

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render_serialized_payload { serialize_collection(collection) }
      end

      def create
        unless params[:scan_form]
          return render_error_payload('need provide parameter scan_form')
        end

        unless params[:scan_form][:stock_location_id]
          return render_error_payload('need provide parameter scan_form - stock_location id')
        end

        stock_location = ::Spree::StockLocation.find(params[:scan_form][:stock_location_id])
        unless stock_location
          return render_error_payload('please provide a valid stock_location id')
        end

        scan_form = Spree::ScanForm.new
        scan_form.store = current_store
        scan_form.stock_location = stock_location

        if scan_form.save
          render_serialized_payload { serialize_collection(scan_form) }
        else
          render_error_payload(scan_form.errors)
        end
      end

      protected

      def model_class
        Spree::ScanForm
      end

      def permitted_resource_params
        params.require(:scan_form).permit(
          :stock_location_id
        )
      end
    end
  end
end
