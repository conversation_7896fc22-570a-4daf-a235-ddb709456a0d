module Api
  module V3
    class TwoFactorAuthsController < BaseController
      include Api::V3::FetchUserTenants
      # before_action -> { doorkeeper_authorize! :read, :admin }, only: [:show, :disable]
      before_action :set_admin_user

      def show
        unless @admin_user.otp_secret
          @admin_user.otp_secret = Spree::AdminUser.generate_otp_secret
          @admin_user.save!
        end

        provisioning_uri = @admin_user.otp_provisioning_uri(@admin_user.email, issuer: 'Axel')
        qr_code = RQRCode::QRCode.new(provisioning_uri).as_png(size: 200)

        qr_code_data_uri = "data:image/png;base64,#{Base64.encode64(qr_code.to_blob).gsub("\n", '')}"

        render json: {
          qr_code_data_uri: qr_code_data_uri,
          secret_key: @admin_user.otp_secret
        }, status: :ok
      end

      def verify_code
        unless @admin_user.validate_and_consume_otp!(params[:otp_attempt])
          return render_error_payload('Invalid OTP.')
        end
        @admin_user.update!(otp_required_for_login: true) unless @admin_user.otp_required_for_login

        if params[:step] == "first"
          fetch_user_tenants(@admin_user.email)
        elsif params[:step] == "second"
          set_tenant
          token = create_token(user)
          tenant_url = generate_tenant_url(user.tenant_name)

          render json: {
            access_token: token.plaintext_token || token.token,
            tenant_url: tenant_url,
            token_type: "Bearer",
            expires_in: token.expires_in,
            refresh_token: token.plaintext_refresh_token || token.refresh_token,
            created_at: token.created_at.to_i
          }, status: :ok
        end
      end

      def disable
        @admin_user.disable_two_factor_authentication!
        render json: {
          message: "2FA disabled"
        }, status: :ok
      rescue StandardError => e
        render_error_payload("Failed to disable 2FA: #{e.message}", :internal_server_error)
      end

      private

      def create_token(admin_user)
        app = Spree::OauthApplication.find_or_create_by!(name: 'Admin Panel', scopes: 'admin', redirect_uri: '')
        token = Spree::OauthAccessToken.create!(
          resource_owner_id: admin_user.id,
          application_id: app.id,
          scopes: 'admin',
          resource_owner_type: "Spree::User",
          expires_in: Doorkeeper.configuration.access_token_expires_in.to_i,
          use_refresh_token: true
        )
        token
      end

      def generate_tenant_url(tenant)
        org = Spree::Organisation.find_by(subdomain: tenant)
        tenant_url = org.custom_domain.presence || org.url
        tenant_url
      end

      def normalize_email
        return render_error_payload('Please provide an email address', :bad_request) if params[:email].blank?

        params[:email].to_s.strip.downcase
      end

      def set_admin_user
        @admin_user = Spree::AdminUser.find_by(email: normalize_email)
      end

      def set_tenant
        workspace = params[:workspace_name]
        if Apartment.tenant_names.exclude?(workspace)
          raise SignInError, "Invalid workspace"
        end

        Apartment::Tenant.switch!(workspace)
      end

      def user
        Spree::User.find_by(email: normalize_email)
      end
    end
  end
end
