# frozen_string_literal: true

module Api
  module V3
    class ShipmentsController < ResourceController
      # include NumberResource
      SHIPMENT_STATES = ['ready', 'ship', 'cancel', 'resume', 'pend']
      before_action :load_variant, only: [:transfer_to_location, :transfer_to_shipment]
      before_action :update_order_package, only: [:update]

      def update_order_package
        if params[:id].present? && params.dig(:shipment, :tracking).present?
          order_package = ::Spree::Shipment.find_by(id: params[:id]).order_package
          order_package.update(tracking: params.dig(:shipment, :tracking))
          order_package.shipments.update(tracking: order_package.tracking)
        end
      end

      def update_selected_shiping_rate_cost
        raise StandardError, 'shipment not found' if resource.blank?

        cost = params.dig(:cost)
        raise StandardError, 'cost field is required' if cost.blank?

        order_package = resource.order_package
        raise StandardError, "order_package not found" if order_package.blank?

        selected_shipping_rate = order_package.tracking? ? order_package.selected_shipping_rate : order_package.shipments.first.selected_shipping_rate
        raise StandardError, "selected_shipping_rate not found" if selected_shipping_rate.blank?

        selected_shipping_rate.update(cost: cost)

        render(status: :ok)
      rescue StandardError => e
        render_error_payload(e.message)
      end

      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = []
        collection = if params[:filter] && params[:filter][:order_id_eq]
          Spree::Shipment.where(order_id: params[:filter][:order_id_eq])
        else
          Spree::Shipment.all
        end

        collection = if params[:filter] && params[:filter][:state_eq]
          collection.where(state: params[:filter][:state_eq]).page(params[:page]).per(per_page)
        else
          collection.page(params[:page]).per(per_page)
        end

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render_serialized_payload { serialize_collection(collection) }
      end

      def create
        result = Spree::Shipments::Create.call(
          store: current_store,
          shipment_attributes: params.require(:shipment).permit(
            :order_id, :stock_location_id, :variant_id, :quantity
          )
        )
        render_result(result, 201)
      end

      def update
        result = Spree::Shipments::Update.call(shipment: resource, shipment_attributes: permitted_resource_params)
        render_result(result)
      end

      def transfer_to_location
        stock_location = ::Spree::StockLocation.find(params.dig(:shipment, :stock_location_id))
        quantity = params.dig(:shipment, :quantity)&.to_i || 1

        if quantity <= 0
          message = "#{I18n.t("spree.api.shipment_transfer_errors_occurred")} \n #{I18n.t("spree.api.negative_quantity")}"
          render_error_payload(message)
          return
        end

        if params.dig(:shipment, :new_variant_id).present?
          new_variant = current_store.variants.find_by(id: params.dig(:shipment, :new_variant_id))
        end
        transfer = resource.transfer_to_location(@variant, quantity, stock_location, new_variant)
        if transfer.valid? && transfer.run!
          render(json: {message: I18n.t('spree.api.shipment_transfer_success')}, status: :created)
        else
          render_error_payload(transfer.errors)
        end
      end

      def transfer_to_shipment
        target_shipment = Spree::Shipment.find_by!(number: params.dig(:shipment, :target_shipment_number))
        quantity = params.dig(:shipment, :quantity)&.to_i || 1

        error =
          if quantity < 0 && target_shipment == resource
            "#{I18n.t("spree.api.negative_quantity")}, \n#{I18n.t("spree.api.wrong_shipment_target")}"
          elsif target_shipment == resource
            I18n.t('spree.api.wrong_shipment_target')
          elsif quantity < 0
            I18n.t('spree.api.negative_quantity')
          end

        if error
          render_error_payload("#{I18n.t("spree.api.shipment_transfer_errors_occurred")} \n#{error}")
        else
          transfer = resource.transfer_to_shipment(@variant, quantity, target_shipment)
          if transfer.valid? && transfer.run!
            render json: {message: I18n.t('spree.api.shipment_transfer_success')}, status: :created
          else
            render_error_payload(transfer.errors)
          end
        end
      end

      SHIPMENT_STATES.each do |state|
        define_method state do
          @ship_result = nil
          result = nil
          ::ActiveRecord::Base.transaction do
            if state == 'ship' && params[:shipment_ids].present?
              shipments = ::Spree::Shipment.where(id: params[:shipment_ids])
              order = shipments.take.order
              order_package = shipments.first.order_package || order.order_packages.create

              shipments.update_all(order_package_id: order_package.id) # rubocop:disable Rails/SkipsModelValidations
              unless non_easypost_shipment(shipments.first)
                order_package.refresh_rates(::Spree::ShippingMethod::DISPLAY_ON_BACK_END, {})
                selected_shipping_method = shipments.take.selected_shipping_rate.shipping_method

                order_package.shipping_rates.each do |rate|
                  if rate.shipping_method.id == selected_shipping_method.id
                    order_package.selected_shipping_rate = rate
                    order_package.save!
                  end
                end
                order_package.buy_easypost_rate
              end

              shipments.each do |shipment|
                shipment.buy_postage_shipment = true
                shipment.refresh_rates(::Spree::ShippingMethod::DISPLAY_ON_BACK_END, {})
                @ship_result = Spree::Shipments::ChangeState.call(
                  shipment: shipment,
                  state: state,
                  current_user: spree_current_user,
                  action_place: "/admin/orders/#{shipment.order_id}/shipments",
                  action: "#{state.titleize} Order",
                )

                raise ActiveRecord::Rollback unless @ship_result.success?
              end
              # rubocop:disable Rails/SkipsModelValidations

              if order_package.update_columns(state: 'shipped')
                shipments.each do |shipment|
                  shipment_order = shipment.order
                  store = shipment_order.store
                  shipment.stock_item_units
                    .select(
                      <<~SQL.squish
                        DISTINCT ON (vendor_inventory_number) *,
                        COUNT(*) OVER (PARTITION BY vendor_inventory_number) AS qty_count
                      SQL
                    )
                    .each do |stock_item_unit|
                      update_price_compare_inventory(stock_item_unit, shipment_order, store)
                    end
                end
              end

              order_package.shipments.update_all(tracking: order_package.tracking, tracking_label: order_package.tracking_label)

              ::Spree::ShipmentMailer.shipped_email(order_package.shipments.first.id).deliver_later

            # rubocop:enable Rails/SkipsModelValidations
            else
              result = Spree::Shipments::ChangeState.call(
                shipment: resource,
                state: state,
                current_user: spree_current_user,
                action_place: "/admin/orders/#{resource.order_id}/shipments",
                action: "#{state.titleize} Order",
              )
              raise ActiveRecord::Rollback unless result.success?
            end
          end

          render_result(@ship_result || result)
        end
      end

      def non_easypost_shipment(shipment)
        shipment.line_items.any? do |line_item|
          line_item.listing&.shipping_category&.shipping_methods&.any?(&:local_pickup)
        end || shipment.line_items.any? do |line_item|
                 line_item.listing&.shipping_category&.name == 'Free Shipping'
               end
      end

      def shipping_rates
        unless params[:shipment_ids]
          return render_error_payload('need provide parameter shipment_ids')
        end

        shipment = resource
        order = shipment.order
        shipment_ids = params[:shipment_ids]

        if shipment_ids
          shipments = ::Spree::Shipment.where(id: shipment_ids)

          order_package = shipments.first.order_package
          order_package = new_order_package = order.order_packages.create! if order_package.nil?
          # rubocop:disable Rails/SkipsModelValidations
          shipments.update_all(order_package_id: order_package.id)
          # rubocop:enable Rails/SkipsModelValidations

          details = "OrderPackage created with number - #{order_package.number}
                    with associated shipmnet ids - #{shipment_ids}"
          order.tracker_logs.create(details: details)
        end

        obj = shipment_ids.present? ? order_package : shipment
        obj.refresh_rates(::Spree::ShippingMethod::DISPLAY_ON_BACK_END, {
          weight: params[:weight],
          height: params[:height],
          length: params[:length],
          width: params[:width],
          weight_unit: params[:weight_unit],
          dimension_unit: params[:dimension_unit],
          buy_postage: params[:buy_postage]
        })

        render_serialized_payload do
          if shipment_ids.present?
            serialize_order_package(obj.reload.shipping_rates.where('cost > ?', 0), new_order_package)
          else
            serialize_shipping_rates(obj.reload.shipping_rates.where('cost > ?', 0))
          end
        end
      end

      def serialize_shipping_rates(shipping_rates)
        ShippingRateSerializer.render_as_hash(shipping_rates)
      end

      def serialize_order_package(shipping_rates, order_package = nil)
        ::Spree::V2::Storefront::OrderPackageSerializer.new(
          shipping_rates, params: {order_package_id: order_package&.id}
        ).serializable_hash
      end

      def buy_postage
        unless params[:shipment_ids]
          return render_error_payload('need provide parameter shipment_ids')
        end

        unless params[:selected_shipping_rate_id]
          return render_error_payload('need provide parameter selected_shipping_rate_id')
        end

        ::ActiveRecord::Base.transaction do
          shipment = ::Spree::Shipment.find_by(id: params[:shipment_ids])
          resource = shipment.order_package

          return render_error_payload('order package is nil, need call shipping_rates correctly first') unless resource

          create_ordered_listing_info(params[:shipment_ids])

          @updated_weight = {
            weight: params['weight'],
            width: params['width'],
            length: params['length'],
            height: params['height']
          }
          if params[:selected_shipping_rate_id].present? || params[:buy_postage] == 'walmart'
            postage_via = params[:buy_postage] == 'walmart' ? 'Walmart BuyPostage' : 'BuyPostage'
            details = "#{postage_via} clicked for order - #{resource.order.number} and order_package - #{resource.number}"
            resource.order.tracker_logs.create(details: details)
            insure = false
            insure = true if (params[:insure] || params['insure'] == "true") || (params[:insure] || params['insure'] == true)
            insure_for_amount = insure ? (params[:insure_for_amount] || params['insure_for_amount']) : nil

            # rubocop:disable Rails/SkipsModelValidations
            resource.shipping_rates.update_all(selected: false)
            # rubocop:enable Rails/SkipsModelValidations
            resource.shipping_rates.update(params[:selected_shipping_rate_id], selected: true)
            if resource.class.name == 'Spree::OrderPackage'
              resource.update({
                selected_shipping_rate_id: params[:selected_shipping_rate_id],
                insure: insure,
                insure_for_amount: insure_for_amount
              })
            else
              resource.shipments.update({
                selected_shipping_rate_id: params[:selected_shipping_rate_id],
                buyer_paid_amount: resource.cost,
                email_template_id: params[:shipment_email_id],
                custom_message: params[:custom_message],
                insure: insure,
                insure_for_amount: insure_for_amount
              })
            end

            resource.reload
            resource.update_amounts
            details = "Successfully updated the shipping cost (#{resource.selected_shipping_rate.cost}) " \
              "to the shipment corresponding to order package ##{resource.number} " \
              'using the resource.update_amounts method and updated shipment cost is ' \
              "#{resource.shipments.first.cost}."

            resource.order.tracker_logs.create(details: details)
            resource.reload
          end

          begin
            # find_and_update_shipment

            params[:shipment] ||= {}
            unless resource.tracking_label?
              if params[:buy_postage] == 'walmart'
                @parcel = {
                  weight: params['weight'],
                  width: params['width'],
                  length: params['length'],
                  height: params['height'],
                  carrier: params['carrier'],
                  carrier_service_type: params['carrier_service_type'],
                  dimension_unit: params[:dimension_unit],
                  weight_unit: params[:weight_unit]
                }
                resource.walmart_create_label(@parcel)
              else
                resource.buy_easypost_rate(@updated_weight)
              end
              resource.state = 'shipped'

              if resource.save!
                resource.shipments.each do |shipment|
                  shipment_order = shipment.order
                  store = shipment_order.store
                  shipment.stock_item_units
                    .select(
                      <<~SQL.squish
                        DISTINCT ON (vendor_inventory_number) *,
                        COUNT(*) OVER (PARTITION BY vendor_inventory_number) AS qty_count
                      SQL
                    )
                    .each do |stock_item_unit|
                      update_price_compare_inventory(stock_item_unit, shipment_order, store)
                    end
                end
              end

              update_associated_shipments(resource)

              ::Spree::ShipmentMailer.shipped_email(resource.shipments.first.id).deliver_later
              unless resource.state == 'shipped'
                # This attribute accessor is to skip "before_transition" state machine.
                resource.buy_postage_shipment = true
                Rails.logger.info("Resource state second reload and ship: #{resource.inspect}")
                resource.update(state: 'shipped')
              end
            end

            render(json: {
              message: 'buy_postage success',
              package_id: resource&.id,
              package_number: resource&.number,
              tracking: resource&.tracking,
              tracking_label: resource&.tracking_label,
              state: resource&.state,
              selected_shipping_rate_id: resource&.selected_shipping_rate_id,
              insure: resource&.insure,
              insure_for_amount: resource&.insure_for_amount,
              cost_to_insure: resource&.cost_to_insure,
              shipping_source: resource&.shipping_source
            })
            # respond_with(resource.reload, default_template: :show)
            # render_serialized_payload { serialize_resource(resource.reload) }
          rescue EasyPost::Errors => e
            resource.order.tracker_logs.create(details: "Encountered error while BuyPostage process.
                                                        Error message - #{e.message}")
            render(json: {error: e.message}, status: :bad_request)
          rescue => e
            resource.order.tracker_logs.create(details: "Encountered error while BuyPostage process.
                                                        Error message for #{resource.number} is - #{e.message}")
            render(json: {error: e.message}, status: :bad_request)
          end
        end
      end

      def delete_shipping_label
        shipment = ::Spree::Shipment.find_by(id: params[:id])
        unless shipment
          render(json: {error: 'Shipment not found'}) and return
        end

        order_package = shipment.order_package
        shipment_rate = order_package&.selected_shipping_rate

        begin
          if order_package.shipping_source == 'walmart shipping'
            order_package.discard_postage_label
          else
            api_key = shipment.order.store.easypost_setting.key
            client =  EasyPost::Client.new(api_key: api_key)
            carrier = order_package&.selected_shipping_rate&.name
            tracking_code = order_package.tracking

            client.refund.create(carrier: carrier, tracking_codes: [tracking_code])
          end
          # Create a refund for purchased shipment on easypost
          order_package.shipments.update(
            tracking: nil,
            tracking_label: nil,
            cost: 0.0,
            buyer_paid_amount: nil,
            state: 'ready',
            email_template_id: nil,
            custom_message: ''
          )

          details = 'Successfully updated the shipping cost (0) ' \
            "to the shipment corresponding to order package ##{resource.number} " \
            'using the delete_shipping_label method and updated shipment cost is ' \
            "#{order_package.shipments.first.cost}."

          resource.order.tracker_logs.create(details: details)

          shipment_rate.update(selected: false)
          order_package.update(tracking: nil, tracking_label: nil, state: 'ready')
          revise_stock_item_unit_state(order_package)
          render(json: {message: 'success'})
        rescue StandardError => e
          render(json: {error: e.message})
        end
      end

      def add_item
        quantity = update_line_item_quantity(params.dig(:shipment, :line_item_id), params.dig(:shipment, :quantity))
        result = Spree::Shipments::AddItem.call(
          shipment: resource,
          variant_id: params.dig(:shipment, :variant_id),
          quantity: quantity
        )
        render_result(result)
      end

      def remove_item
        quantity = update_line_item_quantity(params.dig(:shipment, :line_item_id), params.dig(:shipment, :quantity))
        result = Spree::Shipments::RemoveItem.call(
          shipment: resource,
          variant_id: params.dig(:shipment, :variant_id),
          quantity: quantity
        )

        if result.success?
          if result.value == :shipment_deleted
            head :no_content
          else
            render_serialized_payload { serialize_resource(result.value) }
          end
        else
          render_error_payload(result.error)
        end
      end

      private

      def update_line_item_quantity(line_item_id, params_quantity)
        line_item = ::Spree::LineItem.find_by(id: line_item_id)
        # line_item.present? && line_item.storefront_sale_channel? ? (params_quantity * line_item.product.listing_pack_size) : params_quantity
        line_item.present? && line_item.storefront_sale_channel? ? (params_quantity * line_item.pack_size) : params_quantity
      end

      def update_price_compare_inventory(stock_item_unit, order, store)
        vin = stock_item_unit.vendor_inventory_number

        # return if vin.blank?

        ::Admin::UpdateInventoryCommand.call(
          vin: vin,
          qty: stock_item_unit.qty_count,
          order: order,
          store: store
        )
      end

      def update_associated_shipments(resource)
        resource.shipments.each do |shipment|
          # This attribute accessor is to skip "before_transition" state machine.
          shipment.buy_postage_shipment = true
          shipment.assign_attributes(
            tracking: resource.tracking,
            tracking_label: resource.tracking_label,
            custom_message: params[:custom_message],
            email_template_id: params[:shipment_email_id],
            cost: resource.selected_shipping_rate.cost
          )
          shipment.current_user = spree_current_user
          shipment.action_place = "/admin/orders/#{shipment.order_id}/shipments"
          shipment.action = 'Buy Postage Order'
          shipment.save!
          shipment.reload.ship!
        end

        details = "Successfully updated the shipping cost (#{resource.selected_shipping_rate.cost}) " \
          "to the shipment corresponding to order package ##{resource.number} " \
          'using the update_associated_shipments method and updated shipment cost is ' \
          "#{resource.shipments.first.cost}."

        resource.order.tracker_logs.create(details: details)

        if resource.order.shipments.all? { |shipment| shipment.state == 'shipped' }
          resource.order.update(shipment_state: 'shipped')
        elsif resource.order.shipments.any? { |shipment| shipment.state == 'shipped' }
          resource.order.update(shipment_state: 'partial')
        end
      end

      def model_class
        Spree::Shipment
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def load_variant
        @variant = current_store.variants.find_by(id: params.dig(:shipment, :variant_id))
      end

      def spree_permitted_attributes
        Spree::Shipment.json_api_permitted_attributes + [
          :selected_shipping_rate_id
        ]
      end

      def revise_stock_item_unit_state(order_package)
        order_package.shipments.each do |shipment|
          shipment.stock_item_units.each do |siu|
            siu.update!(state: :locked)
            record_activity_log(siu, shipment)
          end
          shipment.inventory_units.update(state: 'on_hand')
        end
        order_package.order.update(shipment_state: 'ready')
      end

      def record_activity_log(stock_item_unit, shipment)
        order = shipment.order

        ::Spree::ActivityLog.create!(
          loggable: stock_item_unit,
          user_id: spree_current_user.id,
          action_name: order.number,
          role: spree_current_user.spree_roles.first&.name,
          date: Time.current,
          email: spree_current_user.email,
          action_place: "/admin/orders/#{order.id}/shipments",
          action: ::Spree::ActivityLogActions.get(:stock_item_unit, :cancel_label),
          product_id: stock_item_unit.stock_item&.product&.id
        )
      end

      def create_ordered_listing_info(shipment)
        shipments = ::Spree::Shipment.where(id: params[:shipment_ids])
        accumulated_data = {}

        shipments.each do |shipment|
          shipment.line_items.each do |line_item|
            accumulated_data[line_item.listing_id] ||= 0
            accumulated_data[line_item.listing_id] += line_item.quantity
          end
        end

        return if accumulated_data.empty? || accumulated_data[nil].present?

        all_ordered_listings_data = accumulated_data.map do |listing_id, total_quantity|
          {listing_id: listing_id, quantity: total_quantity}
        end

        all_ordered_listings_data.sort_by! { |item| item[:listing_id] }
        ordered_listings_json = all_ordered_listings_data.to_json
        ordered_listing_info = ::Spree::OrderedListingsInfo.find_or_initialize_by(listing_quantities: ordered_listings_json)

        ordered_listing_info.assign_attributes(
          weight: params['weight'],
          length: params['length'],
          width: params['width'],
          height: params['height'],
          shipping_method_id: ::Spree::ShippingRate.find(params[:selected_shipping_rate_id]).shipping_method_id
        )

        ordered_listing_info.save!
      end
    end
  end
end
