# frozen_string_literal: true

module Api
  module V3
    class StockTransfersController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      def create
        variant_ids = permitted_resource_params[:variant]

        if any_missing_variants?(variant_ids)
          render_error_payload('Variant missing')
        else
          variants = Hash.new(0)
          variant_ids.each_with_index do |variant_id, i|
            variants[variant_id] += permitted_resource_params[:quantity][i].to_i
          end

          command = ::Admin::TransferStockCommand.call(
            store: current_store,
            source_location: source_location,
            destination_location: destination_location,
            reference: params[:reference],
            variants: variants
          )
          stock_transfer = command.result

          render_serialized_payload(:created) { serialize_resource(stock_transfer) }
        end
      end

      protected

      def model_class
        Spree::StockTransfer
      end

      def scope_includes
        []
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def source_location
        @source_location ||= Spree::StockLocation.find_by(id: permitted_resource_params[:source_stock_id])
      end

      def destination_location
        @destination_location ||= Spree::StockLocation.find(permitted_resource_params[:destination_stock_id])
      end

      def any_missing_variants?(variant_ids)
        source_location&.stock_items&.where(variant_id: variant_ids, count_on_hand: 0)&.any?
      end

      def permitted_resource_params
        params.require(:stock_transfer).permit(:reference, :source_stock_id, :destination_stock_id,
          variant: [], quantity: [])
      end
    end
  end
end
