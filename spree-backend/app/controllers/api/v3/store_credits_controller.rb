# frozen_string_literal: true

module Api
  module V3
    class StoreCreditsController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      protected

      def model_class
        Spree::StoreCredit
      end

      def scope_includes
        [:created_by, :category]
      end

      def permitted_resource_params
        params.require(:store_credit).permit(:user_id, :category_id, :currency, :amount, :memo)
          .tap { |p| p[:created_by_id] = current_user.id }
      end
    end
  end
end
