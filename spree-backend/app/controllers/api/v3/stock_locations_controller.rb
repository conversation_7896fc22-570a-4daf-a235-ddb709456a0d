# frozen_string_literal: true

module Api
  module V3
    class StockLocationsController < ResourceController
      protected

      def parent_scope
        Spree::StockLocation.for_store(current_store).find(params[:stock_id]).sections
      end

      def scope
        parent_scope
      end

      def model_class
        ::StockLocationSection
      end

      def allowed_sort_attributes
        [:name]
      end

      def permitted_resource_params
        params.require(:stock_location).permit(:name, :description)
          .tap { |p|
            p[:stock_location_id] = params[:stock_id]
          }
      end
    end
  end
end
