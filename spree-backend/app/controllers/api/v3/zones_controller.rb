# frozen_string_literal: true

module Api
  module V3
    class ZonesController < ResourceController
      protected

      def model_class
        Spree::Zone
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def permitted_resource_params
        params.require(:zone).permit(
          :name,
          :description,
          :default_tax,
          :kind,
          country_ids: [],
          state_ids: []
        )
      end
    end
  end
end
