# frozen_string_literal: true

module Api
  module V3
    class SaleChannelsController < ResourceController
      skip_before_action :validate_token_client, :authorize_read_admin_scope, only: [:switch_store]
      before_action :find_sale_channel, only: [:disconnect]

      def available_channels
        brands = %w[amazon ebay walmart]

        available_brands = brands.index_with do |brand|
          parent_scope.for_brand(brand).any?
        end.transform_keys(&:to_sym)

        render json: available_brands, status: :ok
      end

      def brand_channels
        brand = params[:brand]&.downcase
        render_error_payload('Brand is required') and return if brand.blank?

        collection = scope.for_brand(brand)

        amazon_oauth_application_id = nil
        amazon_show_rotate_button = false

        if brand == 'amazon' && collection.any?
          amazon_oauth_app = collection.first.oauth_application

          if amazon_oauth_app
            amazon_credentials = Spree::AmazonLwaCredentials.first
            rotate_lwa_needed = amazon_credentials&.expiry_date && amazon_credentials.expiry_date < 7.days.from_now

            amazon_oauth_application_id = amazon_oauth_app.id
            amazon_show_rotate_button = rotate_lwa_needed
          end
        end

        response_payload = {
          sale_channels: SaleChannelSerializer.render_as_hash(collection),
          amazon_oauth_application_id: amazon_oauth_application_id,
          amazon_show_rotate_button: amazon_show_rotate_button
        }

        render json: response_payload, status: :ok
      end

      def create
        sale_channel = Spree::SaleChannel.new(permitted_resource_params)

        if sale_channel.save
          result =  case sale_channel.brand
                    when 'ebay'
                      {
                        redirect_url: Ebay::Oauth::Authorization.auth_redirect_url(
                          channel_name, params[:country], sale_channel.id, current_tenant
                        )
                      }
                    when 'amazon'
                      {
                        redirect_url: Amazon::Oauth::Authorization.auth_redirect_url(
                          channel_name, params[:country], sale_channel.id, current_tenant
                        )
                      }
                    when 'walmart'
                      oauth_result = process_walmart_oauth(sale_channel, params)
                      if oauth_result[:error].present?
                        {
                          redirect_url: "https://#{current_store.url}/admin/channel-connect-success?error_message=#{URI.encode_www_form_component(oauth_result[:error])}"
                        }
                      else
                        {
                          redirect_url: "https://#{current_store.url}/admin/channel-connect-success?success_message=#{URI.encode_www_form_component(oauth_result[:message])}"
                        }
                      end
                    end

          render json: result, status: :ok
        else
          render_error_payload(sale_channel.errors)
        end
      end

      def update
        sale_channel = model_class.find_by(id: params[:id])
        return render_error_payload('Provide valid sale channel id', :not_found) if sale_channel.nil?

        oauth_application = sale_channel.oauth_application
        return render_error_payload('OAuth application not found for given sale channel', :not_found) if oauth_application.nil?

        oauth_name = params.dig(:sale_channel, :name)
        return render_error_payload('Please provide a valid oauth name') if oauth_name.blank?

        if oauth_application.update(name: oauth_name)
          render_serialized_payload { serialize_resource(sale_channel) }
        else
          render_error_payload(oauth_application.errors.full_messages)
        end
      end

      def fetch_walmart_package_type
        oauth_application = @resource.oauth_application
        package_types = ::Walmart::ShippingApis::PackageType.new(oauth_application.store.id, oauth_application.id).call
        render json: { message: 'success', data: package_types }
      rescue StandardError => e
        render_error_payload(e)
      end

      def available_countries
        brand = params[:brand]&.downcase
        render_error_payload('Brand is required') and return if brand.blank?

        countries = case brand
                    when 'ebay' then Spree::SaleChannel::EBAY_COUNTRY
                    when 'walmart' then Spree::SaleChannel::WALMART_COUNTRY
                    when 'amazon' then Spree::SaleChannel::AMAZON_COUNTRY
                    end

        mapped_countries = countries.map do |country_name, (_code, value)|
          {
            name: country_name.to_s.tr('_', ' '),
            value: value
          }
        end

        render json: { countries: mapped_countries }, status: :ok
      end

      def renew_token
        url = Ebay::Oauth::Authorization.auth_redirect_url(
          channel_name,
          params[:country_code],
          params[:sale_channel_id],
          current_tenant
        )

        render json: { redirect_url: url }, status: :ok
      end

      def rotate_lwa_credentials
        if Amazon::Oauth::RotateLwaCredentials.new(current_store.id, params[:amazon_oauth_application_id]).rotate_credentials
          render json: { message: 'Amazon LWA Credentials updated successfully' }, status: :ok
        else
          render_error_payload('Failed to update LWA Credentials')
        end
      rescue => e
        Rails.logger.error("<=============>\nFailed to update LWA Credentials: #{e.message}<=============>\n")
        render_error_payload('Failed to update LWA Credentials')
      end

      def switch_store
        subdomain = params[:state].split.fourth
        if Apartment.tenant_names.include?(subdomain)
          Apartment::Tenant.switch!(subdomain)
        else
          Apartment::Tenant.switch!('public')
        end

        params[:store_id] = current_store.id

        oauth_result = auth_code

        if oauth_result[:error].present?
          redirect_to("https://#{current_store.url}/admin/channel-connect-success?error_message=#{URI.encode_www_form_component(oauth_result[:error])}", allow_other_hosts: true)
        else
          redirect_to("https://#{current_store.url}/admin/channel-connect-success?success_message=#{URI.encode_www_form_component(oauth_result[:message])}", allow_other_hosts: true)
        end
      rescue => e
        Rails.logger.error("<=============>\nFailed to add sale channel: #{e.message}<=============>\n")
        message = URI.encode_www_form_component('Could not connect, Try Again!')
        redirect_to("https://#{current_store.url}/admin/channel-connect-success?error_message=#{message}", allow_other_hosts: true)
      end

      def disconnect
        if @sale_channel.destroy
          render json: { message: 'Successfully disconnected sale channel.' }, status: :ok
        else
          render_error_payload(@sale_channel.errors)
        end
      end

      def register_brand_account
        brand = params[:brand]&.downcase
        render_error_payload('Brand is required') and return if brand.blank?

        url = case brand
              when 'ebay' then "https://www.ebay.com/signin/"
              when 'walmart' then "https://seller.walmart.com/signup"
              when 'amazon' then "https://sellercentral.amazon.com/signin/"
              end

        render json: { register_url: url }, status: :ok
      end

      private

      def channel_name
        params[:name].to_s.strip.gsub(/\s+/, '_')
      end

      def process_walmart_oauth(sale_channel, params)
        response = Walmart::Oauth::Authorization.new(
          'Walmart Marketplace',
          set_marketplace_id(params[:country]),
          sale_channel.id,
          current_tenant,
          params[:client_id],
          params[:client_secret]
        ).process_request

        return { error: "Could not connect, Try Again!" } if response['error'].present?

        app = Spree::OauthApplication.find_or_create_by(
          name: channel_name,
          sale_channel_id: sale_channel.id,
          store_id: current_store.id,
          site_id: params[:country].to_i
        )

        app.update!(uid: params[:client_id], secret: params[:client_secret])
        app.access_tokens.create!(
          expires_in: response['expires_in'],
          token: response['access_token'],
          refresh_token: response['refresh_token'],
          expire_date: Time.current + response['expires_in'].seconds
        )

        { message: 'Successfully Connected' }
      end

      def auth_code
        parameter = params[:state].to_s.split

        if params[:code].present?
          handle_ebay_auth(parameter)
        elsif params[:spapi_oauth_code].present?
          handle_amazon_auth(parameter)
        else
          { error: "Could not connect, Try Again!" }
        end
      end

      def handle_ebay_auth(parameter)
        response = Ebay::Oauth::Authorization.new(params[:code]).process_request

        if response && response['access_token']
          application = get_oauth_application(parameter, 2.hours.from_now, params, response)
          user_info = Ebay::IdentityApis::GetUser.new(params[:store_id], application.id).call
          application.update(seller_name: user_info.with_indifferent_access.dig(:username)) if user_info

          { message: "Successfully Connected" }
        else
          { error: response['error_description'] || "eBay token exchange failed" }
        end
      end

      def handle_amazon_auth(parameter)
        response = Amazon::Oauth::Authorization.new(params[:spapi_oauth_code]).process_request

        if response && response['access_token']
          get_oauth_application(parameter, 1.hour.from_now, params, response)
          { message: "Successfully Connected" }
        else
          { error: "Could not connect, Try Again!" }
        end
      end

      def get_oauth_application(parameter, expire_date, params, response)
        application = Spree::OauthApplication.find_or_initialize_by(
          sale_channel_id: parameter[2],
          store_id: params[:store_id]
        )

        if application.persisted?
          application.access_tokens.first&.update(
            expires_in: response['expires_in'],
            token: response['access_token'],
            refresh_token: response['refresh_token'],
            expire_date: expire_date
          )
          application.update(re_authenticate: false)
        else
          application.assign_attributes(
            name: parameter[0],
            site_id: parameter[1],
            seller_name: params[:selling_partner_id]
          )
          application.save!
          application.access_tokens.create!(
            expires_in: response['expires_in'],
            token: response['access_token'],
            refresh_token: response['refresh_token'],
            expire_date: expire_date
          )
        end

        application
      end

      def authorize_spree_user
        case action_name
        when 'available_channels' then true
        when 'fetch_walmart_package_type' then true
        when 'available_countries' then true
        when 'brand_channels' then true
        when 'renew_token' then true
        when 'rotate_lwa_credentials' then true
        when 'switch_store' then true
        when 'disconnect' then true
        when 'register_brand_account' then true
        else
          super
        end
      end

      def model_class
        Spree::SaleChannel
      end

      def collection
        @collection ||= scope.joins(:oauth_application).ransack(params[:filter]).result
      end

      def scope_includes
        [:oauth_application]
      end

      def allowed_sort_attributes
        []
      end

      def permitted_resource_params
        params.require(:sale_channel).permit(:brand).tap do |permitted|
          permitted[:store_id] = current_store.id
          permitted[:description] = 'Selling Everything to millions of shoppers'
        end
      end

      def set_marketplace_id(_country_id)
        Spree::SaleChannel::WALMART_COUNTRY.each_value do |value|
          return value.first if value.include?(params[:country])
        end
      end

      def current_tenant
        Apartment::Tenant.current
      end

      def find_sale_channel
        @sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
      end
    end
  end
end
