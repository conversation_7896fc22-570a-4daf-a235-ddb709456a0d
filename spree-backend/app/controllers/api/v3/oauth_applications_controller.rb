# frozen_string_literal: true

module Api
  module V3
    class OauthApplicationsController < BaseController
      def register
        tenant_name = Apartment::Tenant.current
        result = Spree::OauthApplication.find_or_create_by!(name: 'Admin Panel', scopes: 'admin', redirect_uri: '')
        if result
          render(json: {message: 'success', tenant: tenant_name, client_id: result.uid, client_secret: result.secret})
        else
          render_error_payload('Authorization Register Fail')
        end
      end
    end
  end
end
