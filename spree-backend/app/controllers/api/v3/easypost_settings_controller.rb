# frozen_string_literal: true

module Api
  module V3
    class EasypostSettingsController < ResourceController
      protected

      def model_class
        Spree::EasypostSetting
      end

      def resource
        @resource ||= Spree::EasypostSetting.find_by(id: params[:id]) # current_store.easypost_setting || current_store.build_easypost_setting
      end

      def permitted_resource_params
        params.require(:easypost_settings).permit(:buy_postage_when_shipped, :validate_address,
          :use_easypost_on_frontend, :customs_signer, :customs_contents_type, :customs_eel_pfc,
          :carrier_accounts_shipping, :carrier_accounts_returns, :endorsement_type, :key,
          :returns_stock_location_id, :name)
      end
    end
  end
end
