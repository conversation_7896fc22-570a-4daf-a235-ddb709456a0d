# frozen_string_literal: true

module Api
  module V3
    class ImageUploadController < ResourceController
      def upload
        set_resource
        payload = request.parameters.except(:controller, :action)
        payload = payload.merge(type: 'close_modal')

        QrCodeChannel.broadcast_to(broadcasting, payload)
      end

      def successful_upload
        unless params[:resource_id]
          return render_error_payload('need provide parameter resource_id')
        end

        type_array = ['product', 'listing', 'order']
        unless params[:resource] && type_array.include?(params[:resource])
          return render_error_payload('need provide parameter resource - order or product or listing')
        end

        set_resource
        if params[:variant_id].present?
          variant = Spree::Variant.find_by(id: params[:variant_id])
          option_text = variant&.options_text.presence || 'master'
          custom_filename = "#{option_text}_#{variant&.id}" || ''
        end
        payload = request.parameters.except(:controller, :action)
        payload = payload.merge(type: 'refresh')

        @images = []

        params.each do |param_name, file|
          next unless param_name.start_with?('file_') && file.respond_to?(:tempfile)

          filename = params[:variant_id].present? ? custom_filename + '_' + file.original_filename : file.original_filename
          @images << {io: file, filename: filename}
        end

        token = params[:user_token]
        user = Spree::User.find_by(user_token: token)

        case @resource.class.name
        when 'Spree::Product'
          @images.each do |image_attributes|
            image = @resource.images.create!(attachment: image_attributes)
            Spree::ImageUpload.create!(imageable: @resource, image_id: image.attachment.id, user: user) if user.present?
            @resource.update(uploaded_by: user&.email) if user&.present?
          end unless @images.empty?
        when 'Spree::Listing'
          unless @images.empty?
            @resource.image_files.attach(@images)
            image_attachment = @resource.image_files.last
            Spree::ImageUpload.create!(imageable: @resource, image_id: image_attachment.id, user: user) if user.present?
            @resource.update(uploaded_by: user&.email) if user&.present?
          end
        when 'Spree::Order'
          @images.each do |image|
            @resource.images.attach(image)
            attachment = @resource.images.attachments.last
            Spree::ImageUpload.create!(imageable: @resource, image_id: attachment.id, user: user) if user.present?
            @resource.update(uploaded_by: user&.email) if user&.present?
          end unless @images.empty?
        end

        if @images.present?
          QrCodeChannel.broadcast_to(broadcasting, payload)
          render(json: @images, status: :created)
        else
          render(json: {errors: ['No valid files were provided.']}, status: :unprocessable_entity)
        end
      end

      def remove_image
        unless params[:resource_id]
          return render_error_payload('need provide parameter resource_id')
        end

        type_array = ['product', 'listing', 'order']
        unless params[:resource] && type_array.include?(params[:resource])
          return render_error_payload('need provide parameter resource - order or product or listing')
        end

        unless params[:image_id]
          return render_error_payload('need provide parameter image_id')
        end

        set_resource
        if params[:resource] == 'listing'
          image = @resource.image_files.find_by(id: params[:image_id])
        else
          image = @resource.images.find_by(id: params[:image_id])
        end

        if image.nil?
          return render_error_payload('Image not found', status: :not_found)
        end

        begin
          if image.is_a?(ActiveStorage::Attachment)
            image.purge
          elsif image.is_a?(Spree::Image)
            image.attachment.purge if image.respond_to?(:attachment) && image.attachment.attached?
            image.destroy
          else
            return render_error_payload("Unsupported image type: #{image.class.name}")
          end

          render(json: { message: 'Image deleted successfully' }, status: :ok)
        rescue => e
          Rails.logger.error "Failed to delete image: #{e.message}\n#{e.backtrace.join("\n")}"
          return render_error_payload("Failed to delete image: #{e.message}", status: :unprocessable_entity)
        end
      end

      private

      def authorize_spree_user
        case action_name
        when 'remove_image'
          true
        when 'successful_upload'
          true
        else
          super
        end
      end

      def broadcasting
        @broadcasting ||= authorize_spree_user
      end

      def set_resource
        resource_class = case params[:resource]
        when 'product'
          Spree::Product
        when 'listing'
          Spree::Listing
        when 'order'
          Spree::Order
        end
        @resource = resource_class.find(params[:resource_id])
      end
    end
  end
end
