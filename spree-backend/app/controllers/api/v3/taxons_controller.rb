# frozen_string_literal: true

module Api
  module V3
    class TaxonsController < ResourceController
      include ::Spree::Api::V2::Platform::NestedSetRepositionConcern

      before_action -> { params[:page] ||= 1 }, only: :index

      def reposition
        spree_authorize! :update, resource if spree_current_user.present?

        new_parent = scope.find(reposition_params[:new_parent_id])

        if resource.move_to_child_with_index(new_parent, reposition_params[:new_position_idx].to_i)
          reload_taxon_and_set_new_permalink(resource)
          update_permalinks_on_child_taxons

          render_serialized_payload { serialize_resource(resource) }
        elsif resource.errors.any?
          # If there are errors output them to the response
          render_error_payload(resource.errors.full_messages.to_sentence)
        else
          # If the user drops the re-positioned item in the same location it came from
          # we just render the serialized payload, nothing has changed, we don't need to
          # render any errors, or fire any custom success methods.
          render_serialized_payload { serialize_resource(resource) }
        end
      end

      private

      def reload_taxon_and_set_new_permalink(taxon)
        taxon.reload
        taxon.set_permalink
        taxon.save!
      end

      def update_permalinks_on_child_taxons
        resource.descendants.each do |taxon|
          reload_taxon_and_set_new_permalink(taxon)
        end
      end

      def model_class
        Spree::Taxon
      end

      def scope_includes
        [:translations, :icon]
      end

      def serializer_params
        super.tap { |params| params[:view] = :tree if action_name == 'show' }
      end

      def permitted_resource_params
        params.require(:taxon).permit(
          :name, :pretty_name, :permalink, :description,
          :seo_title, :meta_title, :meta_description, :meta_keywords,
          :position, :hide_from_nav, :parent_id, :taxonomy_id, :category_id, :exclude_from_data_feed
        )
      end

      def reposition_params
        params.require(:taxon).permit(:new_parent_id, :new_position_idx)
      end
    end
  end
end
