# frozen_string_literal: true

module Api
  module V3
    class WalmartController < ResourceController
      def resource
        nil
      end

      def get_walmart_product_id
        result = Walmart::ItemService.new(sale_channel_id).get_product_id_via_item_id(params.dig(:item_id))
        if result.success?
          render json: result.content, status: :ok
        else
          render json: {message: result.error_message}, status: result.status
        end
      end

      def search_walmart_catalog
        result = Walmart::ItemService.new(sale_channel_id).search_walmart_catalog(params.dig(:query))
        if result.success?
          item_ids = result.content.pluck('itemId')
          item_ids_in_catalog = Spree::WalmartListingSku.where(sale_channel_id: sale_channel_id, item_id: item_ids).pluck(:item_id)
          items = result.content.map do |item|
            item['in_seller_catalog'] = item_ids_in_catalog.include?(item['itemId'])
            item['sale_channel_id'] = sale_channel_id
            item.with_indifferent_access
          end
          render json: WalmartCatalogItemSerializer.render(items), status: :ok
        else
          render json: {message: result.error_message}, status: result.status
        end
      end

      private

      def sale_channel_id
        params.dig(:sale_channel_id)
      end
    end
  end
end
