# frozen_string_literal: true

module Api
  module V3
    class PrototypesController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      protected

      def model_class
        Spree::Prototype
      end

      def parent_scope
        model_class
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def permitted_resource_params
        params.require(:prototype).permit(
          :name,
          property_ids: [],
          option_type_ids: [],
          taxon_ids: []
        )
      end
    end
  end
end
