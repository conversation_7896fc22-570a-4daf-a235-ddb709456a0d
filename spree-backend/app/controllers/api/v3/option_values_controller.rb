# frozen_string_literal: true

module Api
  module V3
    class OptionValuesController < ResourceController
      private

      def model_class
        Spree::OptionValue
      end

      def parent_scope
        Spree::OptionType.for_store(current_store).find(params[:option_type_id]).option_values
      end

      def scope
        parent_scope
      end

      def scope_includes
        [:option_type]
      end

      def permitted_resource_params
        params.require(:option_value).permit(:name, :presentation, :position)
      end
    end
  end
end
