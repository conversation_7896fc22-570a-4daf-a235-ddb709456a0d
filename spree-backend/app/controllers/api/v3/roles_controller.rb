# frozen_string_literal: true

module Api
  module V3
    class RolesController < ResourceController
      protected

      def model_class
        Spree::Role
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def permitted_resource_params
        params.require(:role).permit(:name,
          permissions: [
            :show_dashboard,
            :view_finance_and_sales,
            :show_inventory,
            :create_and_edit_product,
            :allow_product_delete,
            :view_product_cost_price,
            :manage_stock,
            :manage_listings,
            :create_and_edit_listing,
            :edit_listing,
            :manage_sale_channel,
            :manage_orders,
            :create_and_edit_order,
            :manage_users,
            :create_and_edit_users,
            :send_email,
            :delete_users,
            :view_activity_log,
            :access_reports,
            :sale_and_finance_report,
            :product_and_inventory_report,
            :store_settings,
            :create_and_edit_store,
            :publish_unpublish_store,
            :delete_store,
            :manage_store_payment_configuration,
            :general_settings,
            :manage_webhooks,
            :manage_roles,
            :manage_blogs,
            :view_blogs,
            :create_and_edit_blogs,
            :delete_blogs,
            :manage_organisation_subscriptions,
            :view_organisation_subscriptions_payments,
            :manage_two_factor_authentication,
          ])
          .tap { |params| params[:permission_attributes] = params.delete(:permissions) if params.key?(:permissions) }
      end
    end
  end
end
