# frozen_string_literal: true

module Api
  module V3
    class PlanFeaturesController < ResourceController

      # TODO: remove when removing tenants
      around_action :with_public_tenant

      def organisation_features_index
        features = Axel::OrganisationSubscriptionService.new.get_active_subscription_features(params[:organisation_id])
        render_serialized_payload { serialize_collection(features) }
      end

      protected

      def model_class
        Axel::PlanFeature
      end

      def parent_scope
        if params[:plan_id].present?
          model_class.where(plan_id: params[:plan_id])
        else
          model_class
        end
      end

      private

      def resource_params
        params.require(:plan_feature).permit(:name, :description, :plan_id)
      end
    end
  end
end
