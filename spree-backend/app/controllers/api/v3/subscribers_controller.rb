# frozen_string_literal: true

module Api
  module V3
    class SubscribersController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = Spree::Subscriber.all.ransack(params[:filter]).result.order(created_at: :desc, id: :desc).page(params[:page]).per(per_page)

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render_serialized_payload { serialize_collection(collection) }
      end

      protected

      def model_class
        Spree::Subscriber
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end
    end
  end
end
