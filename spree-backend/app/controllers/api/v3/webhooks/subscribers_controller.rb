# frozen_string_literal: true

module Api
  module V3
    module Webhooks
      class SubscribersController < ResourceController
        before_action -> { params[:page] ||= 1 }, only: :index

        def supported_events
          render_serialized_payload { Spree::Webhooks::Subscriber.supported_events.to_json }
        end

        private

        def model_class
          Spree::Webhooks::Subscriber
        end

        def authorize_spree_user
          case action_name
          when 'supported_events' then true
          else
            super
          end
        end

        def permitted_resource_params
          params.require(:subscriber).permit(:url, :active, subscriptions: [])
        end
      end
    end
  end
end
