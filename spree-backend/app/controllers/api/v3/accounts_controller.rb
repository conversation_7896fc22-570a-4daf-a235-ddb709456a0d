# frozen_string_literal: true

module Api
  module V3
    class AccountsController < BaseController
      include JsonWebToken
      include Api::V3::FetchUserTenants

      before_action :validate_json_web_token, only: [:sign_up]
      before_action :validate_auth_user, only: :oauth_login

      VALID_PROVIDERS = {
        'google' => 'https://accounts.google.com',
        'apple' => 'https://appleid.apple.com'
      }.freeze

      MAX_SUB_USERS = 3

      def public_tenant_url
        url = Spree::Organisation.find_by(subdomain: 'public')&.url
        url ||= Spree::Organisation::HOST_URL_MAP[Socket.gethostname]
        url ||= request.base_url

        render json: { public_url: url }, status: :ok
      end

      def oauth_login
        email = auth_data['email']
        organisation = Spree::Organisation.find_by(admin_email: email)
        user = Spree::AdminUser.find_by(email: email)

        token = JsonWebToken.encode(
          provider: provider,
          uid: auth_data['sub'],
          email: email
        )

        case params[:commit]
        when 'sign_up'
          return render_error_payload("Organisation with email #{email} already exists. Please sign in instead.") if organisation

          render json: {
            message: "#{provider.capitalize} sign up successful. Please set up your account.",
            access_token: token
          }, status: :ok
        when 'sign_in'
          return render_error_payload('No organisation linked to this email. Please sign up first.') if user.nil?

          fetch_user_tenants(email)
        else
          render_error_payload('Invalid action', :bad_request)
        end
      end

      def sign_up
        @email = @decoded_token['email']
        return render_error_payload('Email address is required') if @email.blank?

        return render_error_payload("A maximum of #{MAX_SUB_USERS} sub-user emails is allowed.") if sub_users_exceed_limit?

        organisation = Spree::Organisation.new(
          subdomain: params[:subdomain],
          admin_email: @email
        )

        if organisation.save
          create_and_notify_users(organisation)
        else
          render_error_payload(organisation.errors.full_messages.to_sentence)
        end
      end

      def sign_in
        result = Account::SignInService.new(sign_in_params.to_h.symbolize_keys).call
        render json: result.merge(message: 'Sign In Successfully'), status: :ok
      rescue Account::SignInService::SignInError => e
        handle_sign_in_error(e)
      rescue StandardError => e
        log_and_render_error(e)
      end

      private

      def decode_jwt(token)
        JWT.decode(token, nil, false).first
      rescue JWT::DecodeError
        nil
      end

      def create_and_notify_users(organisation)
        saved_user_emails = create_tenant_and_users(organisation)
        notify_sub_users(saved_user_emails, organisation)

        store = nil
        Apartment::Tenant.switch(organisation.subdomain) do
          store = Spree::Store.first
        end

        Spree::AccountMailer.seller_welcome_email(organisation, store).deliver_later(wait: 2.minutes) unless Rails.env.test?

        result = Account::SignInService.new(
          email: @email,
          workspace_name: organisation.name
        ).call

        render json: result.merge(message: 'Organisation Setup Successfully.'), status: :ok
      rescue StandardError => e
        organisation.destroy!
        render_error_payload("Organisation setup failed: #{e.message}", :bad_request)
      end

      def create_tenant_and_users(organisation)
        saved_user_emails = []

        Apartment::Tenant.switch(organisation.subdomain) do
          store = Spree::Store.first
          user = Spree::User.find_or_initialize_by(email: @email)

          user.assign_attributes(
            confirmed_at: Time.current,
            password: SecureRandom.base58(16),
            provider: @decoded_token['provider'].presence || 'email',
            uid: @decoded_token['uid']
          )
          user.save!

          role = Spree::Role.find_or_create_by(name: 'store_employee')

          Array(params[:sub_users]).each do |email|
            sub_user = Spree::User.find_or_initialize_by(email: email)
            next unless sub_user.new_record?

            sub_user.assign_attributes(
              password: SecureRandom.base58(16),
              confirmed_at: Time.current,
              confirmation_sent_at: Time.current
            )
            if sub_user.save
              sub_user.spree_roles << role if sub_user.spree_roles.exclude?(role)
              sub_user.spree_stores << store if sub_user.spree_stores.exclude?(store)

              saved_user_emails << email
            end
          end
        end
        saved_user_emails
      end

      def notify_sub_users(saved_user_emails, organisation)
        saved_user_emails.each do |email|
          Spree::AccountMailer.store_employee_invite(email, organisation).deliver_later(wait: 2.minutes) unless Rails.env.test?
        end
      end

      def normalized_email(params)
        email = params[:email].to_s.strip.downcase
        email if email.match?(/\A[^@\s]+@[^@\s]+\z/)
      end

      def sign_in_params
        params.permit(:email, :workspace_name, :confirmation_code, :access_token)
      end

      def handle_sign_in_error(error)
        if error.message.include?('redirect_to')
          data = JSON.parse(error.message)
          render json: {error: 'Redirecting to correct tenant', redirect_to: data['redirect_to']}, status: :bad_request
        else
          render_error_payload(error.message, :bad_request)
        end
      end

      def log_and_render_error(error)
        Rails.logger.error("[AccountsController#sign_in] #{error.class}: #{error.message}")
        Rails.logger.error(error.backtrace.join("\n"))
        render_error_payload(error.message, :bad_request)
      end

      def validate_auth_user
        return if Rails.env.test?

        provider = params[:provider].to_s.strip.downcase
        unless VALID_PROVIDERS.key?(provider)
          return render_error_payload('Missing or invalid provider. Allowed providers: google, apple.', :bad_request)
        end

        auth_token = params[:auth_token]
        return render_error_payload('Missing auth token', :bad_request) if auth_token.blank?

        auth_data = decode_jwt(auth_token)
        if auth_data.nil? || auth_data['email'].blank?
          return render_error_payload('Invalid token data', :unauthorized)
        end

        render_error_payload("Provider mismatch: Auth token issuer does not match with #{provider}", :unauthorized) unless auth_data['iss'] == VALID_PROVIDERS[provider]
      end

      def provider
        @provider ||= params[:provider].to_s.strip.downcase
      end

      def auth_data
        @auth_data ||= decode_jwt(params[:auth_token]) || {}
      end

      def sub_users_exceed_limit?
        params[:sub_users]&.size.to_i > MAX_SUB_USERS
      end
    end
  end
end
