# frozen_string_literal: true

module Api
  module V3
    class BaseController < ::Spree::Api::V2::BaseController
      include ActionController::Cookies
      before_action :set_time_zone
      rescue_from Operations::NotAllowed, with: :render_error
      rescue_from ActiveRecord::RecordNotFound, with: :record_not_found

      private

      def validate_json_web_token
        auth_header = request.headers['Authorization']
        token = auth_header&.split(' ')&.last || params[:token]

        unless token.present?
          return render_error_payload('Token missing', :unauthorized)
        end

        @decoded_token = JsonWebToken.decode(token)

        if @decoded_token.nil?
          return render_error_payload('Invalid Token', :unauthorized)
        end

        if Time.at(@decoded_token['exp']) < Time.current
          render_error_payload('Token has Expired', :unauthorized)
        end
      end

      def set_time_zone
        header_timezone = request.headers['timezone']
        Time.zone = header_timezone || cookies['timezone'] || config.time_zone || 'UTC'
      rescue ArgumentError => e # ArgumentError (Invalid Timezone
        Rails.logger.info("set_time_zone Exception #{e}")
        Time.zone = config.time_zone || 'UTC'
      end

      protected

      def render_serialized_payload(status = :ok)
        render json: yield, status: status
      end

      def render_error_payload(error, status = :unprocessable_entity)
        json = if error.is_a?(ActiveModel::Errors)
          {error: error.full_messages.to_sentence, errors: error.messages}
        elsif error.is_a?(Struct)
          {error: error.to_s, errors: error.to_h}
        else
          {error: error}
        end

        render json: json, status: status
      end

      def render_result(result, status = :ok)
        if result.success?
          render_serialized_payload(status) { serialize_resource(result.value) }
        else
          render_error_payload(result.error)
        end
      end

      def current_user
        return unless doorkeeper_token
        return @current_user if @current_user

        doorkeeper_authorize!

        @current_user ||= doorkeeper_token.resource_owner
      end

      def current_ability
        @current_ability ||= Spree::Dependencies.ability_class.constantize.new(current_user)
      end
    end
  end
end
