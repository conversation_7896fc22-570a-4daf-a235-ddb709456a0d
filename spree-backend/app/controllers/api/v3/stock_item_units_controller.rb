# frozen_string_literal: true

module Api
  module V3
    class StockItemUnitsController < ResourceController
      before_action :set_filters, only: :index
      after_action :record_activity, only: :update

      # Override create method due to use command
      def create
        stock_item_id = create_params[:items].sample[:stock_item_id]
        product = ::Spree::StockItem.find_by(id: stock_item_id)&.product
        command = ::Admin::BatchCreateStockItemUnitCommand.call(
          stock_item_unit_params: create_params[:items],
          current_user: spree_current_user,
          action_place: product ? "/admin/inventory/products/#{product.id}/stock" : "N/A",
        )
        if command.success?
          # Reload stock_item_uints by ids
          ids = command.result.values.flatten.map(&:id)
          stock_item_units = Spree::StockItemUnit.where(id: ids).includes(:stock_item)
          render_serialized_payload(:created) do
            serialize_resource(stock_item_units.first)
          end
        else
          render_error_payload(command.errors.full_messages.join(', '))
        end
      end

      def batch_create
        stock_item_id = create_params[:items].sample[:stock_item_id]
        product = ::Spree::StockItem.find_by(id: stock_item_id)&.product
        command = ::Admin::BatchCreateStockItemUnitCommand.call(
          stock_item_unit_params: create_params[:items],
          current_user: spree_current_user,
          action_place: product ? "/admin/inventory/products/#{product.id}/stock" : "N/A",
        )
        if command.success?
          # Reload stock_item_uints by ids
          ids = command.result.values.flatten.map(&:id)
          stock_item_units = Spree::StockItemUnit.where(id: ids).includes(:stock_item)
          render_serialized_payload do
            serialize_collection(stock_item_units)
          end
        else
          render_error_payload(command.errors.full_messages.join(', '))
        end
      end

      def batch_update
        unless params[:product_id]
          return render_error_payload('need provide parameter product id')
        end

        product_id, stock_item_unit_id = params.require([:product_id, :stock_item_unit_id])
        product = current_store.products.find(product_id)

        command = ::Admin::BatchUpdateStockItemUnitCommand.call(
          product: product,
          stock_item_unit_id: stock_item_unit_id,
          attributes: batch_update_params,
          current_user: spree_current_user,
          action_place: product ? "/admin/inventory/products/#{product.id}/stock" : "N/A",
        )

        if command.success?
          # Reload stock_item_uints by ids
          stock_item_units = Spree::StockItemUnit.where(id: stock_item_unit_id).includes(:stock_item)
          render_serialized_payload do
            serialize_collection(stock_item_units)
          end
        else
          render_error_payload(command.errors.full_messages.join(', '))
        end
      end

      def verify_removable
        unless params[:product_id]
          return render_error_payload('need provide parameter product id')
        end

        unless params[:stock_item_unit_id]
          return render_error_payload('need provide parameter stock item unit id')
        end

        product_id, stock_item_unit_id = params.require([:product_id, :stock_item_unit_id])
        product = current_store.products.find(product_id)

        records = Spree::StockItemUnit.where(id: stock_item_unit_id).where.not(state: 'stock')
        if records.present?
          return render_error_payload("stock item unit [#{records.pluck(:id).join(",")}] are not 'stock' state")
        end

        records = Spree::StockItemUnit.joins(stock_item: :variant).where(
          id: stock_item_unit_id,
          state: :stock,
          spree_variants: {product_id: product_id}
        )

        command = ::Admin::StockRemovalCheckCommand.call(
          product: product,
          stock_item_units: records
        )

        if command.success?
          head :no_content
        else
          render_error_payload(command.errors.full_messages.join(', '))
        end
      end

      def batch_lock
        unless params[:shipment_id]
          return render_error_payload('need provide parameter shipment id')
        end

        unless params[:line_item_id]
          return render_error_payload('need provide parameter line_item id')
        end

        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        unless params[:product_id]
          return render_error_payload('need provide parameter product id')
        end

        shipment = Spree::Shipment.find_by(id: params[:shipment_id])
        line_item = Spree::LineItem.find_by(id: params[:line_item_id])
        order = Spree::Order.find_by(id: params[:order_id])
        shipment&.stock_item_units&.select { |siu|
          siu.line_item_id == line_item.id && siu.state == 'locked'
        }&.sum(&:pack_size)

        product = current_store.products.friendly.find(params[:product_id])
        variant = line_item.variant
        stock_locations = variant.available_stock_locations

        search = nil
        available = nil
        locked = nil
        if params[:q]
          search = product.stock_item_units.includes(:stock_item).ransack(params[:q])
          available = search.result.stock.where(spree_stock_items: { variant_id: variant.id })
          locked = search.result.where(spree_stock_item_units: {
            state: :locked,
            shipment_id: params[:shipment_id]
          }) if params[:shipment_id].present? && params[:line_item_id].present?
        else
          search = product.stock_item_units.includes(:stock_item)
          available = search.stock.where(spree_stock_items: { variant_id: variant.id })
          locked = search.where(spree_stock_item_units: {
            state: :locked,
            shipment_id: params[:shipment_id]
          }) if params[:shipment_id].present? && params[:line_item_id].present?
        end

        params[:quantity] = update_pack_size(order, line_item, params[:quantity]) unless line_item.storefront_sale_channel?

        scope = locked ? available.or(locked) : available
        render(json: {stock_item_units: format_result(scope), stock_locations: stock_locations})
      end

      def batch_shipped
        unless params[:shipment_id]
          return render_error_payload('need provide parameter shipment id')
        end

        shipment = ::Spree::Shipment.find(params[:shipment_id])
        shipped_stock_items = []

        shipment.order_package&.shipments&.each do |shipment|
          stock_item_units = shipment.stock_item_units.where(state: 'shipped').order(created_at: :asc)

          # shipped_stock_items_data = stock_item_units.group_by { |siu| siu.stock_item.variant_id }.transform_values do |sius|
          shipped_stock_items_data = stock_item_units.map do |siu|
            {
              id: siu.id,
              number: siu.number,
              printed: siu.printed,
              pack_size: siu.pack_size,
              pack_size_counter: siu.pack_size_counter,
              vendor_receipt_date: siu.vendor_receipt_date&.strftime('%m/%d/%Y'),
              vendor_receipt_number: siu.vendor_receipt_number,
              vendor_inventory_number: siu.vendor_inventory_number,
              vendor_inventory_cost: siu.vendor_inventory_cost,
              expiry_date: siu.expiry_date&.strftime('%m/%d/%Y'),
              remark: siu.remark,
              remarks: siu.remarks,
              stock_item_id: siu.stock_item_id,
              state: siu.state,
              shipment_id: siu.shipment_id,
              line_item_id: siu.line_item_id,
              variant_name: siu.stock_item.variant.name,
              variant_sku: siu.stock_item.variant.sku,
              variant_id: siu.stock_item.variant.id,
              variant_descriptive_name: siu.stock_item.variant.descriptive_name
            }
          end

          shipped_stock_items << shipped_stock_items_data
        end
        render(json: {shipped_stock_items: shipped_stock_items.compact.flatten})
      end

      def find
        stock_item_unit = nil
        if params.key?(:number)
          stock_item_unit = scope.find_by(number: params[:number]) ||
            scope.find_by(vendor_inventory_number: params[:number])
        end

        if stock_item_unit
          render_serialized_payload { serialize_resource(stock_item_unit) }
        else
          record_not_found
        end
      end

      def pricecompare_info
        command = ::Admin::GetPricecompareInfoCommand.call(vin: params[:vin])
        render(json: command.result)
      end

      def divide_units
        unless params[:stock_item_unit_id]
          return render_error_payload('need provide parameter stock_item_unit_id')
        end

        stock_item_unit_id = params[:stock_item_unit_id]

        stock_item_unit = Spree::StockItemUnit.find(stock_item_unit_id)
        pack_size = stock_item_unit.pack_size
        if pack_size <= 1
          return render_error_payload('stock_item_unit pack_size must larger than 1')
        end

        units = []

        (1...pack_size).each do |index|
          new_item = stock_item_unit.dup
          new_item.number = nil
          new_item.printed = false

          new_item = duplicate_stock_item_unit(new_item, index)
          units << new_item if new_item&.valid?
        end

        duplicate_stock_item_unit(stock_item_unit, pack_size)
        units = stock_item_unit.stock_item.stock_item_units.on_hand.order(updated_at: :desc)
        render_serialized_payload do
          serialize_collection(units)
        end
      end

      private

      def set_filters
        params[:filter] ||= {}

        unless params[:item_id]
          params[:page] ||= 1 # Without scope, force using pagination
        end

        q = params[:filter]
        q[:stock_item_stock_location_section_id_eq] = q.delete(:stock_item_stock_location_id_eq) if q[:stock_item_stock_location_id_eq]
        q[:stock_item_stock_location_id_eq] = q.delete(:stock_item_stock_id_eq) if q[:stock_item_stock_id_eq]
      end

      def model_class
        Spree::StockItemUnit
      end

      def parent_scope
        if params[:stock_id]
          Spree::StockLocations.find(params[:stock_id]).stock_item_units
        elsif params[:product_id]
          Spree::Product.find(params[:product_id]).stock_item_units
        elsif params[:item_id] # is a stock_item_id
          Spree::StockItem.find(params[:item_id]).stock_item_units
        else
          super
        end
      end

      def scope(skip_cancancan: false)
        base_scope = super
        if params[:shipment_id]
          available = base_scope.stock
          locked = base_scope.where(spree_stock_item_units: {
            state: :locked,
            shipment_id: params[:shipment_id],
            line_item_id: params[:line_item_id]
          })
          base_scope = available.or(locked)
        end
        base_scope
      end

      def serializer_params
        super
          .tap { |params| params[:view] = :full if action_name == 'find' }
      end

      def authorize_spree_user
        return if spree_current_user.nil?

        case action_name
        when 'batch_create'
          spree_authorize!(:create, model_class)
        when 'batch_update'
          spree_authorize!(:update, model_class)
        when 'batch_lock'
          true
        when 'batch_shipped'
          true
        when 'pricecompare_info'
          true
        when 'divide_units'
          true
        when 'find'
          true
        when 'verify_removable'
          true
        else
          super
        end
      end

      def duplicate_stock_item_unit(new_item, index)
        new_item.vendor_inventory_cost = params[:vic].to_f.round(1)
        new_item.pack_size_counter = "#{index}/#{new_item.pack_size}"
        new_item.pack_size = 1

        new_item.save
        record_activity_log(new_item)

        new_item
      end

      def create_params
        p = params.require(:stock_item_unit)
        unless p.key?(:items)
          p[:items] = [p.dup]
        end

        p.permit(items: [
          :stock_item_id,
          :pack_size,
          :expiry_date,
          :remark,
          :vendor_receipt_number,
          :vendor_receipt_date,
          :vendor_inventory_number,
          :vendor_inventory_cost
        ])
      end

      def batch_update_params
        params.permit(:state, :line_item_id, :shipment_id, :printed, :from_state)
      end

      def permitted_resource_params
        params.require(:stock_item_unit).permit(
          :pack_size,
          :expiry_date,
          :remark,
          :vendor_receipt_number,
          :vendor_receipt_date,
          :vendor_inventory_number,
          :vendor_inventory_cost,
          :printed
        )
      end

      def update_pack_size(order, line_item, quantity)
        quantity = quantity.to_i
        pack_size = line_item&.pack_size
        quantity *= pack_size if pack_size.present? && pack_size.positive?
        quantity
      end

      def format_result(siu)
        siu&.map do |r|
          {
            id: r&.id,
            number: r&.number,
            expiry_date: r&.expiry_date,
            remark: r&.remark,
            vendor_receipt_number: r&.vendor_receipt_number,
            vendor_inventory_number: r&.vendor_inventory_number,
            vendor_receipt_date: r&.vendor_receipt_date,
            printed: r&.printed,
            stock_item_id: r&.stock_item_id,
            stock_location_id: r&.stock_item&.stock_location_id,
            created_at: r&.created_at,
            updated_at: r&.updated_at,
            state: r&.state,
            shipment_id: r&.shipment_id,
            line_item_id: r&.line_item_id,
            vendor_inventory_cost: r&.vendor_inventory_cost,
            pack_size: r&.pack_size,
            pack_size_counter: r&.pack_size_counter,
            expiry_type: r&.expiry_type,
            variant_name: r&.stock_item&.variant&.name,
            variant_sku: r&.stock_item&.variant&.sku,
            variant_id: r&.stock_item&.variant&.id,
            variant_descriptive_name: r&.stock_item&.variant&.descriptive_name
          }
        end
      end

      def record_activity
        record_activity_log(resource)
      end

      def record_activity_log(stock_item_unit)
        product = stock_item_unit&.stock_item&.product

        ::Spree::ActivityLog.create!(
          loggable: stock_item_unit,
          user_id: spree_current_user.id,
          action: ::Spree::ActivityLogActions.get(:stock_item_unit, :edit),
          action_name: stock_item_unit.number,
          role: spree_current_user.spree_roles.first&.name,
          date: Time.current,
          email: spree_current_user.email,
          action_place: product ? "/admin/inventory/products/#{product.id}/stock" : "N/A",
          product_id: product&.id
        )
      end
    end
  end
end
