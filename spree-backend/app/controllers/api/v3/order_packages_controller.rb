# frozen_string_literal: true

module Api
  module V3
    class OrderPackagesController < ResourceController
      SHIPMENT_STATES = ['ready', 'ship', 'cancel', 'resume', 'pend']

      def index
        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        collection = Spree::OrderPackage.where(order_id: params[:order_id]).order(created_at: :desc)
        render_serialized_payload { serialize_collection(collection) }
      end

      def destroy
        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        order_package = ::Spree::OrderPackage.find_by(id: params[:id])
        unless order_package
          return render_error_payload('please provide a valid id')
        end

        # order_package.shipments.update_all!(state: 'ready')
        order_package.shipments.update_all!(order_package_id: nil)
        if order_package.destroy
          head :no_content
        else
          render_error_payload(order_package.errors)
        end
      end

      def shipments
        result = Easypost::OrderPackageShipments.new(params[:id]).call

        if result[:success]
          render json: result[:data].to_json
        else
          render json: {message: result[:message]}, status: result[:status]
        end
      end

      def shipping_rate
        service = Easypost::GetShippingRates.new(params)
        result = service.call

        if result.success?
          render_serialized_payload do
            serialize_collection(result.shipping_rates, '::Api::V3::ShippingRateSerializer'.constantize)
          end
        else
          render json: {message: result.error_message}, status: result.status
        end
      rescue StandardError => e
        render json: {message: "An error occurred: #{e.message}"}, status: :internal_server_error
      end

      def buy_postage
        ActiveRecord::Base.transaction do
          resource = ::Spree::OrderPackage.find_by(id: params[:id])

          begin
            postage_service = Easypost::Postage.new(resource, params, spree_current_user)

            updated_weight = postage_service.extract_weight_params
            insure, insure_for_amount = postage_service.log_buy_postage_action

            postage_service.update_shipment_details(insure, insure_for_amount)

            resource.reload
            resource.update_amounts

            postage_service.handle_postage_purchase(updated_weight)

            render json: resource, template: 'show', status: :ok
          rescue => e
            error_response = postage_service.format_easypost_error(e)
            render json: { error: error_response }, status: :bad_request
          end
        end
      end

      def cancel_shipping_label
        result = Easypost::CancelPostage.new(params[:id], params[:easypost_setting]).delete_label

        if result[:error]
          render(json: {error: result[:error]}, status: :unprocessable_entity)
        else
          render(json: result)
        end
      end

      def attach_pdf
        order_package = ::Spree::OrderPackage.find(params[:id])
        return render json: {error: 'Order Package not found'}, status: :not_found if order_package.nil?

        result = Easypost::AttachLabel.call(order_package: order_package)

        if result.success?
          render json: {success: 'PDF attached successfully'}, status: :ok
          elsebegin
          render json: {error: result.error_message}, status: result.status
        end
      rescue StandardError
        render json: {error: 'An error occurred while processing the request'}, status: :internal_server_error
      end

      def dimensions
        if params[:shipment_ids].blank?
          render json: {error: 'Shipment IDs are required'}, status: :bad_request and return
        end

        shipment_ids = params[:shipment_ids]

        calculator = Easypost::ShipmentDimensionsCalculator.new(shipment_ids)
        dimensions = calculator.calculate

        render json: dimensions, status: :ok
      rescue ActiveRecord::RecordNotFound
        render json: {error: 'Some or all shipments not found'}, status: :not_found
      rescue => e
        render json: {error: 'An unexpected error occurred', details: e.message}, status: :internal_server_error
      end

      def check_shipping_categories
        shipment_ids = params[:shipment_ids]
        service = Easypost::CheckShippingCategories.new(shipment_ids)
        result = service.call
        render json: result
      end

      SHIPMENT_STATES.each do |state|
        define_method(state) do
          processor = ::Shipment::ShipmentProcessor.new(state, params, spree_current_user)
          result = processor.call

          if result.success?
            render json: {shipment: result.value}, status: :ok
          else
            render json: {message: "Can't Mark as Shipped"}, status: :unprocessable_entity
          end
        rescue StandardError => e
          render json: {message: "An error occurred: #{e.message}"}, status: :internal_server_error
        end
      end

      protected

      def model_class
        Spree::OrderPackage
      end

      def resource
        @resource ||= Spree::OrderPackage.find_by(id: params[:id]) # current_store.easypost_setting || current_store.build_easypost_setting
      end

      def permitted_resource_params
        params.require(:order_packages).permit(:number, :tracking,
          :tracking_label, :state, :selected_shipping_rate_id, :order_id,
          :carrier_accounts_shipping, :carrier_accounts_returns, :insure, :insure_for_amount,
          :cost_to_insure, :shipping_source)
      end

      def authorize_spree_user
        case action_name
        when 'destroy'
          true
        else
          super
        end
      end

      def set_pagination_headers
        # do nothing to ignore parent's set_pagination_headers
      end
    end
  end
end
