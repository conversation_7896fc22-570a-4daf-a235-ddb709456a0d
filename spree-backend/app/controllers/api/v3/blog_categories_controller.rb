# frozen_string_literal: true

module Api
  module V3
    class BlogCategoriesController < ResourceController
      include ::AuthorizeUser
      before_action :authorized_to_view_blogs?, only: [:index, :show]
      before_action :authorized_to_create_edit_blogs?, only: [:create, :update]
      before_action :authorized_to_delete_blogs?, only: [:destroy]

      private

      def permitted_resource_params
        params.require(:blog_category).permit(:name, :visible, :position).tap { |p| p[:store_id] = current_store.id }
      end

      def model_class
        Spree::BlogCategory
      end

      def parent_scope
        model_class.for_store(current_store).friendly
      end

      def spree_permitted_attributes
        [:id, :name, :visible, :store_id, :position, :created_at, :updated_at]
      end
    end
  end
end
