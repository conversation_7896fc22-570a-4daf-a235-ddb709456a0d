# frozen_string_literal: true

module Api
  module V3
    class BlogPostsController < ResourceController
      include ::AuthorizeUser
      before_action :authorized_to_view_blogs?, only: [:index, :show]
      before_action :authorized_to_create_edit_blogs?, only: [:create, :update]
      before_action :authorized_to_delete_blogs?, only: [:destroy]

      def upload_images
        image_files = params.dig(:blog_post, :image_files).presence
        return render json: {images: []}, status: :ok unless image_files

        attachments = image_files.map do |image|
          current_store.blog_post_attachments.create!(
            user_id: current_user.id,
            title: image.original_filename,
            file: image
          )
        end

        image_data = attachments.map do |attachment|
          {
            id: attachment.id,
            url: Rails.application.routes.url_helpers.rails_blob_url(attachment.file, host: request.base_url)
          }
        end

        render json: {images: image_data}, status: :created
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error("Failed to upload images: #{e.message}")
        render json: {error: 'Failed to upload one or more images'}, status: :unprocessable_entity
      end

      def remove_images
        attachment_ids = params.dig(:blog_post, :remove_attachment_ids).presence
        return render json: {message: 'No images to remove'}, status: :ok unless attachment_ids

        attachments = current_store.blog_post_attachments.where(id: attachment_ids)

        if attachments.exists?
          attachments.destroy_all
          render json: {message: 'Images removed successfully'}, status: :ok
        else
          render json: {message: 'No matching images found'}, status: :not_found
        end
      rescue StandardError => e
        Rails.logger.error("Failed to remove images: #{e.message}")
        render json: {error: 'An error occurred while removing images'}, status: :unprocessable_entity
      end

      def create
        resource = model_class.new(permitted_resource_params)
        ensure_current_store(resource)

        if resource.save
          attach_attachments(resource) if params.dig(:blog_post, :add_attachment_ids).present?
          render_serialized_payload(:created) { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def update
        resource.assign_attributes(permitted_resource_params)
        ensure_current_store(resource)

        if resource.save
          attach_attachments(resource) if params.dig(:blog_post, :add_attachment_ids).present?
          render_serialized_payload { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      private

      def permitted_resource_params
        params.require(:blog_post).permit(
          :title, :blog_category_id, :user_id, :visible, :description, :slug, :feature_image,
          :published_at, :summary, :position, :views_count, :likes_count, :meta_title,
          :meta_description, :meta_keywords, :author_name, tag_list: []
        )
      end

      def model_class
        Spree::BlogPost
      end

      def parent_scope
        model_class.for_store(current_store).friendly
      end

      def spree_permitted_attributes
        model_class.column_names.map(&:to_sym)
      end

      def attach_attachments(resource)
        attachments = current_store.blog_post_attachments.where(id: params.dig(:blog_post, :add_attachment_ids))
        attachments.update_all(blog_post_id: resource.id) if attachments.any?
      end
    end
  end
end
