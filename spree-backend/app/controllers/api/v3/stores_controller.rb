# frozen_string_literal: true

module Api
  module V3
    class StoresController < ResourceController
      def current
        render_serialized_payload { serialize_resource(current_store) }
      end

      def set_default
        resource.assign_attributes(default: true)
        ensure_current_store(resource)

        if resource.save
          render_serialized_payload { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def publish
        resource.assign_attributes(published: true)
        ensure_current_store(resource)

        if resource.save
          render_serialized_payload { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def disable
        resource.assign_attributes(published: false)
        ensure_current_store(resource)

        if resource.save
          render_serialized_payload { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      protected

      def authorize_spree_user
        case action_name
        when 'current'
          # TODO: Update cancan ability to check access current_user to current_store
          true
        else
          super
        end
      end

      def model_class
        Spree::Store
      end

      def scope(skip_cancancan: false)
        base_scope = current_store.default ? model_class : model_class.for_store(current_store)
        base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
        base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == 'index'
        base_scope
      end

      def serializer_params
        super
          .tap { |params| params[:view] = :full unless action_name == 'index' }
          .tap { |params| params[:view] = :current if action_name == 'current' }
      end

      def permitted_resource_params
        params.require(:store).permit(
          :name, :code, :url,
          :seo_title, :meta_description, :meta_keywords, :seo_robots,
          :mail_from_address, :customer_support_email, :new_order_notifications_email,
          :default_currency, :supported_currencies,
          :default_locale, :supported_locales, :default_country_id, :checkout_zone_id,
          :facebook, :twitter, :instagram,
          :description, :contact_phone, :address, :contact_us, :about_us,
          :term_policies, :guarantee, :return_refund_policy, :shipping_delivery,
          :logo_path, :mailer_logo_path, :favicon_path,
          :upcitemdb_api_key, :barcodelookup_api_key,
          :price_analytics_url, :price_analytics_client_id, :price_analytics_client_secret,
          :number_tracking,
          settings: [
            :limit_digital_download_count,
            :limit_digital_download_days,
            :digital_asset_authorized_clicks,
            :digital_asset_authorized_days,
            :digital_asset_link_expire_time
          ],
          meta_tags: [
            :name,
            :content
          ]
        )
      end
    end
  end
end
