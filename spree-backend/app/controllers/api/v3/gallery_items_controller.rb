# frozen_string_literal: true

module Api
  module V3
    class GalleryItemsController < ResourceController
      include ::Spree::BaseHelper

      before_action -> { params[:page] ||= 1 }, only: :index

      def space_statistics
        db_name = ActiveRecord::Base.connection.current_database
        query = ActiveRecord::Base.sanitize_sql(['SELECT pg_database_size(?) AS size', db_name])
        db_size = ActiveRecord::Base.connection.execute(query).first['size']
        attachments_count = ActiveStorage::Blob.joins(:attachments).count
        attachments_size = ActiveStorage::Blob.joins(:attachments).sum(:byte_size)
        storage_root_path = Rails.application.config.active_storage.service_configurations['local']['root']
        storage_root_size = folder_size(storage_root_path)
        space_statistics_info = []
        space_statistics_info.push({
          db_name: db_name,
          db_size: db_size,
          attachments_count: attachments_count,
          attachments_size: attachments_size,
          storage_root_path: storage_root_path,
          storage_root_size: storage_root_size
        })
        render(json: space_statistics_info)
      end

      def list_product
        objects = current_store.products
        objects = objects.page(params[:page]).per(13)
        render(json: objects)
      end

      def list_order
        objects = current_store.orders
        objects = objects.page(params[:page]).per(13)
        render(json: objects)
      end

      def list_listing
        objects = current_store.listing
        objects = objects.page(params[:page]).per(13)
        render(json: objects)
      end

      def get_product_object # rubocop:disable Naming/AccessorMethodName
        object = current_store.products.find_by(id: params[:object_id])
        image_ids = []
        if object&.images&.any?
          object&.images&.each do |img|
            image = img&.attachment
            image_ids.push(image.id)
          end
        end
        render(json: {product: object, images: image_ids})
      end

      def get_order_object # rubocop:disable Naming/AccessorMethodName
        object = current_store.orders.find_by(number: params[:object_number])
        image_ids = []
        if object&.images&.any?
          object&.images&.each do |attachment|
            image_ids.push(attachment.id)
          end
        end
        render(json: {order: object, images: image_ids})
      end

      def get_listing_object # rubocop:disable Naming/AccessorMethodName
        object = current_store.listing.find_by(id: params[:object_id])
        image_ids = []
        if object&.image_files&.any?
          object&.image_files&.each do |attachment|
            image_ids.push(attachment.id)
          end
        end
        render(json: {listing: object, image_ids: image_ids})
      end

      def remove_order_image
        object = current_store.orders.find_by(number: params[:object_number])
        image = object.images.find_by(id: params[:image_id])

        if image&.purge
          render(json: {status: 'success', message: 'Image deleted successfully'})
        else
          render(json: {status: 'error', error: 'Failed to delete image'}, status: :unprocessable_entity)
        end
      end

      def remove_listing_image
        object = current_store.listing.find_by(id: params[:object_id])
        image = object.image_files.find_by(id: params[:image_id])

        if image&.purge
          render(json: {status: 'success', message: 'Image deleted successfully'})
        else
          render(json: {status: 'error', error: 'Failed to delete image'}, status: :unprocessable_entity)
        end
      end

      def search
        scope = params[:scope]
        query = params[:query]

        if scope.present?
          case scope
          when 'Product'
            object = Spree::Product.join_translation_table(::Spree::Product).where(
              "#{::Spree::Product.translation_table_alias}.name ILIKE ?",
              "%#{params[:query]}%"
            )
          when 'Order'
            object = Spree::Order.where('number ILIKE ?', "%#{query}%")
          when 'Listing'
            object = Spree::Listing.where('title ILIKE ?', "%#{params[:query]}%")
          end
          objects = object&.page(params[:page])&.per(13)
          render(json: objects)
        else
          render(json: {status: 'error', message: 'Scope is empty'})
        end
      end

      protected

      def model_class
        Spree::GalleryItem
      end

      def scope
        current_store.gallery_items.with_attached_file
      end

      def allowed_sort_attributes; end

      def permitted_resource_params
        params.require(:gallery_item).permit(:title, :file)
          .tap { |p| p[:uploaded_by] = current_user.email }
      end
    end
  end
end
