# frozen_string_literal: true

module Api
  module V3
    class ProductPropertiesController < ResourceController
      before_action :ensure_product_exists

      def create
        resource = model_class.new(permitted_resource_params.merge(product_id: params[:product_id]))
        ensure_current_store(resource)

        if resource.save
          render_serialized_payload(:created) { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      rescue StandardError => e
        render_error_payload(e.message)
      end

      private

      def ensure_product_exists
        render_error_payload('Product not exists') unless Spree::Product.exists?(params[:product_id])
      end

      def model_class
        Spree::ProductProperty
      end

      def parent_scope
        Spree::Product.find(params[:product_id]).product_properties
      end

      def scope
        parent_scope
      end

      def scope_includes
        [:property]
      end

      def permitted_resource_params
        params.require(:product_property).permit(:property_name, :value, :position, :show_property)
      end
    end
  end
end
