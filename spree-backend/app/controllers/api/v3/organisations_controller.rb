# frozen_string_literal: true

module Api
  module V3
    class OrganisationsController < ResourceController
      def update_two_factor
        tenant_name = Apartment::Tenant.current
        organisation = Spree::Organisation.find_by(subdomain: tenant_name)

        return render_error_payload('Organisation not found', :not_found) if organisation.nil?

        if organisation.update(two_factor_enabled: params[:two_factor_enabled])
          render json: {
            message: "Two-factor authentication #{params[:two_factor_enabled] ? 'enabled' : 'disabled'} successfully",
            organisation: organisation
          }, status: :ok
        else
          render_error_payload('Failed to update two-factor authentication', :unprocessable_entity)
        end
      end

      def index
        current_tenant = Apartment::Tenant.current
        # TODO: added app only for tests to complete!
        if current_tenant == 'public' || current_tenant == "app"
          return super
        end

        # show only organisation for tenant from which request is coming
        Apartment::Tenant.switch!('public')
        render_serialized_payload { serialize_collection(scope.where(subdomain: current_tenant)) }
      ensure
        Apartment::Tenant.switch!(current_tenant) if current_tenant.present?
      end

      def show
        current_tenant = Apartment::Tenant.current
        # TODO: added app only for tests to complete!
        if current_tenant == 'public' || current_tenant == "app"
          return super
        end

        # show only organisation for tenant from which request is coming
        render_serialized_payload { serialize_resource(scope.where(id: params[:id], subdomain: current_tenant).first) }
      end

      protected

      def model_class
        Spree::Organisation
      end

      def scope
        Spree::Organisation
      end

      def allowed_sort_attributes
        []
      end

      def permitted_resource_params
        params.permit(
          :name,
          :subdomain,
          :admin_email,
          :custom_domain
        )
      end
    end
  end
end
