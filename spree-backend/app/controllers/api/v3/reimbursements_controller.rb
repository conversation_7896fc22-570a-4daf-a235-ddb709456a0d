# frozen_string_literal: true

module Api
  module V3
    class ReimbursementsController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        collection = Spree::Reimbursement.where(order_id: params[:order_id]).page(params[:page]).per(per_page)

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render_serialized_payload { serialize_collection(collection) }
      end

      def create
        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        unless params[:customer_return_id]
          return render_error_payload('need provide parameter customer_return_id')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        customer_return = ::Spree::CustomerReturn.find(params[:customer_return_id])
        unless customer_return
          return render_error_payload('please provide a valid customer_return id')
        end

        unless customer_return&.order&.id == order.id
          return render_error_payload('please provide a valid order id and customer_return id')
        end

        reimbursement = Spree::Reimbursement.build_from_customer_return(customer_return)

        if reimbursement.save
          render_serialized_payload { serialize_collection(reimbursement) }
        else
          render_error_payload(reimbursement.errors)
        end
      end

      def perform
        reimbursement = ::Spree::Reimbursement.find(params[:id])
        if params[:order_id]
          order = ::Spree::Order.find(params[:order_id])
          unless order
            return render_error_payload('please provide a valid order id')
          end

          unless reimbursement&.order_id == order.id
            return render_error_payload('please provide a valid order id and reimbursement id')
          end
        end

        reimbursement.perform!
        render_serialized_payload { {status: :ok} }
      rescue StateMachines::InvalidTransition => e
        render_error_payload(e.message)
      end

      protected

      def model_class
        Spree::Reimbursement
      end

      def permitted_resource_params
        params.require(:reimbursement).permit(
          return_items_attributes: [
            :id,
            :exchange_variant_id,
            :preferred_reimbursement_type_id,
            :override_reimbursement_type_id,
            :reason,
            :return_authorization_reason_id,
            :return_quantity,
            :pre_tax_amount
          ]
        )
      end
    end
  end
end
