# frozen_string_literal: true

module Api
  module V3
    class OrganisationSubscriptionsController < ResourceController
      before_action :find_organisation
      before_action :authorize_organisation_management
      before_action :find_subscription, only: [:update, :cancel, :create_stripe_portal_session, :payments]

      # TODO: remove when removing tenants
      around_action :with_public_tenant

      def initialize
        super
        @organisation_subscription_service = Axel::OrganisationSubscriptionService.new
      end

      def create
        unless params[:plan_id].present? &&
               params[:store_id].present? &&
               params[:organisation_id].present? &&
               params[:success_url].present? &&
               params[:cancel_url].present?
          return render json: {
            success: false,
            errors: [
              'plan_id, store_id, organisation_id, success_url and cancel_url are required'
            ]
          }, status: :unprocessable_entity
        end

        # check plan
        plan = Axel::Plan.where(id: params[:plan_id], status: 'active').first
        if plan.nil?
          return render json: {
            success: false,
            errors: ['Plan not found']
          }, status: :unprocessable_entity
        end

        # check for existing active subscriptions
        organisation = @target_organisation
        existing_active_subscription = Axel::OrganisationSubscription.where(
          organisation_id: organisation.id,
          status: ['active', 'pending']
        ).first

        if existing_active_subscription.nil?
          @organisation_subscription = model_class.new(organisation_subscription_params)
          @organisation_subscription.status = "pending"
          @organisation_subscription.organisation = organisation
          @organisation_subscription.plan = plan

          unless @organisation_subscription.save
            render json: { success: false, errors: @organisation_subscription.errors.full_messages }, status: :unprocessable_entity
          end
        else
          # if active - error, if pending - create new checkout session
          if existing_active_subscription.active?
            return render json: {
              success: false,
              errors: ['Organization already has an active subscription']
            }, status: :unprocessable_entity
          else
            @organisation_subscription = existing_active_subscription
            # change the plan if plan differs
            if existing_active_subscription.plan_id != plan.id
              @organisation_subscription.plan = plan
              @organisation_subscription.save
            end
          end
        end

        begin
          session = @organisation_subscription_service.create_checkout_session(
            @organisation_subscription,
            organisation,
            plan,
            params[:success_url],
            params[:cancel_url]
          )
        rescue Stripe::InvalidRequestError => e
          return render json: { success: false, errors: [e.message] }, status: :unprocessable_entity
        rescue Stripe::StripeError => e
          return render json: { success: false, errors: [e.message] }, status: :unprocessable_entity
        end

        render json: {
          success: true,
          organisation_subscription: Api::V3::OrganisationSubscriptionSerializer.render_as_hash(@organisation_subscription),
          checkout_session_url: session.url
        }, status: :created
      end

      def cancel
        result = @organisation_subscription_service.cancel_subscription(@organisation_subscription)

        if result[:success]
          render json: {
            success: true,
            organisation_subscription: Api::V3::OrganisationSubscriptionSerializer.render_as_hash(result[:organisation_subscription])
          }
        else
          render json: {
            success: false,
            errors: result[:errors]
          }, status: :unprocessable_entity
        end
      end

      def reactivate
        @organisation_subscription = parent_scope.find(params[:id])

        result = @organisation_subscription_service.reactivate_subscription(@organisation_subscription)

        if result[:success]
          render json: {
            success: true,
            organisation_subscription: Api::V3::OrganisationSubscriptionSerializer.render_as_hash(result[:organisation_subscription])
          }
        else
          render json: {
            success: false,
            errors: result[:errors]
          }, status: :unprocessable_entity
        end

      rescue ActiveRecord::RecordNotFound
        render json: { success: false, errors: ['Subscription not found'] }, status: :not_found
      end

      def create_stripe_portal_session
        unless params[:return_url].present?
          return render json: {
            success: false,
            errors: [
              'return_url is required'
            ]
          }, status: :unprocessable_entity
        end

        stripe_customer_id = @organisation_subscription.organisation.stripe_customer_id
        if stripe_customer_id.nil?
          return render json: {
            success: false,
            errors: ['Stripe customer ID not found']
          }
        end

        begin
          portal_session = @organisation_subscription_service.create_stripe_customer_portal(stripe_customer_id, params[:return_url])
        rescue Stripe::StripeError => e
          return render json: {
            success: false,
            errors: [e.message]
          }, status: :unprocessable_entity
        end

        render json: {
          success: true,
          organisation_subscription: Api::V3::OrganisationSubscriptionSerializer.render_as_hash(@organisation_subscription),
          portal_session_url: portal_session.url
        }
      end

      # Upgrade/downgrade plan
      def update
        unless params[:plan_id].present? &&
               params[:return_url].present? &&
               params[:success_url].present?
          return render json: {
            success: false,
            errors: [':plan_id, :return_url and :success_url are required'], status: :unprocessable_entity
          }
        end

        new_plan = Axel::Plan.where(id: params[:plan_id], status: 'active').first
        if new_plan.nil?
          return render json: {
            success: false,
            errors: ['Plan not found'], status: :unprocessable_entity
          }
        end

        begin
          # Retrieve stripe subscription info
          stripe_sub = @organisation_subscription_service.get_stripe_subscription(@organisation_subscription.external_id)

          stripe_sub_item_id = stripe_sub.items.data.first.id
          # Create portal for user to confirm upgrade/downgrade
          confirm_portal = @organisation_subscription_service.create_stripe_customer_confirm_portal(
            stripe_sub.customer,
            params[:return_url],
            params[:success_url],
            stripe_sub.id,
            stripe_sub_item_id,
            new_plan.stripe_price_id,
          )
        rescue Stripe::StripeError => e
          return render json: {
            success: false,
            errors: [e.message]
          }, status: :unprocessable_entity
        end

        render json: {
          success: true,
          organisation_subscription: Api::V3::OrganisationSubscriptionSerializer.render_as_hash(@organisation_subscription),
          confirm_portal_url: confirm_portal.url
        }
      end

      def payments
        payments = @organisation_subscription.organisation_subscription_payments
                                             .order(created_at: :desc)

        render json: {
          success: true,
          payments: Api::V3::OrganisationSubscriptionPaymentSerializer.render_as_hash(payments)
        }
      end

      private

      def find_subscription
        @organisation_subscription = parent_scope.find(params[:id])
        if @organisation_subscription.present? && !@organisation_subscription.active?
          render json: { success: false, errors: ['Subscription is not active'] }, status: :bad_request
        end
      rescue ActiveRecord::RecordNotFound
        render json: { success: false, errors: ['Subscription not found'] }, status: :not_found
      end

      def find_organisation
        @target_organisation = Spree::Organisation.find_by(id: params[:organisation_id])
        if @target_organisation.nil?
          render json: { success: false, errors: ['Organisation not found'] }, status: :not_found
        end
      end

      def authorize_organisation_management
        # TODO: change all this to just check if user has store_owner role. Move this roles to public schema.
        unless params[:organisation_id].present?
          render json: { success: false, errors: ['Organisation ID is required'] }, status: :unprocessable_entity and return
        end

        # current_user is from the original tenant (e.g., 'acme.users')
        # We assume current_user responds to 'email' (or another suitable unique identifier)
        unless current_user && current_user.respond_to?(:email) && current_user.email.present?
          render json: { success: false, errors: ['Unable to identify current user for authorization.'] }, status: :unauthorized and return
        end

        # check if user has "store_owner" role
        # TODO: when implementing permissions, check for "manage organisation_subscriptions" permission
        is_authorized_owner = current_user.spree_roles.where(name: 'store_owner').exists?

        unless is_authorized_owner
          render json: {
            success: false,
            errors: ['You are not authorized to manage this organisation.']
          }, status: :forbidden
        end

        # If we reach here, the user is authorized, and @target_organisation is loaded.
      end

      def model_class
        Axel::OrganisationSubscription
      end

      def parent_scope
        model_class.where(organisation_id: params[:organisation_id])
      end

      def organisation_subscription_params
        params.permit(:organisation_id, :plan_id, :store_id)
      end
    end
  end
end
