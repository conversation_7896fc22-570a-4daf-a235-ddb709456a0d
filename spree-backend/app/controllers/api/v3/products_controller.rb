# frozen_string_literal: true

module Api
  module V3
    class ProductsController < ResourceController
      include ::Spree::Api::V2::ProductListIncludes
      include ::Spree::Admin::ElasticsearchHelper
      include ::Product::ElasticsearchFilterable

      after_action :helper_sync_product_to_elasticsearch, only: [:create, :update, :destroy, :clone]
      after_action :record_activity_log, only: [:create, :update, :destroy]
      before_action :apply_elasticsearch_filtering, only: [:index]

      before_action -> { params[:page] ||= 1 }, only: :index
      before_action -> { params[:sort] ||= 'name' }, only: :index

      def clone
        # resource.abbreviation = generate_unique_abbreviation
        new_product = resource.api_duplicate(params)

        if new_product.persisted?
          render_serialized_payload(:created) { serialize_resource(new_product) }
        else
          render_error_payload(new_product.errors)
        end
      rescue ActiveRecord::RecordInvalid => e
        render_error_payload(e.message)
      end

      protected

      def model_class
        Spree::Product
      end

      def scope_includes
        [:translations, :master, :variants_including_master]
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      # def sorted_collection
      #  @sorted_collection ||= collection_sorter.new(collection, current_currency, params, allowed_sort_attributes).call.distinct(false)
      # end

      # def collection_sorter
      #  Spree::Api::Dependencies.platform_products_sorter.constantize
      # end

      def permitted_resource_params
        params.require(:product).permit(
          :name, :status, :sku, :abbreviation, :description, :upc, :promotionable, :temporary_unavailable,
          :price, :cost_currency, :cost_price, :compare_at_price, :compare_to_price,
          :available_on, :discontinue_on, :make_active_at,
          :lbs, :oz, :weight, :height, :width, :depth,
          :tax_category_id, :easy_post_hs_tariff_number,
          :meta_title, :meta_keywords, :meta_description,
          taxon_ids: [], option_type_ids: []
        )
      end

      private

      def record_activity_log
        return unless @resource.present?

        ::Spree::ActivityLog.create!(
          loggable: @resource,
          user_id: spree_current_user.id,
          action: ::Spree::ActivityLogActions.get(:product, resolve_product_action),
          role: spree_current_user.spree_roles.first&.name,
          date: Time.current,
          email: spree_current_user.email,
          action_place: resolve_product_action_place,
          action_name: @resource.name,
          product_id: @resource.id
        )
      end

      def resolve_product_action
        case action_name
        when "update"  then :edit
        when "destroy" then :remove
        when "create"  then :add
        else :default
        end
      end

      def resolve_product_action_place
        case action_name
        when "update", "create"
          "/admin/inventory/products/#{@resource.id}/edit"
        when "destroy"
          "/admin/inventory/products"
        else
          "N/A"
        end
      end
    end
  end
end
