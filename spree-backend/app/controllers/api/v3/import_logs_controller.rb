# frozen_string_literal: true

module Api
  module V3
    class ImportLogsController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        if params[:log_type]
          valid_log_types = ['job', 'webhook']
          if valid_log_types.exclude?(params[:log_type])
            return render_error_payload('The specified log type is not support', 400)
          end

          log_type = params[:log_type] == 'job' ? 0 : 1
          import_logs = Spree::ImportLog.where(store_id: current_store.id, log_type: log_type).order(created_at: :desc).page(params[:page])
                          .per(per_page)
        else
          import_logs = Spree::ImportLog.where(store_id: current_store.id).order(created_at: :desc).page(params[:page])
                          .per(per_page)
        end

        render(json: ImportLogSerializer.render(import_logs))
      end

      def show
        import_log = Spree::ImportLog.find(params[:id])
        render(json: ImportLogSerializer.render(import_log))
      end

      def import_sample
        send_data(
          File.read(Rails.public_path.join('sample/sample.xlsx').to_s),
          type: 'application/xlsx; header=present',
          disposition: 'attachment',
          filename: 'sample.xlsx'
        )
      end

      def clear
        if Spree::ImportLog.where(store_id: current_store.id).delete_all
          render(json: {status: 'success', message: 'Import logs clear successfully'})
        else
          render(json: {status: 'error', error: 'Failed to clear import logs'}, status: :unprocessable_entity)
        end
      end

      protected

      def model_class
        Spree::ImportLog
      end

      def permitted_resource_params
        params.permit(
          :id,
          :started_by_email,
          :state,
          :success_row_count,
          :error_row_count,
          :error_details,
          :initiated_for,
          :log_type
        )
      end
    end
  end
end
