# frozen_string_literal: true

module Api
  module V3
    class OrderItemsController < ResourceController
      before_action :parent_scope, only: [:create]

      def create
        result = Spree::LineItems::Create.call(order: @order, line_item_attributes: permitted_resource_params)
        if result.success?
          render_serialized_payload(:created) { serialize_resource(result.value) }
        else
          render_error_payload(result.error)
        end
      end

      def update
        result = Spree::LineItems::Update.call(line_item: resource, line_item_attributes: permitted_resource_params)
        if result.success?
          render_serialized_payload { serialize_resource(result.value) }
        else
          render_error_payload(resource.errors)
        end
      end

      def destroy
        result = Spree::LineItems::Destroy.call(line_item: resource)
        if result.success?
          head :no_content
        else
          render_error_payload(result.error)
        end
      end

      private

      def model_class
        Spree::LineItem
      end

      def parent_scope
        @order ||= current_store.orders.find(params[:order_id])
        @order.line_items
      end

      def scope
        parent_scope
      end

      def serializer_params
        super.tap { |params| params[:view] = :order }
      end

      def permitted_resource_params
        params.require(:order_item).permit(:variant_id, :quantity)
      end
    end
  end
end
