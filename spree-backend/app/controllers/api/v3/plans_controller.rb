# frozen_string_literal: true

module Api
  module V3
    class PlansController < ResourceController
      # TODO: remove when removing tenants
      around_action :with_public_tenant

      def create_stripe_products
        require "stripe"
        Stripe.api_key = ENV['AXEL_STRIPE_API_KEY']

        created_count = 0
        failed_count = 0
        results = { created: [], failed: [] }

        plans = model_class.all

        plans.each do |plan|
          begin
            begin
              # create Stripe product if not exists
              stripe_product = Stripe::Product.retrieve(plan.stripe_product_id)
              # TODO: update Stripe product if exists
            rescue Stripe::InvalidRequestError => e
              if e.http_status == 404
                Rails.logger.info("Creating Stripe product #{plan.stripe_product_id}")
                stripe_product = Stripe::Product.create(
                  id: plan.stripe_product_id,
                  name: plan.name,
                  description: "Subscription plan: #{plan.name}",
                  metadata: {
                    plan_id: plan.id
                  }
                )
              else
                raise e
              end
            end

            # create Stripe price if not exists
            stripe_prices = Stripe::Price.search({
                                                   query: "product:'#{plan.stripe_product_id}' AND active:'true'",
                                                 })

            if stripe_prices.data.empty?
              Rails.logger.info("Creating Stripe price for plan #{plan.id}")
              stripe_price = Stripe::Price.create(
                product: stripe_product.id,
                unit_amount: plan.price,
                currency: 'usd',
                recurring: {
                  interval: plan.recurring.split(' ')[1],
                  interval_count: plan.recurring.split(' ')[0],
                },
                metadata: {
                  plan_id: plan.id
                }
              )
              plan.update(stripe_price_id: stripe_price.id)
              created_count += 1
              results[:created] << { id: plan.id, name: plan.name, price_id: stripe_price.id, product_id: stripe_product.id }
            elsif plan.stripe_price_id.blank?
              plan.update(stripe_price_id: stripe_prices.data.first.id)
            end
          rescue => e
            failed_count += 1
            results[:failed] << { id: plan.id, name: plan.name, error: e.message }
          end
        end

        render json: {
          success: true,
          message: "Created/updated #{created_count} Stripe products/prices. Failed: #{failed_count}",
          results: results
        }, status: :ok
      rescue => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      protected

      def model_class
        Axel::Plan
      end

      def parent_scope
        model_class
      end
    end
  end
end
