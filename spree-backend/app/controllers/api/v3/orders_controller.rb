# frozen_string_literal: true

module Api
  module V3
    class OrdersController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      def create
        resource = current_store.orders.new(permitted_resource_params)
        resource.created_by_id = current_user.id

        if resource.save
          render_serialized_payload(:created) { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def update
        result = Spree::Checkout::Update.call(
          order: resource,
          params: update_params,
          # permitted_attributes: permitted_resource_params,
          permitted_attributes: spree_permitted_attributes,
          request_env: request.headers.env
        )
        render_result(result)
      end

      def empty
        result = Spree::Cart::Empty.call(order: resource)
        render_result(result)
      end

      def next
        result = Spree::Checkout::Next.call(order: resource)
        render_result(result)
      end

      def advance
        result = Spree::Checkout::Advance.call(order: resource)
        render_result(result)
      end

      def complete
        result = Spree::Checkout::Complete.call(order: resource)
        render_result(result)
      end

      def approve
        result = Spree::Orders::Approve.call(order: resource, approver: spree_current_user)
        render_result(result)
      end

      def cancel
        result = Spree::Orders::Cancel.call(order: resource, canceler: spree_current_user)
        render_result(result)
      end

      def resume
        resource.resume!
        render_serialized_payload { serialize_resource(resource.reload) }
      rescue StandardError => e
        render_error_payload(e)
      end

      def resend
        result = resource.deliver_order_confirmation_email
        if result == true
          render(json: {message: 'resend success'})
        else
          render_error_payload('resend fail')
        end
      end

      def destroy
        result = destroy_service.call(order: resource)

        if result.success?
          head :no_content
        else
          render_error_payload(result.error)
        end
      end

      def apply_coupon_code
        resource.coupon_code = params[:coupon_code]
        result = coupon_handler.new(resource).apply

        if result.error.blank?
          render_serialized_payload { serialize_resource(resource.reload) }
        else
          render_error_payload(result.error)
        end
      end

      def use_store_credit
        result = use_store_credit_service.call(
          order: resource,
          amount: params[:amount].try(:to_f)
        )

        render_result(result)
      end

      def sync
        ::Spree::SaleChannel.for_brand(params['type']).find_each do |channel|
          next unless channel.oauth_application

          channel.oauth_application
          klass = case channel.brand
          when 'amazon' then ::Amazon::SyncOrderJob
          when 'ebay' then ::SyncEbayOrderJob
          when 'walmart' then ::Walmart::SyncWalmartOrdersJob
          end
          klass&.perform_later(current_store.id, channel.oauth_application.id)
        end
        render_serialized_payload { {status: :ok} }
      end

      def calculate_weight
        line_items = ::Spree::LineItem.where(id: params[:lineItemIdArray])

        # Calculate the total weight of line items
        sum_weight = line_items.to_a.sum(0) do |item|
          # Determine weight in pounds and ounces based on whether the variant is a master variant or not
          lbs = item.variant.is_master ? item.product.lbs : item.variant.lbs
          oz = item.variant.is_master ? item.product.oz : item.variant.oz

          # Ensure weight values are valid and compute the total weight for the line item
          lbs && oz && (lbs > 0 || oz > 0) ? (lbs * 16 + oz) * item.quantity : 0.1
        end

        ordered_listings_data = ordered_listings_data(line_items)

        if ordered_listings_data
          sum_weight = ordered_listings_data.weight || 0.1
          filtered_ordered_listings_data = [{
            weight: ordered_listings_data.weight,
            length: ordered_listings_data.length,
            width: ordered_listings_data.width,
            height: ordered_listings_data.height
          }]
        end
        # Render a JSON response with the calculated weight
        render(json: {
          message: 'success',
          data: {
            sum_weight_lbs: (sum_weight / 16).floor,
            sum_weight_oz: (sum_weight % 16).floor(1),
            ordered_listings_data: filtered_ordered_listings_data
          }
        })
      end

      def validate_stock_location
        unless params[:shipment_ids]
          return render_error_payload('need provide parameter shipment_ids')
        end

        shipments = ::Spree::Shipment.where(id: params[:shipment_ids])
        distinct_addresses = shipments.map(&:stock_location_id).uniq
        if distinct_addresses.length == 1
          render(json: {message: 'success', data: {error: false}})
        else
          render(json: {
            message: 'success',
            data: {error: true}
          })
        end
      end

      def calculate_weight
        line_items = ::Spree::LineItem.where(id: params[:lineItemIdArray])

        # Calculate the total weight of line items
        sum_weight = line_items.to_a.sum(0) do |item|
          # Determine weight in pounds and ounces based on whether the variant is a master variant or not
          lbs = item.variant.is_master ? item.product.lbs : item.variant.lbs
          oz = item.variant.is_master ? item.product.oz : item.variant.oz

          # Ensure weight values are valid and compute the total weight for the line item
          lbs && oz && (lbs > 0 || oz > 0) ? (lbs * 16 + oz) * item.quantity : 0.1
        end

        ordered_listings_data = ordered_listings_data(line_items)

        if ordered_listings_data
          sum_weight = ordered_listings_data.weight || 0.1
          filtered_ordered_listings_data = [{
            weight: ordered_listings_data.weight,
            length: ordered_listings_data.length,
            width: ordered_listings_data.width,
            height: ordered_listings_data.height
          }]
        end
        # Render a JSON response with the calculated weight
        render(json: {
          message: 'success',
          data: {
            sum_weight_lbs: (sum_weight / 16).floor,
            sum_weight_oz: (sum_weight % 16).floor(1),
            ordered_listings_data: filtered_ordered_listings_data
          }
        })
      end

      def validate_stock_location
        unless params[:shipment_ids]
          return render_error_payload('need provide parameter shipment_ids')
        end

        shipments = ::Spree::Shipment.where(id: params[:shipment_ids])
        distinct_addresses = shipments.map(&:stock_location_id).uniq
        if distinct_addresses.length == 1
          render(json: {message: 'success', data: {error: false}})
        else
          render(json: {
            message: 'success',
            data: {error: true}
          })
        end
      end

      private

      def model_class
        Spree::Order
      end

      def collection
        @collection ||= scope.ransack(params[:filter]).result.order(created_at: :desc)
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def allowed_sort_attributes
        super.push(:available_on, :make_active_at, :total, :payment_total, :item_total, :shipment_total,
          :adjustment_total, :promo_total, :included_tax_total, :additional_tax_total,
          :item_count, :tax_total, :completed_at)
      end

      def authorize_spree_user
        case action_name
        when 'sync' then true
        else
          super
        end
      end

      def use_store_credit_service
        Spree::Api::Dependencies.platform_order_use_store_credit_service.constantize
      end

      def destroy_service
        Spree::Api::Dependencies.platform_order_destroy_service.constantize
      end

      def coupon_handler
        Spree::Api::Dependencies.platform_coupon_handler.constantize
      end

      def update_params
        params.tap { |p|
          p[:order][:bill_address_attributes] = (p[:order] || {}).delete(:bill_address)
          p[:order][:ship_address_attributes] = (p[:order] || {}).delete(:ship_address)
        }
      end

      def permitted_resource_params
        params.require(:order).permit(
          :email, :user_id, :envelope_fee, :complimentary_items,
          bill_address: [:firstname, :lastname, :address1, :address2, :city, :state_id, :state_name, :country_id, :zipcode, :company, :phone, :alternative_phone],
          ship_address: [:firstname, :lastname, :address1, :address2, :city, :state_id, :state_name, :country_id, :zipcode, :company, :phone, :alternative_phone],
          items: [:variant_id, :quantity]
        ).tap { |p|
          p[:bill_address_attributes] = p.delete(:bill_address) if p.key?(:bill_address)
          p[:ship_address_attributes] = p.delete(:ship_address) if p.key?(:ship_address)
          p[:line_items_attributes] = p.delete(:items) if p.key?(:items)
        }
      end

      def spree_permitted_attributes1
        super + [
          bill_address_attributes: Spree::Address.json_api_permitted_attributes,
          ship_address_attributes: Spree::Address.json_api_permitted_attributes,
          line_items_attributes: Spree::LineItem.json_api_permitted_attributes,
          payments_attributes: Spree::Payment.json_api_permitted_attributes + [
            source_attributes: Spree::CreditCard.json_api_permitted_attributes
          ],
          shipments_attributes: Spree::Shipment.json_api_permitted_attributes
        ]
      end

      def ordered_listings_data(line_items)
        ordered_listings_data = line_items.each_with_object({}) do |line_item, result|
          result[line_item.listing_id] ||= 0
          result[line_item.listing_id] += line_item.quantity
        end

        return if ordered_listings_data.empty? || ordered_listings_data[nil].present?

        ordered_listings_data = ordered_listings_data.map do |listing_id, total_quantity|
          {listing_id: listing_id, quantity: total_quantity}
        end

        ordered_listings_data.sort_by! { |item| item[:listing_id] }
        ordered_listings_json = ordered_listings_data.to_json
        ::Spree::OrderedListingsInfo.find_by(listing_quantities: ordered_listings_json)
      end
    end
  end
end
