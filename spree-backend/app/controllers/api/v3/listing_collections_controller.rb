# frozen_string_literal: true

module Api
  module V3
    class ListingCollectionsController < ResourceController
      protected

      def model_class
        Spree::ListingCollection
      end

      def permitted_resource_params
        params.require(:listing_collection).permit(:name, :description, :slug, listing_ids: [])
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end
    end
  end
end
