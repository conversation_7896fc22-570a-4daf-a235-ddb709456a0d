# frozen_string_literal: true

module Api
  module V3
    class StockMovementsController < ResourceController
      def index1
        @stock_movements = stock_location.stock_movements.recent
                             .includes(stock_item: {variant: :product})
                             .page(params[:page])

        render json: StockMovementSerializer.render(@stock_movements)
      end

      def new1
        @stock_movement = stock_location.stock_movements.build
        StockMovementSerializer.render(@stock_movements)
      end

      def create1
        @stock_movement = stock_location.stock_movements.build(stock_movement_params)
        if @stock_movement.save
          render json: StockMovementSerializer.render(@stock_movement), status: :created
        else
          render json: {errors: @stock_movement.errors.full_messages}, status: :unprocessable_entity
        end
      end

      private

      def model_class
        Spree::StockMovement
      end

      def parent_scope
        Spree::StockLocation.find(params[:stock_id]).stock_movements
      end

      def scope
        parent_scope
      end

      def stock_movement_params
        params.require(:stock_movement).permit(:quantity, :stock_item_id, :action)
      end
    end
  end
end
