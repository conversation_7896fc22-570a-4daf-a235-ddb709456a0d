# frozen_string_literal: true

module Api
  module V3
    class PaymentMethodsController < ResourceController
      def providers
        klass = Struct.new(:name)
        Spree::Gateway.providers
          .sort_by(&:name)
          .map { |provider| klass.new(provider.name) }
          .then { |collection|
            render_serialized_payload { Api::V3::GatewaySerializer.render(collection) }
          }
      end

      private

      def model_class
        Spree::PaymentMethod
      end

      def authorize_spree_user
        case action_name
        when 'providers' then true
        else
          super
        end
      end

      def spree_permitted_attributes
        preferred_attributes = []

        if action_name == 'update'
          resource.defined_preferences.each do |preference|
            preferred_attributes << "preferred_#{preference}".to_sym
          end
        end

        super + preferred_attributes
      end
    end
  end
end
