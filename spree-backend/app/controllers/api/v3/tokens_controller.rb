# frozen_string_literal: true

require 'active_support/core_ext/digest/uuid'

module Api
  module V3
    class TokensController < Doorkeeper::ApplicationMetalController
      include Spree::Core::ControllerHelpers::Store

      BASE_UUID = '4a69d03a-4fdd-47d5-b8b8-4a2ceb7a6f37'

      # OAuth 2.0 Token Introspection - https://datatracker.ietf.org/doc/html/rfc7662
      def introspect
        introspection = Doorkeeper::OAuth::TokenIntrospection.new(server, token)

        if introspection.authorized?
          uuid = Digest::UUID.uuid_v3(BASE_UUID, current_store.url)
          render(json: introspection.to_json.merge(store_id: uuid), status: :ok)
        else
          error = introspection.error_response
          headers.merge!(error.headers)
          render(json: error.body, status: error.status)
        end
      end

      protected

      def token
        @token ||=
          if params[:token_type_hint] == 'refresh_token'
            Doorkeeper.config.access_token_model.by_refresh_token(params['token'])
          else
            Doorkeeper.config.access_token_model.by_token(params['token']) ||
              Doorkeeper.config.access_token_model.by_refresh_token(params['token'])
          end
      end
    end
  end
end
