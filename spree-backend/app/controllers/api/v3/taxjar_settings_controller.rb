# frozen_string_literal: true

module Api
  module V3
    class TaxjarSettingsController < ResourceController
      def refresh
        ::SpreeTaxjar::Categories.refresh(current_store)
        render json: {status: :ok}
      end

      protected

      def model_class
        Spree::TaxjarSetting
      end

      def resource
        @resource ||= current_store.taxjar_setting || current_store.build_taxjar_setting
      end

      def permitted_resource_params
        params.require(:taxjar_settings).permit(
          :taxjar_api_key, :taxjar_enabled, :taxjar_debug_enabled, :taxjar_sandbox_environment_enabled
        )
      end
    end
  end
end
