# frozen_string_literal: true

module Api
  module V3
    class StripeTaxSettingsController < ResourceController
      protected

      def model_class
        Spree::StripeTaxSettings
      end

      def resource
        @resource ||= current_store.stripe_tax_setting || current_store.build_stripe_tax_setting
      end

      def permitted_resource_params
        params.require(:stripe_tax_settings).permit(:stripe_client_secret, :stripe_tax_enabled)
      end
    end
  end
end
