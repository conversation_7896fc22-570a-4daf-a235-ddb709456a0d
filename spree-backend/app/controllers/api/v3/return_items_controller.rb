# frozen_string_literal: true

module Api
  module V3
    class ReturnItemsController < ResourceController
      def update
        super
      end

      protected

      def model_class
        Spree::ReturnItem
      end

      def permitted_resource_params
        params.require(:return_item).permit(
          :exchange_variant_id,
          :customer_return_id,
          :reimbursement_id,
          :preferred_reimbursement_type_id,
          :override_reimbursement_type_id,
          :reason,
          :return_authorization_reason_id,
          :return_quantity,
          :pre_tax_amount
        )
      end
    end
  end
end
