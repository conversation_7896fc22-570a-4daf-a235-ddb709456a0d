# frozen_string_literal: true

module Api
  module V3
    class PromotionsController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      def create
        resource = model_class.new
        ensure_current_store(resource)
        result = Spree::Promotions::Update.call(
          promotion: resource,
          params: permitted_resource_params
        )
        render_result(result, :created)
      end

      def update
        ensure_current_store(resource)
        result = Spree::Promotions::Update.call(
          promotion: resource,
          params: permitted_resource_params
        )
        render_result(result)
      end

      private

      def model_class
        Spree::Promotion
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def scope_includes
        [:promotion_category, :promotion_rules, :promotion_actions]
      end

      def permitted_resource_params
        params.require(:promotion).permit(
          :kind, :name, :description, :code, :starts_at, :expires_at,
          :order_amount_required, :order_amount_min, :order_amount_operator_min,
          :order_amount_max, :order_amount_operator_max,
          :discount_type, :discount_value, :discount_free,
          :product_selection_type,
          :all_users, :first_order, :only_logged_users, :limit_one_per_user, :usage_limit,
          :min_selection_type, :min_quantity, :min_amount,
          :min_product_selection_type,
          :get_quantity, :get_product_selection_type,
          :comp_quantity, :comp_product_selection_type,
          :tier1_spend, :tier1_discount, :tier2_spend, :tier2_discount, :tier3_spend, :tier3_discount,
          :number_purchases, :all_countries,
          products_ids: [], collections_ids: [], users_ids: [],
          min_products_ids: [], min_collections_ids: [], get_products_ids: [], get_collections_ids: [],
          comp_products_ids: [], comp_collections_ids: [], country_ids: []
        )
          .tap { |p| p.delete(:kind) if action_name == 'update' }
      end

      def calculator_params
        [
          :preferred_flat_percent,
          :preferred_amount,
          :preferred_first_item,
          :preferred_additional_item,
          :preferred_max_items,
          :preferred_percent,
          :preferred_minimal_amount,
          :preferred_normal_amount,
          :preferred_discount_amount,
          :preferred_currency,
          :preferred_base_amount,
          :preferred_base_percent,
          {preferred_tiers: {}}
        ]
      end
    end
  end
end
