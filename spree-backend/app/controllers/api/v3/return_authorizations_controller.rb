# frozen_string_literal: true

module Api
  module V3
    class ReturnAuthorizationsController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = []
        if params[:order_id]
          order = ::Spree::Order.find(params[:order_id])
          unless order
            return render_error_payload('please provide a valid order id')
          end

          collection = Spree::ReturnAuthorization.where(order_id: params[:order_id]).ransack(params[:filter]).result.page(params[:page]).per(per_page)

          # To satisfy how nested attributes works we want to create placeholder ReturnItems for
          # any InventoryUnits that have not already been added to the ReturnAuthorization.
          new_object = order.return_authorizations.build
          all_inventory_units = order&.inventory_units
          associated_inventory_units = new_object&.return_items&.map(&:inventory_unit)
          unassociated_inventory_units = all_inventory_units - associated_inventory_units
          new_return_items = unassociated_inventory_units.map do |new_unit|
            new_object&.return_items&.build(inventory_unit: new_unit).tap(&:set_default_pre_tax_amount)
          end
          ris = (new_object&.return_items&.+ new_return_items).sort_by(&:inventory_unit_id).uniq
        else
          collection = Spree::ReturnAuthorization.all.ransack(params[:filter]).result.page(params[:page]).per(per_page)
        end

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render(json: {return_authorizations: serialize_collection(collection), form_return_items_for_create: format_result(ris)})
        # render_serialized_payload { serialize_collection(collection) }
      end

      def create
        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        if params[:return_authorization]
          if params[:return_authorization][:stock_location_id]
            stock_location = ::Spree::StockLocation.find(params[:return_authorization][:stock_location_id])
            unless stock_location
              return render_error_payload('please provide a valid stock location id')
            end
          end

          if params[:return_authorization][:return_authorization_reason_id]
            return_authorization_reason = ::Spree::ReturnAuthorizationReason.find(params[:return_authorization][:return_authorization_reason_id])
            unless return_authorization_reason&.active
              return render_error_payload('please provide a valid return authorization reason id')
            end
          end
        end

        return_authorization = order.return_authorizations.create!(permitted_resource_params)

        render_serialized_payload { serialize_collection(return_authorization) }
      rescue ActiveRecord::RecordInvalid => e
        render_error_payload(e.message)
      end

      def destroy
        if params[:order_id]
          order = ::Spree::Order.find(params[:order_id])
          unless order
            return render_error_payload('please provide a valid order id')
          end

          return_authorization = ::Spree::ReturnAuthorization.find(params[:id])
          unless return_authorization&.order_id == order.id
            return render_error_payload('please provide a valid order id and return authorization id')
          end
        end

        super
      end

      def update
        if params[:order_id]
          order = ::Spree::Order.find(params[:order_id])
          unless order
            return render_error_payload('please provide a valid order id')
          end

          return_authorization = ::Spree::ReturnAuthorization.find(params[:id])
          unless return_authorization&.order_id == order.id
            return render_error_payload('please provide a valid order id and return authorization id')
          end
        end

        super
      end

      def cancel
        return_authorization = ::Spree::ReturnAuthorization.find(params[:id])
        if params[:order_id]
          order = ::Spree::Order.find(params[:order_id])
          unless order
            return render_error_payload('please provide a valid order id')
          end

          unless return_authorization&.order_id == order.id
            return render_error_payload('please provide a valid order id and return authorization id')
          end
        end

        return_authorization.cancel!
        render_serialized_payload { {status: :ok} }
      end

      def send_label
        unless params[:order_id]
          return render_error_payload('need provide parameter order_id')
        end

        unless params[:id]
          return render_error_payload('need provide parameter id of return_authorization')
        end

        unless params[:customer_shipment_id]
          return render_error_payload('need provide parameter customer_shipment_id')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order_id')
        end

        return_authorization = ::Spree::ReturnAuthorization.find(params[:id])
        unless return_authorization
          return render_error_payload('please provide a valid id of return_authorization')
        end

        customer_shipment = ::Spree::CustomerShipment.find(params[:customer_shipment_id])
        unless customer_shipment
          return render_error_payload('please provide a valid customer_shipment_id')
        end

        unless return_authorization&.order_id == order.id
          return render_error_payload('please provide a valid order id and a vaid id of return_authorization')
        end

        unless customer_shipment&.return_authorization_id == return_authorization.id
          return render_error_payload('please provide a vaid id of return_authorization and a valid customer_shipment_id')
        end

        return_authorization.send_label(customer_shipment)
        render_serialized_payload { {status: :ok} }
      end

      protected

      def model_class
        Spree::ReturnAuthorization
      end

      def permitted_resource_params
        params.require(:return_authorization).permit(
          :create_label, # if create_label is "1", will create a spree_customer_shipments record
          :state,
          # :order_id,
          :memo,
          :stock_location_id,
          :return_authorization_reason_id,
          :refund_shipping_fee,
          :authorized,
          :custom_weight,
          return_items_attributes: [
            :inventory_unit_id,
            :reason,
            :return_authorization_reason_id,
            :return_quantity
          ]
        )
      end

      def format_result(ris)
        ris&.map do |r|
          {
            # Spree::ReturnItem column data
            id: r.id,
            return_authorization_id: r.return_authorization_id,
            inventory_unit_id: r.inventory_unit_id,
            exchange_variant_id: r.exchange_variant_id,
            created_at: r.created_at,
            updated_at: r.updated_at,
            pre_tax_amount: r.pre_tax_amount,
            included_tax_total: r.included_tax_total,
            additional_tax_total: r.additional_tax_total,
            reception_status: r.reception_status,
            acceptance_status: r.acceptance_status,
            customer_return_id: r.customer_return_id,
            reimbursement_id: r.reimbursement_id,
            acceptance_status_errors: r.acceptance_status_errors,
            preferred_reimbursement_type_id: r.preferred_reimbursement_type_id,
            override_reimbursement_type_id: r.override_reimbursement_type_id,
            resellable: r.resellable,
            reason: r.reason,
            return_authorization_reason_id: r.return_authorization_reason_id,
            # extral data
            editable: r&.inventory_unit&.shipped? && !r&.return_authorization&.customer_returned_items? && r&.reimbursement.nil?,
            return_quantity: r.return_quantity,
            quantity: r&.inventory_unit&.quantity,
            variant_name: r&.inventory_unit&.variant&.name,
            sku: r&.inventory_unit&.variant&.sku,
            inventory_unit_state: r&.inventory_unit&.state,
            eligible_exchange_variants: r&.eligible_exchange_variants&.map do |v|
              {
                id: v&.id,
                sku: v&.sku,
                variant_name: v&.name
              }
            end
          }
        end
      end
    end
  end
end
