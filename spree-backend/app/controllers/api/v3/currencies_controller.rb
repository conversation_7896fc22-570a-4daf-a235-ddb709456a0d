# frozen_string_literal: true

module Api
  module V3
    class CurrenciesController < ResourceController
      def index
        render_serialized_payload { currencies.to_json }
      end

      protected

      def model_class
        nil
      end

      def authorize_spree_user
      end

      def currencies
        ::Money::Currency.table.map do |_code, details|
          iso = details[:iso_code]
          {iso: iso, name: "#{details[:name]} (#{iso})", symbol: details[:symbol]}
        end
      end
    end
  end
end
