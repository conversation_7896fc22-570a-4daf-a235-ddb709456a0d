# frozen_string_literal: true

module Api
  module V3
    class FeaturedProductsController < ResourceController
      def batch
        Array.wrap(params.require(:product_ids))
          .map { |product_id|
            Spree::FeaturedProduct.where(product_id: product_id, store: current_store).first_or_create
          }
          .then { |collection|
            render_serialized_payload { serialize_collection(collection) }
          }
      end

      protected

      def model_class
        Spree::FeaturedProduct
      end

      def authorize_spree_user
        case action_name
        when 'batch' then true
        else
          super
        end
      end

      def allowed_sort_attributes
        []
      end

      def permitted_resource_params
        params.require(:featured_product).permit(:product_id, :position)
      end
    end
  end
end
