# frozen_string_literal: true

module Api
  module V3
    class PagesController < ResourceController
      rescue_from ActiveRecord::SubclassNotFound, with: :render_withtype_error

      protected

      def model_class
        Spree::CmsPage
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def permitted_resource_params
        params.require(:page).permit(
          :type, :locale, :title, :slug, :visible,
          :content, :meta_title, :meta_description,
          sections: [:id, :_destroy, :position, :visible, :type, :name, properties: {}]
        )
          .tap { |p| p[:type] = convert_page_type(p[:type]) if p[:type] }
          .tap { |p|
            p[:sections_attributes] = (p.delete(:sections) || []).each do |item|
              item[:type] = convert_section_type(item[:type]) if item[:type]
            end
          }
      end

      def convert_page_type(type)
        (type == 'home' ? 'Homepage' : "#{type.classify}Page")
          .then { |t| "Spree::Cms::Pages::#{t}" }
      end

      def convert_section_type(type)
        "Spree::Cms::Sections::#{type.classify}"
      end

      def render_withtype_error(exception)
        render_error_payload('The specified Page type is not supported', 422)
      end

      def collection
        @collection ||= scope.ransack(collection_filters).result
      end

      def collection_filters
        return {} unless params[:filter]

        params[:filter].dup.tap do |filter|
          filter['type_eq'] = convert_page_type(filter['type_eq']) if filter['type_eq']
        end
      end
    end
  end
end
