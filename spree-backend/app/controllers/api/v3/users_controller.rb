# frozen_string_literal: true

module Api
  module V3
    class UsersController < ResourceController
      rescue_from Spree::Core::DestroyWithOrdersError, with: :render_withorder_error
      rescue_from CanCan::AccessDenied, with: :render_access_denied

      before_action -> { params[:page] ||= 1 }, only: [:index, :items]
      before_action :check_role_change_restriction, only: :update

      def current
        render_serialized_payload { serialize_resource(spree_current_user) }
      end

      def addresses
        user = Spree::User.find(params[:id])
        addresses = Spree::Address.where(user_id: params[:id])
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?
        addresses = addresses.page(params[:page]).per(per_page)

        render(json: {
          use_billing: user.ship_address_id.present? && user.ship_address_id == user.bill_address_id,
          bill_address_id: user.bill_address_id,
          ship_address_id: user.ship_address_id,
          addresses: AddressSerializer.render(addresses)
        })
      end

      def orders
        params[:q] ||= {}
        @search = current_store.orders.reverse_chronological.ransack(params[:q].merge(user_id_eq: params[:id]))
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?
        orders = @search.result.page(params[:page]).per(per_page)
        render(json: OrderSerializer.render(orders))
      end

      def items
        current_store.line_items
          .joins(:order)
          .includes(variant: [:product, {option_values: :option_type}])
          .where(order: {user_id: params[:id]})
          .ransack(params[:q] || {})
          .tap { |s| s.sorts = 'created_at desc' if s.sorts.empty? }
          .result
          .page(params[:page])
          .per(params[:per_page])
          .then do |items|
            render_serialized_payload do
              {
                data: normalize_serialized_data(serialize_collection(items, LineItemSerializer, view: :user)),
                page: items.current_page,
                totalCount: items.total_count,
                totalPage: items.total_pages
              }
            end
          end
      end

      protected

      def authorize_spree_user
        case action_name
        when 'current' then true
        else
          super
        end
      end

      def model_class
        Spree::User
      end

      def serializer_params
        super
          .tap { |params| params[:view] = :full unless action_name == 'index' }
          .tap { |params| params[:view] = :current if action_name == 'current' }
      end

      def permitted_resource_params
        params.require(:user).permit(
          :email, :password, :password_confirmation,
          :first_name, :last_name, :ship_address_id, :bill_address_id, :phone_number,
          role_ids: [], store_ids: []
        ).tap do |params|
          params[:spree_role_ids] = params.delete(:role_ids) if params[:role_ids]
          params[:spree_store_ids] = params.delete(:store_ids) if params[:store_ids]

          if action_name == 'update' && params[:password_confirmation].blank?
            params.delete(:password)
          end

          if params[:spree_role_ids].present?
            role_users = params[:spree_role_ids].reject(&:blank?).map do |role_id|
              Spree::RoleUser.new(role_id: role_id, store_id: current_store.id)
            end
            params[:role_users] = role_users
            params.delete(:spree_role_ids)
          end
        end
      end

      def check_role_change_restriction
        return unless params[:user] && params[:user][:role_ids].present?

        target_user = Spree::User.find(params[:id])

        if target_user == spree_current_user && target_user.has_spree_role?('store_owner')
          raise CanCan::AccessDenied.new("Store owners cannot change their own role", :update, Spree::User)
        end
      end

      def render_access_denied(exception)
        render json: { error: exception.message }, status: :forbidden
      end

      def normalize_serialized_data(raw_data)
        raw_data.is_a?(String) ? JSON.parse(raw_data) : raw_data
      end
    end
  end
end
