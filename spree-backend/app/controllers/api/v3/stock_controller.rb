# frozen_string_literal: true

module Api
  module V3
    class StockController < ResourceController
      def alerts
        @stock_items = Spree::StockItem
                         .joins(:stock_location, variant: [:product])
                         .includes(:stock_location, variant: {product: [:master, :translations]})
                         .where(spree_stock_locations: {store: current_store})
                         .where(spree_variants: {track_inventory: true})
                         .where.not(spree_stock_items: {inventory_threshold: nil})
                         .where('spree_stock_items.count_on_hand < spree_stock_items.inventory_threshold')
        render(json: StockItemSerializer.render(@stock_items, view: :index))
      end

      def pull
        unless params[:order_numbers]
          return render_error_payload('need provide parameter order numbers')
        end

        @line_items = Spree::LineItem.includes(:order)
        @line_items = @line_items.includes(variant: [:product, stock_items: [:stock_location, :stock_location_section]])
        @line_items = @line_items.where(spree_orders: {number: params[:order_numbers]})
        @groups = @line_items.group_by(&:variant)
        render(json: format_result)
      end

      protected

      def model_class
        Spree::StockLocation
      end

      def scope_includes
        [:country, :state, :sections]
      end

      def authorize_spree_user
        case action_name
        when 'alerts'
          true
        when 'pull'
          true
        else
          super
        end
      end

      def permitted_resource_params
        params.require(:stock).permit(:name, :admin_name,
          :country_id, :zipcode, :state_id, :state_name, :city, :address1, :address2,
          :phone, :company_name,
          :active, :default, :backorderable_default, :propagate_all_variants)
      end

      def format_result
        variant_array = []
        @groups.each do |variant, line_items|
          product_image = variant.product&.images&.first
          product_image_url = if product_image
            main_app.url_for(product_image.url(variant.product))
          end
          product_image_id = product_image&.id
          product_name = variant.product.name
          product_id = variant.product&.id
          variant_id = variant.id
          variant_sku = variant.sku
          product_upc = variant.product.upc
          line_items_total_quantity = line_items.sum(&:quantity)
          line_item_array = []
          line_items.each do |line_item|
            next unless line_item.id

            line_item_id = line_item.id
            order_id = line_item.order.id
            order_number = line_item.order.number
            quantity = line_item.order.quantity
            line_item_array.push({
              line_item_id: line_item_id,
              order_id: order_id,
              order_number: order_number,
              quantity: quantity
            })
          end
          stock_location_array = []
          variant.stock_items.group_by(&:stock_location).each_with_index do |(stock_location, stock_items), _index|
            next unless stock_location&.id

            stock_location_id = stock_location&.id
            stock_location_name = stock_location&.name
            stock_location_section_array = []
            stock_items.each_with_index do |stock_item, _index|
              next unless stock_item.stock_location_section&.id

              stock_location_section_id = stock_item.stock_location_section&.id
              stock_location_section_name = stock_item.stock_location_section&.name
              stock_location_section_array.push({
                stock_location_section_id: stock_location_section_id,
                stock_location_section_name: stock_location_section_name
              })
            end
            stock_location_array.push({
              stock_location_id: stock_location_id,
              stock_location_name: stock_location_name,
              stock_location_sections: stock_location_section_array
            })
          end
          variant_array.push({
            product_image_id: product_image_id,
            product_image_url: product_image_url,
            product_name: product_name,
            product_id: product_id,
            variant_id: variant_id,
            variant_sku: variant_sku,
            product_upc: product_upc,
            line_items_total_quantity: line_items_total_quantity,
            line_items: line_item_array,
            stock_locations: stock_location_array
          })
        end
        variant_array
      end
    end
  end
end
