# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class CountriesController < ResourceController
        protected

        def serializer_params
          include_fields = params[:include]&.split(",") || []
          super.tap { |params|
            params[:include] = include_fields
            params[:view] = :full if ["show"].include?(action_name)
          }
        end

        private

        def spree_permitted_attributes
          []
        end

        def resource
          return current_store.default_country if params[:iso] == "default"

          scope.find_by(iso: params[:iso]&.upcase) ||
            scope.find_by(id: params[:iso]&.upcase) ||
            scope.find_by(iso3: params[:iso]&.upcase)
        end

        def resource_serializer
          ::Api::V3::Storefront::CountrySerializer
        end

        def collection_serializer
          ::Api::V3::Storefront::CountrySerializer
        end

        def collection_finder
          Spree::Api::Dependencies.storefront_country_finder.constantize
        end

        def model_class
          Spree::Country
        end

        # by default we want to return all countries on a single page
        def set_default_per_page
          params[:per_page] ||= Spree::Country.count
        end
      end
    end
  end
end
