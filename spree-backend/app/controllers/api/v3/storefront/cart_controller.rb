# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class CartController < ::Api::V3::Storefront::ResourceController
        include ::Spree::Admin::SubscribersHelper
        include Spree::Api::V2::Storefront::OrderConcern
        include Spree::Api::V2::CouponCodesHelper
        include Spree::Api::V2::Storefront::MetadataControllerConcern

        before_action :ensure_valid_product, only: [:create, :add_item, :show]
        before_action :ensure_valid_metadata, only: %i[create add_item]
        before_action :ensure_order, except: %i[create associate]
        before_action :load_variant, only: :add_item
        before_action :load_listing, only: :add_item
        before_action :require_spree_current_user, only: :associate

        def create
          spree_authorize! :create, Spree::Order

          create_cart_params = {
            user: spree_current_user,
            store: current_store,
            currency: current_currency,
            public_metadata: create_params[:public_metadata],
            private_metadata: create_params[:private_metadata],
          }

          order = spree_current_order if spree_current_order.present?
          order ||= create_service.call(create_cart_params).value

          render_serialized_payload(201) { serialize_resource(order) }
        end

        def add_item
          spree_authorize! :update, spree_current_order, order_token
          spree_authorize! :show, @variant

          params_with_options = add_item_params.to_h
              params_with_options[:options] ||= {}
              params_with_options[:options][:cart] = params[:cart]
              params_with_options[:options][:listing_id] = @listing.id

          result = add_item_service.call(
            order: spree_current_order,
            variant: @variant,
            quantity: add_item_params[:quantity],
            public_metadata: add_item_params[:public_metadata],
            private_metadata: add_item_params[:private_metadata],
            options: params_with_options[:options],
          )

          check_local_pickup_eligibility
          if result.success? && spree_current_order&.email.present?
            subscriber = ::Spree::Subscriber.find_or_create_by(email: spree_current_order&.email)
            process_subscriber_action(subscriber, 'added item to the cart', spree_current_order) if subscriber
          end

          render_order(result)
        end

        def remove_line_item
          spree_authorize! :update, spree_current_order, order_token

          remove_line_item_service.call(
            order: spree_current_order,
            line_item: line_item
          )
          check_local_pickup_eligibility
          render_serialized_payload { serialized_current_order }
        end

        def empty
          spree_authorize! :update, spree_current_order, order_token
          result = empty_cart_service.call(order: spree_current_order)
          if result.success?
            render_serialized_payload { serialized_current_order }
          else
            render_error_payload(result.error)
          end
        end

        def destroy
          spree_authorize! :update, spree_current_order, order_token

          result = destroy_cart_service.call(order: spree_current_order)

          if result.success?
            head 204
          else
            render_error_payload(result.error)
          end
        end

        def set_quantity
          return render_error_item_quantity unless params[:quantity].to_i > 0
          spree_authorize! :update, spree_current_order, order_token
          result = set_item_quantity_service.call(order: spree_current_order, line_item: line_item, quantity: params[:quantity])

          render_order(result)
        end

        def show
          spree_authorize! :show, spree_current_order, order_token

          render_serialized_payload { serialized_current_order }
        end

        def apply_coupon_code
          spree_authorize! :update, spree_current_order, order_token

          spree_current_order.coupon_code = params[:coupon_code]
          result = coupon_handler.new(spree_current_order).apply

          if result.error.blank?
            render_serialized_payload { serialized_current_order }
          else
            render_error_payload(result.error)
          end
        end

        def remove_coupon_code
          spree_authorize! :update, spree_current_order, order_token

          coupon_codes = select_coupon_codes

          return render_error_payload(I18n.t('spree.api.v2.cart.no_coupon_code')) if coupon_codes.empty?

          result_errors = coupon_codes.count > 1 ? select_errors(coupon_codes) : select_error(coupon_codes)

          if result_errors.blank?
            render_serialized_payload { serialized_current_order }
          else
            render_error_payload(result_errors)
          end
        end

        def estimate_shipping_rates
          spree_authorize! :show, spree_current_order, order_token

          result = estimate_shipping_rates_service.call(order: spree_current_order, country_iso: params[:country_iso])

          if result.error.blank?
            render_serialized_payload { serialize_estimated_shipping_rates(result.value) }
          else
            render_error_payload(result.error)
          end
        end

        def associate
          guest_order_token = params[:guest_order_token]
          guest_order = ::Spree::Api::Dependencies.storefront_current_order_finder.constantize.new.execute(
            store: current_store,
            user: nil,
            token: guest_order_token,
            currency: current_currency
          )

          return render_error_payload("guest order is empty") unless guest_order.present?

          spree_authorize! :update, guest_order, guest_order_token

          result = associate_service.call(guest_order: guest_order, user: spree_current_user)

          if result.success?
            render_serialized_payload { serialize_resource(guest_order) }
          else
            render_error_payload(result.error)
          end
        end

        def change_currency
          spree_authorize! :update, spree_current_order, order_token

          return render json: { errors: "Need to provide new_currency prameter." }, status: :unprocessable_entity if params[:new_currency].nil?

          result = change_currency_service.call(order: spree_current_order, new_currency: params[:new_currency])

          render_order(result)
        end

        private

        def resource_serializer
          ::Api::V3::Storefront::CartSerializer
        end

        def create_service
          Spree::Api::Dependencies.storefront_cart_create_service.constantize
        end

        def add_item_service
          Spree::Api::Dependencies.storefront_cart_add_item_service.constantize
        end

        def empty_cart_service
          Spree::Api::Dependencies.storefront_cart_empty_service.constantize
        end

        def destroy_cart_service
          Spree::Api::Dependencies.storefront_cart_destroy_service.constantize
        end

        def set_item_quantity_service
          Spree::Api::Dependencies.storefront_cart_set_item_quantity_service.constantize
        end

        def remove_line_item_service
          Spree::Api::Dependencies.storefront_cart_remove_line_item_service.constantize
        end

        def coupon_handler
          Spree::Api::Dependencies.storefront_coupon_handler.constantize
        end

        def estimate_shipping_rates_service
          Spree::Api::Dependencies.storefront_cart_estimate_shipping_rates_service.constantize
        end

        def associate_service
          Spree::Api::Dependencies.storefront_cart_associate_service.constantize
        end

        def change_currency_service
          Spree::Api::Dependencies.storefront_cart_change_currency_service.constantize
        end

        def line_item
          @line_item ||= spree_current_order.line_items.find(params[:line_item_id])
        end

        def load_variant
          @variant = current_store.variants.find_by(id: add_item_params[:variant_id])
          return render json: {error: "variant id must exists"}, status: :unprocessable_entity if @variant.nil?
        end

        def render_error_item_quantity
          render json: { error: I18n.t(:wrong_quantity, scope: 'spree.api.v2.cart') }, status: 422
        end

        def estimate_shipping_rates_serializer
          Spree::Api::Dependencies.storefront_estimated_shipment_serializer.constantize
        end

        def serialize_estimated_shipping_rates(shipping_rates)
          estimate_shipping_rates_serializer.new(
            shipping_rates,
            params: serializer_params
          ).serializable_hash
        end

        def create_params
          params.permit(:quantity, :variant_id, public_metadata: {}, private_metadata: {}, options: {})
        end

        def add_item_params
          params.require(:cart).permit(:order_token, :listing_id, :quantity, :variant_id, public_metadata: {}, private_metadata: {}, options: {})
        end

        # added by axel
        def load_listing
          @listing = @variant.product.listings.find_by(id: params[:listing_id])
          return render json: {error: "listing id must exists"}, status: :unprocessable_entity if @listing.nil?

          return render json: {error: "Unpublished Listing"}, status: :unprocessable_entity unless @listing.Active?
        end

        def check_local_pickup_eligibility
          local_pickup = spree_current_order.line_items.reload.map do |line_item|
            line_item.listing&.shipping_category&.shipping_methods&.any?(&:local_pickup)
          end
          # rubocop:disable Rails/SkipsModelValidations
          spree_current_order.update_columns(local_pickup: local_pickup.exclude?(false))
          # rubocop:enable Rails/SkipsModelValidations
        end

        def update_quantity_as_per_pack_size
          # listing = params[:action] == "add_item" ? @listing : line_item.listing
          # params[:quantity] *= listing.pack_size_value

          pack_size = params[:action] == "add_item" ? @listing.pack_size_value : line_item.pack_size
          params[:quantity] *= pack_size
        end

        def ensure_valid_product
          if spree_current_user.present? || @variant || spree_current_order.present?
            if @variant
              has_active_storefront_listing = @variant.product&.listings&.active&.for_brand('storefront')&.any?

              unless has_active_storefront_listing && @variant.product.active?
                render(json: { error: "Unpublished product" }, status: :unprocessable_entity)
              end
            elsif spree_current_order.present?
              spree_current_order.line_items.each do |li|
                added_product = li.product
                has_active_storefront_listing = added_product&.listings&.active&.for_brand('storefront')&.any?

                next if has_active_storefront_listing && added_product.active?

                params[:line_item_id] = li.id

                spree_authorize!(:update, spree_current_order, order_token)
                remove_line_item_service.call(
                  order: spree_current_order,
                  line_item: line_item,
                )
                render(json: { error: "Unpublished product." }, status: :unprocessable_entity)
              end
            end
          end
        end
        # end added by axel
      end
    end
  end
end
