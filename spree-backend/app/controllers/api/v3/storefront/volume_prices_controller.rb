# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class VolumePricesController < ResourceController
        def index
          check_required_params
          variant_id = params.dig("variant_id")
          listing_id = params.dig("listing_id")

          variant = Spree::Variant.find_by(id: variant_id)
          raise "Variant not found" if variant.blank?

          volume_prices = Spree::VolumePrice.where(variant_id: variant.id, listing_id: listing_id).order(position: :asc)

          render_serialized_payload { serialize_resource(volume_prices) }
        rescue StandardError => e
          render_error_payload(e.message)
        end

        def model_class
          Spree::VolumePrice
        end

        def scope(skip_cancancan: false)
          base_scope = model_class
          # base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
          base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == "index"
          base_scope = model_class.include?(Spree::TranslatableResource) ? base_scope.i18n : base_scope
          base_scope
        end

        private

        def spree_permitted_attributes
          []
        end

        def check_required_params
          missing_params = []

          missing_params << "variant_id" if params.dig("variant_id").blank?
          missing_params << "listing_id" if params.dig("listing_id").blank?

          raise "Missing required params " + missing_params.join(", ") if missing_params.present?
        end
      end
    end
  end
end
