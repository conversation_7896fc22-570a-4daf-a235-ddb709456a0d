# frozen_string_literal: true

module Api
  module V3
    module Storefront
      module Account
        class AddressesController < ::Api::V3::Storefront::ResourceController
          include Spree::BaseHelper

          before_action :require_spree_current_user

          def create
            spree_authorize! :create, model_class

            result = create_service.call(user: spree_current_user, address_params: address_params)
            render_result(result, 201)
          end

          def update
            spree_authorize! :update, resource

            result = update_service.call(address: resource, address_params: address_params)
            render_result(result)
          end

          def destroy
            spree_authorize! :destroy, resource

            if resource.destroy
              head 204
            else
              render_error_payload(resource.errors)
            end
          end

          private

          def spree_permitted_attributes
            []
          end

          def scope
            super.where(user: spree_current_user, country: available_countries).not_deleted
          end

          def model_class
            Spree::Address
          end

          def collection_finder
            Spree::Api::Dependencies.storefront_address_finder.constantize
          end

          def create_service
            Spree::Api::Dependencies.storefront_address_create_service.constantize
          end

          def update_service
            Spree::Api::Dependencies.storefront_address_update_service.constantize
          end

          def address_params
            params.require(:address).permit(permitted_address_attributes)
          end
        end
      end
    end
  end
end
