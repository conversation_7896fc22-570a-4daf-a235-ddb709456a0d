module Api
  module V3
    module Storefront
      class NotificationsController < ResourceController
        before_action :require_spree_current_user, unless: :guest_user

        def model_class
          Spree::VolumePrice
        end

        def create
          check_required_params

          variant = Spree::Variant.find_by(id: params[:variant_id])

          raise "Product not found" unless variant
          raise "Email is required for guest users" if customer_email.blank?

          notification = ::Spree::ProductNotification.find_or_initialize_by(
            user: spree_current_user,
            variant: variant,
            email: customer_email,
          )

          notification.notify = true
          notification.action = params[:action] if params[:action].present?
          notification.save!

          if Spree::NotifyMeMailer.notify_me_email(customer_email, variant, current_store).deliver
            render(json: { message: "Create notification success" }, status: 201)
          else
            raise "Unable to Notify Product Owner, Try Again Later"
          end
        rescue StandardError => e
          render_error_payload(e.message)
        end

        private

        def customer_email
          spree_current_user&.email || params[:email]
        end

        def spree_permitted_attributes
          []
        end

        def guest_user
          params[:email].present?
        end

        def render_success
          render json: { message: "success" }.to_json
        end

        def render_error(message)
          render json: { errors: message }, status: :unprocessable_entity
        end

        def check_required_params
          missing_params = []

          missing_params << "variant_id" if params.dig("variant_id").blank?

          raise "Missing required params " + missing_params.join(", ") if missing_params.present?
        end
      end
    end
  end
end
