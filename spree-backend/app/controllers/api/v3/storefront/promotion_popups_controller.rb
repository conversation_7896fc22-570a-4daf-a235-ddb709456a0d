# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class PromotionPopupsController < ResourceController
        private

        def model_class
          Spree::PromotionPopup
        end

        def resource_serializer
          Api::V3::Storefront::PromotionPopupSerializer
        end

        def scope_includes
          [:template]
        end

        def spree_permitted_attributes
          []
        end
      end
    end
  end
end
