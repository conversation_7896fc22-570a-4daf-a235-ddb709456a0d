# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class ResourceController < BaseController
        include Api::V3::Caching

        after_action :set_pagination_headers, only: :index

        def index
          render_serialized_payload do
            Rails.cache.fetch(collection_cache_key(paginated_collection), collection_cache_opts) do
              serialize_collection(paginated_collection)
            end
          end
        end

        def show
          render_serialized_payload { serialize_resource(resource) }
        end

        def create
          resource = model_class.new(permitted_resource_params)
          ensure_current_store(resource)

          if resource.save
            render_serialized_payload(:created) { serialize_resource(resource) }
          else
            render_error_payload(resource.errors)
          end
        end

        def destroy
          if resource.destroy
            head :no_content
          else
            render_error_payload(resource.errors)
          end
        end

        def update
          resource.assign_attributes(permitted_resource_params)
          ensure_current_store(resource)

          if resource.save!
            resource.reload
            render_serialized_payload { serialize_resource(resource) }
          else
            render_error_payload(resource.errors)
          end
        end

        protected

        def access_denied(exception)
          access_denied_401(exception)
        end

        # Scope methods
        def parent_scope
          model_class.for_store(current_store)
        end

        def scope(skip_cancancan: false)
          base_scope = parent_scope
          # base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
          base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == 'index'
          base_scope = model_class.include?(Spree::TranslatableResource) ? base_scope.i18n : base_scope
          base_scope
        end

        def scope_includes
          []
        end

        # overwriting to utilize ransack gem for filtering
        # https://github.com/activerecord-hackery/ransack#search-matchers
        def collection
          @collection ||= scope.ransack(params[:filter]).result
        end

        def sorted_collection
          sorter_class = defined?(collection_sorter) ? collection_sorter : Spree::BaseSorter
          @sorted_collection ||= sorter_class.new(collection, params, allowed_sort_attributes).call
        end

        def paginated_collection
          # @paginated_collection ||= collection_paginator.new(sorted_collection, params).call

          @paginated_collection ||= begin
            collection = sorted_collection
            if params[:page]
              collection = sorted_collection.page(params[:page]).per(params[:per_page])
            end
            collection
          end
        end

        def resource
          @resource ||= scope.find(params[:id])
        end

        # Serializer methods
        def resource_serializer
          serializer_base_name = model_class.to_s.sub('Spree::', '')
          "::Api::V3::Storefront::#{serializer_base_name}Serializer".constantize
        end

        def collection_serializer
          resource_serializer
        end

        def serialize_collection(collection, serializer = collection_serializer, options = {})
          serializer.render(collection, **serializer_params.merge(options))
        end

        def serialize_resource(resource, serializer = resource_serializer, options = {})
          options = options.merge(base_url: request.base_url)
          serializer.render(resource, **serializer_params.merge(options))
        end

        def spree_permitted_attributes
          store_ids = if model_class.method_defined?(:stores)
            [{store_ids: []}]
          else
            []
          end

          model_class.json_api_permitted_attributes + store_ids + metadata_params
        end

        def default_sort_attributes
          [:id, :name, :slug, :number, :position, :updated_at, :created_at, :deleted_at]
        end

        def allowed_sort_attributes
          (default_sort_attributes << spree_permitted_attributes).uniq.compact
        end

        def set_pagination_headers
          return if model_class.nil?

          scope = paginated_collection
          return unless scope.respond_to?(:total_pages)

          # request_params = request.query_parameters
          # url_without_params = request.original_url.slice(0..(request.original_url.index('?') - 1)) unless request_params.empty?
          # url_without_params ||= request.original_url

          # page = {}
          # page[:first] = 1 if scope.total_pages > 1 && !scope.first_page?
          # page[:last] = scope.total_pages  if scope.total_pages > 1 && !scope.last_page?
          # page[:next] = scope.current_page + 1 unless scope.last_page?
          # page[:prev] = scope.current_page - 1 unless scope.first_page?

          # pagination_links = []
          # page.each do |k, v|
          #   new_request_hash = request_params.merge({page: v})
          #   pagination_links << "<#{url_without_params}?#{new_request_hash.to_param}>; rel=\"#{k}\""
          # end
          # headers['Link'] = pagination_links.join(', ')
          headers['X-Page'] = scope.current_page
          headers['X-Total-Pages'] = scope.total_pages
          headers['X-Total-Count'] = scope.total_count
        end

        def finder_params
          params.merge(
            store: current_store,
            locale: current_locale,
            currency: current_currency,
            user: spree_current_user
          )
        end
      end
    end
  end
end
