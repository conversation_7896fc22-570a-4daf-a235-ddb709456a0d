# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class WishlistsController < ::Api::V3::Storefront::ResourceController
        before_action :require_spree_current_user, except: [:show]
        before_action :ensure_valid_quantity, only: [:add_item, :set_item_quantity]
        
        def model_class
          Spree::Wishlist
        end

        def show
          spree_authorize! :show, resource
          super
        end

        def destroy
          authorize! :destroy, resource

          if resource.destroy
            head 204
          else
            render_error_payload("The wishlist could not be destroyed")
          end
        end

        def default
          spree_authorize! :create, Spree::Wishlist

          @default_wishlist = spree_current_user.default_wishlist_for_store(current_store)

          render_serialized_payload { serialize_resource(@default_wishlist) }
        end

        def add_item
          spree_authorize! :create, ::Spree::WishedItem

          return render json: { errors: "Need to provide variant_id prameter." }, status: :unprocessable_entity if params[:variant_id].nil?
          return render json: { errors: "Need to provide listing_id prameter." }, status: :unprocessable_entity if params[:listing_id].nil?
          variant = ::Spree::Variant.find(params[:variant_id])
          listing = ::Spree::Listing.find(params[:listing_id])
          
          if resource.wished_items.present? && resource.wished_items.detect { |wv| wv.variant_id.to_s == params[:variant_id].to_s && wv.listing_id.to_s == params[:listing_id] }.present?
            @wished_item = resource.wished_items.detect { |wi| wi.variant_id.to_s == params[:variant_id].to_s }
            @wished_item.quantity = params[:quantity]
            @wished_item.listing_id = params[:listing_id]
          else
            @wished_item = ::Spree::WishedItem.new(params.permit(:quantity, :variant_id, :listing_id))
            @wished_item.wishlist = resource
            @wished_item.save
          end
          
          resource.reload
          
          if @wished_item.persisted?
            render_serialized_payload { serialize_resource(@wished_item, "::Api::V3::Storefront::WishedItemSerializer".constantize) }
          else
            render_error_payload(@wished_item.errors)
          end
        end
        
        def set_item_quantity
          spree_authorize! :update, wished_item
          
          wished_item.update(params.permit(:quantity))

          if wished_item.errors.empty?
            render_serialized_payload { serialize_resource(wished_item, "::Api::V3::Storefront::WishedItemSerializer".constantize) }
          else
            render_error_payload(wished_item.errors)
          end
        end

        def remove_item
          spree_authorize! :destroy, wished_item

          if wished_item.destroy
            head 204
          else
            render_error_payload(resource.errors)
          end
        end

        private

        protected

        def resource
          @resource ||= scope.find_by(token: params[:id])
        end

        def scope
          @scope ||= Spree::Wishlist.for_store(current_store).where(user: spree_current_user)
        end

        def resource_serializer
          ::Api::V3::Storefront::WishlistSerializer
        end

        def collection_serializer
          resource_serializer
        end

        def wished_item
          @wished_item ||= resource.wished_items.find(params[:item_id])
        end

        def render_error_item_quantity
          render json: { errors: "Wrong Quantity." }, status: 422
        end

        def ensure_valid_quantity
          return render_error_item_quantity if params[:quantity].present? && params[:quantity].to_i <= 0

          params[:quantity] = if params[:quantity].present?
                                params[:quantity].to_i
                              else
                                1
                              end
        end
      end
    end
  end
end