# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class BlogPostsController < ::Api::V3::Storefront::ResourceController
        def model_class
          Spree::BlogPost
        end

        def like
          return render json: { errors: "You are not authorized to perform this action." }, status: :unprocessable_entity if spree_current_user.nil?

          return render_serialized_payload { serialize_resource(resource) } if resource.likes.exists?(user_id: spree_current_user.id)

          like = resource.likes.new(user: spree_current_user)
          if like.save
            resource.increment!(:likes_count)
            render_serialized_payload { serialize_resource(resource) }
          else
            render json: { errors: like.errors.full_messages }, status: :unprocessable_entity
          end
        end

        def unlike
          return render json: { errors: "You are not authorized to perform this action." }, status: :unprocessable_entity if spree_current_user.nil?

          like = resource.likes.find_by(user: spree_current_user)
          if like
            like.destroy
            resource.decrement!(:likes_count)
            render_serialized_payload { serialize_resource(resource) }
          else
            render_serialized_payload { serialize_resource(resource) }
          end
        end

        private

        def spree_permitted_attributes
          []
        end

        protected

        def resource
          @resource ||= scope.find_by(slug: params[:id]) ||
                        ::Spree::BlogPost.find_by(slug: params[:id])
        end

        def scope
          @scope ||= Spree::BlogPost.for_store(current_store).friendly.visible.published
        end

      end
    end
  end
end