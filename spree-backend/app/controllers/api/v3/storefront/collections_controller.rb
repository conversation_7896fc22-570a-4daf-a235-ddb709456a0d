# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class CollectionsController < BaseController
        def show
          render_serialized_payload do
            ::Api::V3::Storefront::CollectionSerializer.render(resource)
          end
        end

        private

        def resource
          @resource ||= if params[:id].to_i.to_s == params[:id]
            Spree::ListingCollection.find(params[:id])
          else
            Spree::ListingCollection.find_by!(slug: params[:id])
          end
        end
      end
    end
  end
end
