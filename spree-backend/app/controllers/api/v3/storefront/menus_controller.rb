# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class MenusController < ResourceController
        protected

        def scope
          super.by_locale(I18n.locale)
        end

        def collection
          @collection ||= if defined?(collection_finder)
              collection_finder.new(scope: scope, params: finder_params).execute
            else
              scope
            end
        end

        private

        def spree_permitted_attributes
          []
        end

        def resource_serializer
          ::Api::V3::Storefront::MenuSerializer
        end

        def collection_serializer
          ::Api::V3::Storefront::MenuSerializer
        end

        def collection_finder
          Spree::Api::Dependencies.storefront_menu_finder.constantize
        end

        def model_class
          Spree::Menu
        end
      end
    end
  end
end
