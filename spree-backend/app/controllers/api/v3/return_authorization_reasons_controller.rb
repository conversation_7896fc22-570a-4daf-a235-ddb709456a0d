# frozen_string_literal: true

module Api
  module V3
    class ReturnAuthorizationReasonsController < ResourceController
      def index
        collection = Spree::ReturnAuthorizationReason.all
        render_serialized_payload { serialize_collection(collection) }
      end

      protected

      def model_class
        Spree::ReturnAuthorizationReason
      end

      def permitted_resource_params
        params.require(:return_authorization_reason).permit(
          :name,
          :active,
          :mutable
        )
      end
    end
  end
end
