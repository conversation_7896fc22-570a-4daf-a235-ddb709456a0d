# frozen_string_literal: true

module Api
  module V3
    class ShippingMethodsController < ResourceController
      protected

      def model_class
        Spree::ShippingMethod
      end

      def permitted_resource_params
        params.require(:shipping_method).permit(:name, :display_on,
          :admin_name, :code, :tracking_url,
          :local_pickup, :pickup_country_id, :pickup_state_id, :pickup_zip, :address_details,
          :tax_category_id,
          zone_ids: [],
          shipping_category_ids: [],
          calculator_attributes: [:type]).tap { |p|
          p[:pickup_country] = p.delete(:pickup_country_id) if p.key?(:pickup_country_id)
          p[:pickup_state] = p.delete(:pickup_state_id) if p.key?(:pickup_state_id)
          p[:address_details] = p.delete(:pickup_address) if p.key?(:pickup_address)
        }
      end
    end
  end
end
