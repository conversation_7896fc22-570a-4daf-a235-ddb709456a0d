# frozen_string_literal: true

module Api
  module V3
    class InvoiceSettingsController < ResourceController
      protected

      def model_class
        Spree::InvoiceSetting
      end

      # FIXME: MM: Strange logic in API mode: create record on GET requests
      def resource
        @resource ||= (current_store.invoice_setting || current_store.build_invoice_setting)
                        .tap { |settings| settings.country ||= current_store.default_country }
      end

      def permitted_resource_params
        params.require(:invoice_settings).permit(
          :name, :address, :city, :zipcode, :state_id, :state_name, :country_id, :message, :logo
        )
      end
    end
  end
end
