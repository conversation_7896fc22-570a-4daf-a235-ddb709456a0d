# frozen_string_literal: true

module Api
  module V3
    class AxelStripeController < ::Spree::Api::V2::BaseController

      def webhook
        require "stripe"

        Stripe.api_key = ENV['AXEL_STRIPE_API_KEY']
        Stripe.api_version = '2025-04-30.basil'

        payload = request.body.read
        event = nil

        begin
          event = Stripe::Event.construct_from(
            JSON.parse(payload, symbolize_names: true),
          )
        rescue JSON::ParserError => e
          Rails.logger.error("Webhook error while parsing basic request. #{e.message})")
          return render_error_payload("Invalid payload", 400)
        end
        # Check if webhook signing is configured.
        endpoint_secret = ENV['AXEL_STRIPE_WEBHOOK_SECRET']
        if endpoint_secret.present?
          # Retrieve the event by verifying the signature using the raw body and secret.
          signature = request.env['HTTP_STRIPE_SIGNATURE']
          begin
            event = Stripe::Webhook.construct_event(
              payload, signature, endpoint_secret
            )
          rescue Stripe::SignatureVerificationError => err
            Rails.logger.error("Webhook signature verification failed. #{err.message}")
            return render_error_payload("Invalid signature", 400)
          end
        end

        # Handle the event
        case event.type
        when "customer.subscription.updated"
          subscription = event.data.object
          # Handle both cancellations and plan changes
          if subscription.status == 'canceled' || subscription.cancel_at_period_end
            handle_subscription_cancellation(subscription)
          else
            handle_subscription_updated(subscription)
          end
        when "customer.subscription.deleted"
          subscription = event.data.object
          handle_subscription_deleted(subscription)
        when "invoice.payment_succeeded"
          invoice = event.data.object
          handle_invoice_payment_succeeded(invoice)
        when "invoice.payment_failed"
          invoice = event.data.object
          handle_invoice_payment_failed(invoice)
        else
          Rails.logger.info("Unhandled event type: #{event.type}")
        end
        render(json: { message: "stripe webhook handled successfully" }, status: :ok)
      end

      private

      def handle_subscription_cancellation(subscription)
        Rails.logger.info("Subscription cancellation detected: #{subscription.id}")

        # Find the organisation subscription by external_id
        organisation_subscription = Axel::OrganisationSubscription.find_by(external_id: subscription.id)
        return unless organisation_subscription

        # Mark the subscription as canceled or update cancel_at_period_end flag
        if subscription.status == 'canceled'
          organisation_subscription.update(
            status: 'canceled',
          )
        elsif subscription.cancel_at_period_end
          # Still active but will be canceled at period end
          organisation_subscription.update(
            canceled_at: Time.at(subscription.cancel_at)
          )
        end
      end

      def handle_subscription_updated(subscription)
        Rails.logger.info("Subscription updated: #{subscription.id}")

        # Find the organisation subscription by external_id
        organisation_subscription = Axel::OrganisationSubscription.find_by(external_id: subscription.id)
        return unless organisation_subscription

        # Get the subscription item and price ID
        subscription_item = subscription.items.data.first
        price_id = subscription_item.price.id

        # Find the plan that matches this price ID
        new_plan = Axel::Plan.find_by(stripe_price_id: price_id)

        if new_plan && new_plan.id != organisation_subscription.plan_id
          Rails.logger.info("Plan changed for subscription #{subscription.id} to #{new_plan.name}")


          # Update the subscription with the new plan
          organisation_subscription.update(
            plan_id: new_plan.id,
            status: subscription.status,
            start_date: Time.at(subscription_item.current_period_start),
            end_date: Time.at(subscription_item.current_period_end)
          )
        end
      end

      def handle_subscription_deleted(subscription)
        Rails.logger.info("Subscription deleted: #{subscription.id}")

        # Find the organisation subscription by external_id
        organisation_subscription = Axel::OrganisationSubscription.find_by(external_id: subscription.id)
        return unless organisation_subscription

        # Mark the subscription as canceled (final state)
        # This should be rare but handles cases where the subscription is forcefully deleted in Stripe
        organisation_subscription.update(
          status: 'canceled',
          canceled_at: Time.current
        )

        Rails.logger.info("Subscription #{subscription.id} has been forcefully deleted in Stripe and marked as canceled")
      end

      def handle_invoice_payment_succeeded(invoice)
        Rails.logger.info("Invoice payment succeeded: #{invoice.id}")

        if invoice.parent.present? && invoice.parent.type == 'subscription_details'
          subscription_id = invoice.parent.subscription_details.subscription
        else
          Rails.logger.error("Invoice #{invoice.id} does not have expected subscription details")
          return
        end

        update_params = {}

        organisation_subscription = Axel::OrganisationSubscription.find_by(external_id: subscription_id)
        if organisation_subscription.nil?
          # first purchase, find by metadata
          subscription = Stripe::Subscription.retrieve(subscription_id)
          organisation_subscription = Axel::OrganisationSubscription.find_by(id: subscription.metadata.organisation_subscription_id)
          if organisation_subscription.nil?
            Rails.logger.error("Subscription #{subscription_id} not found in Axel")
            return
          end

          update_params[:external_id] = subscription_id
        end

        subscription_line = invoice.lines.data.first
        if subscription_line.nil? || subscription_line.period.nil?
          Rails.logger.error("Subscription line item not found in invoice #{invoice.id}")
          return
        end

        update_params[:status] = 'active'
        update_params[:start_date] = Time.at(subscription_line.period.start)
        update_params[:end_date] = Time.at(subscription_line.period.end)

        if organisation_subscription.pending?
          update_params[:original_start_date] = Time.at(subscription_line.period.start)
          update_params[:original_end_date] = Time.at(subscription_line.period.end)
        end

        organisation_subscription.update(update_params)
        Rails.logger.info("Updated subscription #{subscription_id} with new billing period")

        organisation = organisation_subscription.organisation

        # create organisation subscription payment
        Axel::OrganisationSubscriptionPayment.create!(
          organisation_id: organisation.id,
          organisation_subscription_id: organisation_subscription.id,
          amount: invoice.amount_paid,
          currency: invoice.currency,
          stripe_invoice_id: invoice.id,
          paid_at: Time.at(invoice.status_transitions.paid_at),
          start_date: Time.at(subscription_line.period.start),
          end_date: Time.at(subscription_line.period.end)
        )

        # update organization customer ID
        if invoice.customer.present? && organisation.stripe_customer_id.blank?
          organisation.update(stripe_customer_id: invoice.customer)
        end
      end

      def handle_invoice_payment_failed(invoice)
        Rails.logger.info("Invoice payment failed: #{invoice.id}")

        if invoice.parent.present? && invoice.parent.type == 'subscription_details'
          subscription_id = invoice.parent.subscription_details.subscription
        else
          Rails.logger.error("Invoice #{invoice.id} does not have expected subscription details")
          return
        end

        # Find the organisation subscription by external_id
        organisation_subscription = Axel::OrganisationSubscription.find_by(external_id: subscription_id)
        if organisation_subscription.nil?
          Rails.logger.error("Subscription #{subscription_id} not found in Axel")
          return
        end

        subscription = Stripe::Subscription.retrieve(subscription_id)
        new_status = subscription.status # TODO: need to map stripe statuses to our statuses
        organisation_subscription.update(status: new_status)

        Rails.logger.info("Updated subscription #{organisation_subscription.id} to #{new_status} status after payment failure (attempt #{invoice.attempt_count})")
      end
    end
  end
end
