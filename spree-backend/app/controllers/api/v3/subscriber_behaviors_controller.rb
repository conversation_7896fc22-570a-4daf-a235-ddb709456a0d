# frozen_string_literal: true

module Api
  module V3
    class SubscriberBehaviorsController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = []
        if params[:subscriber_id]
          subscriber = ::Spree::Subscriber.find(params[:subscriber_id])
          unless subscriber
            return render_error_payload('please provide a valid subscriber id')
          end

          collection = Spree::SubscriberBehavior.where(subscriber_id: params[:subscriber_id]).ransack(params[:filter]).result.order(created_at: :desc, id: :desc).page(params[:page]).per(per_page)
        else
          collection = Spree::SubscriberBehavior.all.ransack(params[:filter]).result.order(created_at: :desc, id: :desc).page(params[:page]).per(per_page)
        end

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render_serialized_payload { serialize_collection(collection) }
      end

      protected

      def model_class
        Spree::SubscriberBehavior
      end
    end
  end
end
