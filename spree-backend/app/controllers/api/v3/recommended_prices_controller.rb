# frozen_string_literal: true

module Api
  module V3
    class RecommendedPricesController < ResourceController

      before_action :prefind_listing_inventory, only: [
        :show, :update, :apply, :history, :initial_data]

      include ValidationHelper

      # Returns the AxelPriceAnalytics auth token for the current store and the price analytics service URL.
      def token
        check_request_expected_params(current_store, %w[price_analytics_client_id price_analytics_client_secret])

        service = AxelPriceAnalytics::Service.new(store: current_store)
        render json: {host: current_store&.price_analytics_url, token: service.get_auth_token(obj: current_store)}
      end

      # Sets up price analytics credentials for the current store.
      #
      # If the store is already integrated with price analytics, returns the connection info.
      # Otherwise, updates the store URL based on its domain, creates an OAuth application,
      # and registers the store with the AxelPriceAnalytics service. The service response
      # is used to update the store's client ID and secret for price analytics.
      def credentials

        # return if already integrated
        if current_store.price_analytics_url.present? &&
           current_store.price_analytics_client_id.present? &&
           current_store.price_analytics_client_secret.present?
          render json: price_analytics_connection_info
          return
        end

        Rails.logger.info "[RecommendedPricesController:credentials] Integrating with price analytics service for store #{current_store.url}"

        #TODO should we hardcode it and put to config or expect from request ????
        sendle_client_id = "axelmarket_axel_org"
        sendle_client_secret =  "CWtB2bm8G7wztJJd5qBpk3Sf"


        # create axel-price-analytics application
        app = Spree::OauthApplication.create_or_find_by(
          name: 'AxelPriceAnalytics',
          scopes: 'admin',
          redirect_uri: '',
          store_id: current_store.id
        )

        # create user on axel-price-analytics service
        ## TODO should we put emails from request ????

        data = {
          "externalUserId" => current_store.price_analytics_customer_id,
          "platform" => current_store.url,
          "autoPriceReturnUrl" => "https://#{current_store.url}",
          "autoPriceGetUrl" => "https://#{current_store.url}",
          "details" => {
            "auth" => {
              "url" => "https://#{current_store.url}/spree_oauth/token",
              "type" => "oauth",
              "clientId" => app.uid,
              "clientSecret" => app.secret
            },
            "emails" => {
              "info" => ["<EMAIL>"],
              "warning" => ["<EMAIL>"]
            },
            "parcels" => {
              "sendle" => {
                "clientId" => sendle_client_id,
                "clientKey" => sendle_client_secret
              }
            },
            "autoanalyse" => {
              "getUrl" => "https://#{current_store.url}/api/v3/recommended_prices/export",
              "setUrl" => "https://#{current_store.url}/api/v3/recommended_prices/auto_update"
            }
          }
        }

        service = AxelPriceAnalytics::Service.new(store: current_store)
        response = service.create_user(data: data)

        # update the current store with axel-price-analytics credentials
        current_store.update(
          price_analytics_url: "https://#{service.base_domain}",
          price_analytics_client_id: response["userKey"]["clientId"],
          price_analytics_client_secret: response["userKey"]["clientSecret"]
        )
        render json: price_analytics_connection_info
      end

      # Gets the data for AxelPriceAnalytics analysis task
      def initial_data
        render json: collect_data(@recommended_price)
      end

      def show
        unless @recommended_price.persisted?
          @recommended_price.save
        end
        render_serialized_payload { serialize_resource(@recommended_price) }
      end

      def update
        if @recommended_price.update(update_params)
          render_serialized_payload { serialize_resource(@recommended_price) }
        else
          render_error_payload(@recommended_price.errors)
        end
      end

      # Apply the new price for a specific listing_inventory(new price in spree).
      # Creates new record in Spree::PriceHistory
      # Usually used for the result of AxelPriceAnalytics service
      def apply
        # Validate price presence and ensure it is greater than 0
        if params[:price].nil? || params[:price].to_f <= 0
          render json: { error: 'Price must be greater than 0 and cannot be nil' }, status: :unprocessable_entity
          return
        end

        old_price = @recommended_price.listing_inventory.price
        @recommended_price.listing_inventory.price = params[:price]
        @recommended_price.listing_inventory.save!
        @recommended_price.reload

        Spree::PriceHistory.create!(
          variant_id: @recommended_price.variant_id,
          currency: 'USD',
          previous_amount: old_price,
          amount: @recommended_price.listing_inventory.price,
          kind: 'manual',
          strategy: params[:strategy]
        )
        render_serialized_payload { serialize_resource(@recommended_price) }
      end

      # Retrieves the price history for a specific listing_inventory by variant.
      def history
        Spree::PriceHistory
          .where(variant_id: @recommended_price.variant_id)
          .order(created_at: :desc)
          .limit(20)
          .map { |price_history|
            {
              date: price_history.created_at,
              previous_amount: price_history.previous_amount,
              amount: price_history.amount,
              kind: price_history.kind,
              strategy: price_history.strategy
            }
          }
          .then { |json| render json: json }
      end

      #=================================
      # Used for 'axel-price-analytics' service
      #
      def export
        render json: Spree::RecommendedPrice
                       .where(auto_update: true, status: 'active')
                       .where.not(shipping_cost: nil)
                       .where(auto_update_at: ..Time.current)
                       .joins(:variant).includes(variant: [:product, :prices])
                       .page(1).per(params[:limit])
                       .map { |record| collect_data(record) }
                       .select { |o| o['inventoryCost'].present? }
      end

      def auto_update

        # get existed recommended price object
        listing_inventory_id = params.require(:result).require(:input).require(:externalProductId)
        listing_inventory = Spree::ListingInventory.find(listing_inventory_id)
        recommended_price = listing_inventory&.recommended_price

        # update if exists
        if recommended_price

          # update recommended price with analysis result
          result = params.to_unsafe_hash["result"]
          calculated = result.fetch("calculated") { {} }
          item_data_accepted = result.dig("items", "accepted") || []
          item_data_ignored = result.dig("items", "ignored") || []
          items = item_data_accepted.each { |item| item["state"] = "accepted" } +
                  item_data_ignored.each { |item| item["state"] = "ignored" }
          attrs = {
            zero_profit_price: calculated["zeroProfitPrice"],
            average_price: calculated["averagePrice"],
            average_effective_price: calculated["averagePrice"],
            weighted_average_effective: calculated["weightedAverage"],
            prices: {
              analysis_id: params.fetch("analyseId", nil),
              recommended: result.fetch("systemRecommendedPrice") { {} },
              additional: result.fetch("otherPrices") { [] },
              query_id: result.dig("input", "queryId"),
              items: items,
            },
            status: "active",
            min_price_ebay: calculated.fetch("minPricesByMarketplace") { {} }.fetch("ebay") { 0 },
          }
          recommended_price.assign_attributes(attrs)

          # update recommended price dates
          recommended_price.data_updated_at = Time.current
          recommended_price.calc_next_auto_update_at
          recommended_price.save!

          # auto apply suggested price
          recommended_price.apply_recommended_price!
        end
        render json: {'status': 'ok'}
      end

      #=================================
      # # Used for 'axel-price-analytics' service. OLD.
      # # GET /api/v3/recommended_prices/export
      # # Used for 'axel-price-analytics' service
      # def export
      #   json = Spree::RecommendedPrice
      #            .where(auto_update: true, status: 'active')
      #            .where.not(shipping_cost: nil)
      #            .where(auto_update_at: ..Time.current)
      #            .joins(:variant).includes(variant: [:product, :prices])
      #            .page(1).per(params[:limit])
      #            .map { |record| collect_data(record) }
      #            .select { |o| o['inventoryCost'].present? }
      #   render json: json
      # end
      #
      # # PUT /api/v3/recommended_price
      # # Used for 'axel-price-analytics' service
      # def auto_update
      #   listing_inventory_id = params.require(:result).require(:input).require(:externalProductId)
      #   listing_inventory = Spree::ListingInventory.find(listing_inventory_id)
      #   recommended_price = listing_inventory&.recommended_price
      #   if recommended_price
      #     service = PriceAnalytics::Process.new(store: current_store, user: spree_current_user, recommended_price: recommended_price)
      #     service.update(params.to_unsafe_hash)
      #     recommended_price.data_updated_at = Time.current
      #     recommended_price.calc_next_auto_update_at
      #     recommended_price.save!
      #
      #     recommended_price.apply_recommended_price!
      #   end
      #   render json: {'status': 'ok'}
      # end

      protected

      def model_class
        Spree::RecommendedPrice
      end

      def authorize_spree_user
        spree_current_user.present?
      end
      # def authorize_spree_user
      #   return if spree_current_user.nil?
      #   return true if ['token', 'export', 'update', 'auto_update', 'history', 'credentials', 'initial_data', 'apply'].include?(action_name)
      #   super
      # end

      def prefind_listing_inventory
        @listing_inventory = Spree::ListingInventory.find(params[:id])
        @recommended_price = @listing_inventory.recommended_price ||
          @listing_inventory.build_recommended_price(variant_id: @listing_inventory.variant_id)
      end

      def update_params
        params.require(:recommended_price).permit(
          :zero_profit_price, :average_price, :average_effective_price,
          :min_price_ebay, :weighted_average_effective,
          :shipping_cost,
          :auto_update, :auto_update_period, :auto_update_at,
          :analysis_id, :stocks_rate_value, :sales_rate_value, :sales_period
        )
      end

      private

      def price_analytics_connection_info
        {
          host: current_store.price_analytics_url,
          client_id: current_store.price_analytics_client_id,
          client_secret: current_store.price_analytics_client_secret
        }
      end

      def variant_inventory_cost(variant)
        Spree::StockItemUnit
          .joins(:stock_item)
          .where(state: 'stock', stock_item: {variant_id: variant.id})
          .where.not(vendor_inventory_cost: '')
          .reorder(:variant_id)
          .group(:variant_id)
          .pluck(Arel.sql('AVG(CAST(vendor_inventory_cost AS DOUBLE PRECISION)) as average'))
          .to_a
          .first
      end

      def collect_data(recommended_price)
        listing_inventory = recommended_price.listing_inventory
        variant = recommended_price.variant
        data = {
          'externalUserId' =>
            params[:externalUserId] || current_store&.price_analytics_customer_id,
          'externalProductId' => listing_inventory&.id,
          'title' => listing_inventory&.listing&.title,
          'upc' => variant.upc,
          'currentPrice' => listing_inventory&.price,
          'inventoryCost' => variant.avg_vic(strict_nil: true),
          'shippingCost' => recommended_price.shipping_cost || get_shipping_cost(variant),
          'marketplaces' => ['ebay'],
          'stock' => variant.stock_items.sum(:count_on_hand),
          'stockIncreasedAt' => variant.stock_items.order(updated_at: :desc).first&.updated_at&.as_json,
          'sales' => variant_sales(variant),
          'lastShippedPrice' => variant_last_shipped_at(variant),
          'envelopCost' => '0.0999',
          'variableCharge' => '0.136',
          'fixCharge' => '0.4',
          'tax' => '0.10'
        }
        Rails.logger.info("[RP:collect_data] listing inventory id: #{listing_inventory&.id}, data : #{data.as_json}")
        data
      end

      def variant_sales(variant)
        variant
          .line_items
          .joins(:order)
          .where(order: {state: 'complete', completed_at: 14.days.ago.at_beginning_of_day..})
          .group(Arel.sql('DATE("order"."completed_at")'))
          .pluck(Arel.sql('DATE("order"."completed_at")'), Arel.sql('SUM("quantity") as total'))
          .map { |record| {record[0].as_json => record[1].to_i} }
      end

      def variant_last_shipped_at(variant)
        variant.line_items.joins(:order).where(order: {state: 'complete'}).order(completed_at: :desc).first&.order&.completed_at&.as_json
      end

      def get_shipping_cost(variant)
        service = AxelPriceAnalytics::Service.new(store: current_store)
        service.calculate_shipping_cost(data: {
          "company" => "sendle",
          "weight" => variant.weight,
          "weightUnit" => "oz",
          "height" => variant.height,
          "width" => variant.width,
          "depth" => variant.depth,
          "dimensionUnit" => "in"
        })
      end

    end
  end
end
