# frozen_string_literal: true

module Api
  module V3
    class TrackerLogsController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index
      before_action -> { params[:sort] ||= '-created_at' }, only: :index

      private

      def model_class
        Spree::TrackerLog
      end

      def parent_scope
        @order ||= current_store.orders.find(params[:order_id])
        Spree::TrackerLog.where(trackable_id: @order.id)
      end

      def scope
        parent_scope
      end

      def allowed_sort_attributes
        []
      end
    end
  end
end
