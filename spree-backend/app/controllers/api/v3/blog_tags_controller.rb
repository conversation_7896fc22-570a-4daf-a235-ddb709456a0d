# frozen_string_literal: true

module Api
  module V3
    class BlogTagsController < ResourceController
      include ::AuthorizeUser
      before_action :authorized_to_view_blogs?, only: [:index, :show]
      before_action :authorized_to_create_edit_blogs?, only: [:create, :update]
      before_action :authorized_to_delete_blogs?, only: [:destroy]

      private

      def model_class
        ActsAsTaggableOn::Tag
      end

      def parent_scope
        model_class
      end

      def spree_permitted_attributes
        [:id, :name, :created_at, :updated_at]
      end

      def resource_serializer
        BlogTagSerializer
      end

      def permitted_resource_params
        params.require(:blog_tag).permit(:name)
      end
    end
  end
end
