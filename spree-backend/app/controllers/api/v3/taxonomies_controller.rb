# frozen_string_literal: true

module Api
  module V3
    class TaxonomiesController < ResourceController
      private

      def model_class
        Spree::Taxonomy
      end

      def scope_includes
        [
          :translations,
          root: [
            :translations,
            :icon,
            children: [:translations, :icon]
          ]
        ]
      end

      def serializer_params
        super.tap { |params| params[:view] = :full if action_name == 'show' }
      end

      def permitted_resource_params
        params.require(:taxonomy).permit(
          :name,
          :position
        )
      end
    end
  end
end
