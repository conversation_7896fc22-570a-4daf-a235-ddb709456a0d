# frozen_string_literal: true

require 'prawn'

module Api
  module V3
    class AnalysisController < BaseController
      include ::Spree::BaseHelper

      before_action(:set_current_store, only: [:show, :download, :print_expiration])
      before_action(:ensure_report_exists, :set_default_pagination, only: [:show, :download])

      def index
        render(json: supported_reports)
      end

      def supported_reports
        reports = {
          sales: [:orders]
        }
        reports.merge(::SpreeAnalysis::ReportConfig.configuration.reports)
      end

      def show
        if @report_name.to_s == 'orders'
          return show_orders
        end

        @report = ::Spree::ReportGenerationService.generate_report(@report_name, params.merge(@pagination_hash))
        @report.controller = self

        render(json: gen_json_response)
      end

      def show_orders
        filter_orders_params(params)
        orders_response
      end

      def download
        file_type = {csv: 'csv', xls: 'xls', xlsx: 'xlsx', text: 'txt', json: 'json'}
        if params.dig(:type).blank?
          return render(json: {error: 'Download type is required'}, status: :bad_request)
        end

        if ['csv', 'xls', 'xlsx', 'text', 'json'].exclude?(params[:type])
          return render(json: {error: 'Unknown download type'}, status: :bad_request)
        end

        @download_name = params.dig(:file_name) || "#{@report_name}.#{file_type[params[:type].to_sym]}"

        if @report_name.to_s == 'orders'
          return download_orders
        end

        @report = ::Spree::ReportGenerationService.generate_report(@report_name, params.merge(@pagination_hash))
        @report.controller = self

        if params[:type] == 'csv'
          response_csv
        elsif params[:type] == 'xls'
          response_xls
        elsif params[:type] == 'xlsx'
          response_xlsx
        elsif params[:type] == 'text'
          response_text
        elsif params[:type] == 'json'
          response_json
        else
          render(json: {error: 'Unknown download type'}, status: :bad_request)
        end
      end

      def download_orders
        filter_orders_params(params)
        col_sep = params[:type] == 'xlsx' ? "\t" : ','

        if params[:type] == 'xlsx'
          package = SalesReport::Order.new(@orders, col_sep).generate_xlsx
        else
          csv_string = SalesReport::Order.new(@orders, col_sep).perform_report
          @csv_data = CSV.parse(csv_string, col_sep: col_sep, headers: true)
        end

        @start_date = params[:q][:created_at_gt].to_date
        @end_date = params[:q][:created_at_lt].to_date

        if params[:type] == 'csv'
          send_data(csv_string, type: :csv, filename: @download_name, disposition: 'attachment')
        elsif params[:type] == 'xls'
          send_data(csv_string, type: :xls, filename: @download_name, disposition: 'attachment')
        elsif params[:type] == 'xlsx'
          send_data(
            package.to_stream.read,
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            disposition: 'attachment',
            filename: @download_name
          )
        elsif params[:type] == 'text'
          send_data(csv_string, type: :txt, filename: @download_name, disposition: 'attachment')
        elsif params[:type] == 'json'
          orders_response
        else
          render(json: {error: 'Unknown download type'}, status: :bad_request)
        end
      end

      private

      def response_csv
        send_data(::Spree::ReportGenerationService.download(@report), filename: @download_name)
      end

      def response_xlsx
        package = ::Spree::ReportGenerationService.download_xlsx(@report, {col_sep: "\t", report_name: @report_name.to_s})
        send_data(
          package.to_stream.read,
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          disposition: 'attachment',
          filename: @download_name
        )
      end

      def response_xls
        send_data(::Spree::ReportGenerationService.download(@report, {col_sep: "\t"}), filename: @download_name)
      end

      def response_text
        send_data(::Spree::ReportGenerationService.download(@report), filename: @download_name)
      end

      def response_json
        send_data(gen_json_response.to_json, filename: @download_name)
      end

      def set_current_store
        params[:store] = current_store
      end

      def fetch_current_store
        params[:store] = current_store
      end

      def ensure_report_exists
        @report_name = params[:id].to_sym
        unless params[:report_category]
          render(json: {error: 'Report Category is required'}, status: :bad_request)
        end
        @report_category = params[:report_category].to_sym
        reports = supported_reports
        if @report_category
          unless reports.key?(@report_category) && reports[@report_category].include?(@report_name)
            render(json: {error: 'Report Category not exist'}, status: :bad_request)
          end
        end
      end

      def set_default_pagination
        @pagination_hash = {paginate: false}
        unless params[:paginate] == 'false'
          @pagination_hash[:paginate] = true
          @pagination_hash[:records_per_page] = params[:per_page].try(:to_i) || SpreeAnalysis::Config[:records_per_page]
          @pagination_hash[:offset] = params[:page].to_i * @pagination_hash[:records_per_page]
        end
      end

      def gen_json_response
        {
          pagination_required: @report.to_h[:pagination_required],
          current_page: params[:page].to_i || 0,
          total_pages: @report.to_h[:total_pages].to_i,
          per_page: @report.to_h[:per_page].to_i,
          stats: @report.to_h[:stats]
        }
      end

      def orders_scope
        current_store.orders
      end

      def filter_orders_params(params)
        params[:q] ||= {}

        if params[:q].blank?
          params[:q][:created_at_gt] = Time.zone.yesterday.beginning_of_day.to_s
          params[:q][:created_at_lt] = Time.zone.today.end_of_day.to_s
        end
        params[:q][:completed_at_not_null] ||= '1' if ::Spree::Backend::Config[:show_only_complete_orders_by_default]
        @show_only_completed = params[:q][:completed_at_not_null] == '1'
        params[:q][:s] ||= @show_only_completed ? 'completed_at desc' : 'created_at desc'
        params[:q][:completed_at_not_null] = '' unless @show_only_completed
        params[:q].delete(:inventory_units_shipment_id_null) if params[:q][:inventory_units_shipment_id_null] == '0'

        if params[:q][:created_at_gt].present?
          params[:q][:created_at_gt] = begin
            Time.zone.parse(params[:q][:created_at_gt]).beginning_of_day
          rescue StandardError
            ''
          end
        end

        if params[:q][:created_at_lt].present?
          params[:q][:created_at_lt] = begin
            Time.zone.parse(params[:q][:created_at_lt]).end_of_day
          rescue StandardError
            ''
          end
        end
        @search = orders_scope.ransack(params[:q])
        @orders = if @pagination_hash[:paginate]
          @search.result(distinct: true).page(params[:page]).per(params[:per_page])
        else
          @search.result(distinct: true)
        end
      end

      def orders_response
        records = []
        @orders.find_each do |order|
          records << order
        end
        ret = {
          start_date: params.dig(:q, :created_at_gt),
          end_date: params.dig(:q, :created_at_lt),
          pagination_required: true,
          paginate: @pagination_hash[:paginate],
          per_page: @pagination_hash[:records_per_page].to_i,
          current_page: params[:page].to_i || 0,
          total_pages: @orders.count,
          stats: records
        }
        if @download_name.present?
          send_data(Api::V3::Analysis::OrderReportSerializer.render(ret), filename: @download_name)
        else
          render(json: Api::V3::Analysis::OrderReportSerializer.render(ret))
        end
      end
    end
  end
end
