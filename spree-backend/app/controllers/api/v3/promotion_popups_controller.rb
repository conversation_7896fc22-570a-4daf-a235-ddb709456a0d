# frozen_string_literal: true

module Api
  module V3
    class PromotionPopupsController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index
      before_action -> { params[:sort] ||= 'created_at' }, only: :index

      def templates
        scope = Spree::PromotionPopupTemplate.where(store: current_store)
        scope = scope.where(theme_type: params[:theme_type]) if params[:theme_type]
        render_serialized_payload do
          ::Api::V3::PromotionPopupTemplateSerializer.render(scope)
        end
      end

      def clone
        popup = resource.dup
        popup.name = "CLONE of #{popup.name}"

        if popup.save
          render_serialized_payload(:created) { serialize_resource(popup) }
        else
          render_error_payload(popup.errors)
        end
      end

      private

      def model_class
        Spree::PromotionPopup
      end

      def scope_includes
        [:template]
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def authorize_spree_user
        case action_name
        when 'templates'
          true
        else
          super
        end
      end

      def permitted_resource_params
        params.require(:promotion_popup).permit(
          :template_id,
          :kind, :name, :enabled,
          :title_visible, :title, :title_font, :title_font_size,
          :description_visible, :description, :description_font, :description_font_size,
          :email_visible, :email, :email_font, :email_font_size,
          :image_banner_visible, :image_banner, :image_banner_link,
          :description2_visible, :description2, :description2_font, :description2_font_size,
          :button_visible, :button_label, :button_link,
          :social_media_visible, :social_media,
          :instagram_visible, :instagram_link,
          :facebook_visible, :facebook_link,
          :twitter_visible, :twitter_link,
          :discount_coupon_type, :discount_code,
          :time_trigger_type, :time_trigger, :scrolling_position_number,
          :page_rules_type, :specific_page_rule, :specific_page_path,
          :frequency_type, :frequency_number, :frequency_per,
          :schedule_rules_type, :schedule_rules_start_time, :schedule_rules_end_time
        )
      end
    end
  end
end
