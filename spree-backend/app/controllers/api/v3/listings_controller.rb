# frozen_string_literal: true

module Api
  module V3
    class ListingsController < ResourceController
      include ::Listing::AllConcerns
      before_action :update_filter_params, only: :index
      before_action :apply_elasticsearch_filtering, only: [:index]
      before_action :filter_by_product_id, only: :index
      after_action :action_logger, only: :destroy
      before_action -> { params[:page] ||= 1 }, only: :index
      before_action :set_listing, only: [:destroy]
      before_action :set_permissions, only: [:index, :create, :update, :sync_listing_images]
      before_action :validate_authorized_user, only: [:create, :update, :sync_listing_images]
      before_action :revise_active_listing, only: [:update]
      before_action :find_sale_channel, only: [:upc_number_search, :fetch_product_with_epid]
      before_action :set_sale_channel, only: [:sync]
      before_action :merge_base_url, only: [:show, :update]

      def create
        result = ::Listing::ListingCreationService.new(current_store, permitted_resource_params, params).call
        if result.success?
          record_activity_log(result.listing, action_name)
          render_serialized_payload(:created) { serialize_resource(result.listing) }
        else
          render json: {error: result.error_message}, status: result.status
        end
      end

      def listing_inventories
        listing = Spree::Listing.find_by(id: params[:id])
        if listing
          render_serialized_payload { serialize_collection(listing.listing_inventories, '::Api::V3::ListingInventorySerializer'.constantize) }
        else
          render json: {error: 'Listing not found'}, status: :not_found
        end
      end

      def load_item_specifics
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
        category_id = params[:category_id]
        @ebay_item_specifics = Ebay::TaxonomyApis::ItemSpecific.new(
          current_store.id,
          sale_channel.oauth_application.id
        ).get_item_specifics_records(category_id)

        render json: {message: 'success', data: @ebay_item_specifics, listing: @listing}
      end

      def fetch_taxons
        @sale_channel = Spree::SaleChannel.find_by(id: params.dig(:sale_channel_id))
        taxons = Ebay::TaxonomyApis::GetCategorySuggestion.new(
          current_store,
          @sale_channel.oauth_application.id
        ).get_category_suggestions(params.dig(:search_query))
        render json: taxons.to_json
      end

      def end_item
        case @resource.sale_channel&.brand
        when 'storefront'
          end_storefront_listing
        when 'walmart'
          end_walmart_listing
        when 'amazon'
          end_amazon_listing
        when 'ebay'
          end_ebay_listing
        else
          render json: {error: 'Invalid Sale Channel'}, status: :unprocessable_entity
        end
      end

      def quantity_price_quick_edit
        case @resource.sale_channel&.brand
        when 'storefront'
          render json: {error: 'Not support'}, status: :not_found
        when 'amazon'
          quantity_price_quick_edit_amazon_listing
        when 'ebay'
          quantity_price_quick_edit_ebay_listing
        when 'walmart'
          quantity_price_quick_edit_walmart_listing
        end
      end

      def upc_number_search
        upc = params.dig(:listing, :upc).to_s.strip
        if upc.empty?
          return render json: {error: 'UPC cannot be blank'}, status: :unprocessable_entity
        end

        upc_number = upc.length == 12 ? "0#{upc}" : upc
        response = Ebay::BrowseApis::Search.new(current_store.id, @sale_channel.oauth_application.id).call(upc_number)

        if response.nil?
          render json: {message: 'Product not found'}, status: :not_found
        else
          render json: response, status: :ok
        end
      end

      def fetch_product_with_epid
        response = Ebay::CatalogApis::GetProduct.new(current_store.id, @sale_channel.oauth_application.id).call(params.dig(:listing, :epid))
        if response.nil?
          render json: {error: 'Product not found'}, status: :unprocessable_entity
        else
          render json: response.to_json
        end
      end

      def update_positions
        render json: {error: 'Position updated'}, status: :ok if @resource.update(image_classifier: params['positions'])
      end

      def get_category_features # rubocop:disable Naming/AccessorMethodName
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
        category_id = params[:category_id]

        get_category_features = Ebay::TradingApis::GetCategoryFeatures.new(
          current_store.id,
          sale_channel.oauth_application.id
        ).get_category_features(category_id)

        response = get_category_features.dig('GetCategoryFeaturesResponse')
        if response['Ack'] == 'Failure'
          render json: {error: response['Errors']}, status: :unprocessable_entity
        else
          render json: {message: 'success', data: get_category_features}
        end
      end

      def get_recommended_price # rubocop:disable Naming/AccessorMethodName
        listing = Spree::Listing.find_by(id: params[:listing_id])
        variant = Spree::Variant.find_by(id: params[:variant_id])
        listing_inventory = listing.listing_inventories.find_by(variant: variant)
        recommended_price = listing_inventory&.recommended_price || listing_inventory&.build_recommended_price(variant: variant)

        # Fix old records without listing id
        if listing_inventory
          if recommended_price&.persisted?
            recommended_price&.update(listing_inventory_id: listing_inventory.id) unless recommended_price&.listing_inventory_id
          end

          unless recommended_price&.persisted?
            # First invoke for this variant
            recommended_price = PriceAnalytics::Request.new(
              store: current_store,
              user: spree_current_user,
              variant: @variant,
              listing_inventory: @listing_inventory
            ).execute
          end
        end

        render json: {message: 'success', data: recommended_price}
      end

      def listing_conditions
        listing = Spree::Listing.find_by(id: params[:id])
        category_id = Rails.env.production? ? params[:category_id] || listing.category_id : '183593'

        response = Ebay::TradingApis::GetCategoryFeatures.new(
          listing.store.id,
          listing&.sale_channel&.oauth_application&.id
        ).get_category_features(category_id)

        error = response.dig('GetCategoryFeaturesResponse', 'Errors')

        if error.present?
          render json: {error: error}, status: :unprocessable_entity
        else
          render json: response.to_json
        end
      end

      def bind
        service = Listing::BindingValidators::BindService.new(params[:product_id], params[:listing_id])

        if service.call
          render json: {message: 'Binding successful'}, status: :ok
        else
          render json: {error: service.errors}, status: :unprocessable_entity
        end
      end

      def get_suggested_products
        listing = ::Spree::Listing.find_by(id: params[:listing_id])

        return render json: {error: 'Listing not found'}, status: :not_found unless listing

        search_terms = [listing.title, listing.sale_channel_metadata&.dig('productName'), listing.sale_channel_metadata&.dig('item-name')].compact_blank

        if search_terms.empty?
          return render json: {error: 'No valid search terms found'}, status: :unprocessable_entity
        end

        begin
          related_products = Spree::Product.search(
            search_terms,
            fields: [{name: :word_start, boost: 2}],
            match: :word_start,
            operator: :or,
            misspellings: {below: 5},
            limit: 5
          ).results

          render json: related_products, status: :ok
        rescue => e
          Rails.logger.error("Error fetching related products: #{e.message}")
          render json: {error: 'Internal Server Error'}, status: :internal_server_error
        end
      end

      def sync_listing_images
        Listing::UpdateImagesService.call(@resource, params, request, current_user)
        render_serialized_payload { serialize_resource(@resource.reload) }
      rescue => e
        Rails.logger.error("Image sync error: #{e.message}")
        render json: { error: "Failed to sync listing images" }, status: :unprocessable_entity
      end

      # amazon specific functions
      def get_taxons
        sale_channel = Spree::SaleChannel.find_by(id: params.dig(:filter, :sale_channel_id))
        taxons = {}
        if sale_channel.brand == 'amazon'
          taxons_list = Amazon::ProductTypeApis::SearchDefinitionsProductType.new(
            current_store,
            sale_channel.oauth_application.id
            ).search_product_type(params.dig(:filter, :search_query))
          (taxons_list.dig(:productTypes)|| taxons_list.dig('productTypes')).each do |type|
            type_name = (type.dig(:displayName) || type.dig("displayName"))&.split('_')&.join(" ")
            taxons = taxons.merge((type.dig(:name) || type.dig("name"))=> type_name)
          end
          # taxons = {'EYEBROW_COLOR': 'Eyebrow Color', 'EYELID_COLOR': 'Eyelid Color'} # for spec
        else
          taxons = Ebay::TaxonomyApis::GetCategorySuggestion.new(
            current_store,
            sale_channel.oauth_application.id
          ).get_category_suggestions(params.dig(:filter, :search_query))
        end
        render json: taxons.to_json
      end

      def amazon_catalog_search
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id]) || Spree::SaleChannel.find_by(id: params['sale_channel_id'])
        brand_name = params[:brand_name] || params['brand_name']
        brands_array = brand_name.split(/[\s,]+/)
        formatted_brand_name = brands_array.join(',')
        keyword = (params[:keyword] || params['keyword']).strip
        token = (params[:token] || params['token'])&.sub(/=+$/, "")
        response = Amazon::CatalogApis::SearchCatalogItems.new(current_store.id, sale_channel.oauth_application.id).search(keyword, token, formatted_brand_name)
        if response.nil? || response.dig("numberOfResults") == 0
          render json: { type: 'show_error_message', response: 'Product not found'}
        else
          render json: { type: 'amazon_catalog', response: response, sale_channel_id: sale_channel.id, keyword: params[:keyword], brand_name: formatted_brand_name }
        end
      end

      def get_catalog_items
        listing = Spree::Listing.find_by(id: params[:id]) || Spree::Listing.find_by(id: params['id'])
        sale_channel = listing.sale_channel
        asin = params[:asin] || params['asin']
        response = Amazon::CatalogApis::GetCatalogItem.new(current_store.id, sale_channel.oauth_application.id).get_attribute(asin)
        product_types = response['productTypes'] || response.dig(:productTypes)
        product_type = product_types.first["productType"] || product_types.first.dig(:productType)
        category_name = product_type.split('_').map(&:capitalize).join(' ')
        category_id =  "#{product_type}_#{category_name}"
        render json: {payload: response, categoryName: category_name, categoryId: category_id, sale_channel_id: sale_channel.id, listingId: listing.id}
      end

      def load_product_types
        listing = Spree::Listing.find_by(id: params[:id]) || Spree::Listing.find_by(id: params['id'])
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id]) || Spree::SaleChannel.find_by(id: params['sale_channel_id'])
        category = (params[:category_id] || params['category_id'])&.split('_')
        category.pop()
        category_id = category.join('_')
        attributes = get_attributes_hash(sale_channel, category_id)
        asin = params.dig(:catalog_item_payload, 'asin') || params.dig('catalog_item_payload', 'asin')
        attribute_values = get_attribute_value(listing, params.dig(:catalog_item_payload) || params.dig('catalog_item_payload'))
        attributes
        render json: {asin: asin, attributes: attributes, attribute_values: attribute_values}
        # render format: :js
      end

      def listing_issues
        listing = Spree::Listing.find_by(id: params[:id]) || Spree::Listing.find_by(id: params['id'])
        sale_channel = listing.sale_channel
        sku = listing.sku
        response = ::Amazon::ListingApis::GetListingItem.new(current_store.id, sale_channel.oauth_application.id).get_listing_item(sku)
        error_message = []
        if response.dig('errors')
          error_message << response.dig('errors').first.dig('message')
        else
          response.dig('issues').each do |issue|
            next if issue.dig('severity') != "ERROR"
            error_message << issue.dig('message')
          end
        end
        error_message
        render json: { listingId: listing.id, sale_channel_id: sale_channel.id,  error_message: error_message}
      end

      def revise_amazon_listing
        listing = Spree::Listing.find_by(id: params[:id]) || Spree::Listing.find_by(id: params['id'])
        ActiveRecord::Base.transaction do
          begin
            store_id = current_store.id
            listing_data = {}
            sale_channel = Spree::SaleChannel.find_by(id: params['sale_channel_id'])
            oauth_application = sale_channel.oauth_application
            sku = params.dig('item_specifics', 'package_contains_sku-sku') || listing&.sku
            # Validate presence of SKU
            raise StandardError, 'SKU must be present' if sku.blank?

            product_type = get_amazon_product_type(params.dig('taxon'))
            listing_data['product_type'] = product_type
            listing_data['item_specifics'] = item_specific_values(listing, params)
            attributes = get_attributes_hash(sale_channel, product_type)
            response = Amazon::ListingApis::PutListingItem.new(store_id, oauth_application.id).put_listing_item(sku, listing_data, listing)

            listing.update!(sku: sku, category_id: params['taxon'], category_name: params['taxon']&.split("_")&.last)
            listing.update!(status: "Active") if response&.dig('status') == 'ACCEPTED'
            render json: {status: response&.dig('status'), issues: response&.dig('issues'), attributes: attributes}, status: :ok
          rescue StandardError => e
            render_error_payload(e.message)
            raise ActiveRecord::Rollback
          end
        end
      end

      def update_amazon_listing
        listing = Spree::Listing.find_by(id: params[:id]) || Spree::Listing.find_by(id: params['id'])
        ActiveRecord::Base.transaction do
          begin
            announcements = Spree::Announcement.where(subject_id: listing.id)
            announcements.destroy_all if announcements.any? #destoy all previous announcement for that listing.
            store_id = current_store.id
            listing_data = {}
            sale_channel = Spree::SaleChannel.find_by(id: params['sale_channel_id'])
            oauth_application = sale_channel.oauth_application
            sku = params.dig('item_specifics', 'package_contains_sku-sku') || listing.sku
            # Validate presence of SKU
            raise StandardError, 'SKU must be present' if sku.blank?

            previous_asin = listing.sale_channel_hash.dig('attributes', 'merchant_suggested_asin')&.first&.dig('value')
            current_asin = params.dig('item_specifics', 'merchant_suggested_asin-value')
            # Destroy all previous images when the listing is being revised.
            listing.image_files.attachments.destroy_all if previous_asin != current_asin
            product_type = get_amazon_product_type(params.dig('taxon'))
            listing_data['product_type'] = product_type
            listing_data['item_specifics'] = item_specific_values(listing, params)
            attributes = get_attributes_hash(sale_channel, product_type)
            response = Amazon::ListingApis::PatchListingItem.new(store_id, oauth_application.id).patch_listing_item(sku, listing_data, listing)

            listing.update!(sku: sku, category_id: params['taxon'], category_name: params['taxon']&.split("_")&.last)
            render json: {status: response.dig('status'), issues: response.dig('issues'), attributes: attributes}, status: :ok
          rescue StandardError => e
            render_error_payload(e.message)
            raise ActiveRecord::Rollback
          end
        end
      end
      # end amazon specific functions

      private

      def find_sale_channel
        @sale_channel = Spree::SaleChannel.find_by(id: params[:listing][:sale_channel_id])
        render_error_payload('Invalid Sale Channel Id') if @sale_channel.nil?
      end

      def end_storefront_listing
        if @resource.update(status: 'Ended', end_time: Time.current)
          render json: ListingSerializer.render(@resource)
        else
          render json: {error: 'Failed to update listing'}, status: :unprocessable_entity
        end
      end

      def end_ebay_listing
        response = Ebay::TradingApis::EndItem.new(
          current_store.id,
          @resource.sale_channel&.oauth_application&.id
        ).end_item(@resource.item_id)

        if response
          @resource.update(end_time: Time.current, status: 'Ended')
          render json: ListingSerializer.render(@resource)
        else
          render json: {error: 'Failed to end item on eBay'}, status: :unprocessable_entity
        end
      end

      def end_amazon_listing
        response = Amazon::ListingApis::DeleteListingItem.new(
          current_store.id,
          @resource.sale_channel&.oauth_application&.id
        ).delete_listing_item(@resource.sku, @resource)

        if response
          @resource.update(end_time: Time.current, status: 'Ended')
          render json: ListingSerializer.render(@resource)
        else
          render json: {error: 'Failed to end item on eBay'}, status: :unprocessable_entity
        end
      end

      def set_permissions
        @permissions = current_user_permissions.find_permission
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end

      def authorized_to_create_listing?
        @permissions&.create_and_edit_listing
      end

      def validate_authorized_user
        unless authorized_to_create_listing?
          render_error_payload('You are not authorized to perform this action', :forbidden)
        end
      end

      protected

      def model_class
        Spree::Listing
      end

      def scope_includes
        [{sale_channel: :oauth_application}, :product]
      end

      def serializer_params
        include_fields = params[:include]&.split(',') || []
        super.tap { |params|
          params[:include] = include_fields
          params[:frequently_bought_listing] = frequently_bought_listing if ['index'].include?(action_name)
          params[:view] = :full if ['index', 'listing_inventories', 'listing_conditions'].exclude?(action_name)
        }
      end

      def authorize_spree_user
        case action_name
        when 'fetch_taxons' then true
        when 'load_item_specifics' then true
        when 'upc_number_search' then true
        when 'fetch_product_with_epid' then true
        when 'listing_conditions' then true
        when 'listing_inventories' then true
        when 'get_category_features' then true
        when 'get_recommended_price' then true
        when 'fetch_walmart_package_type' then true
        when 'sync' then true
        when 'bind' then true
        when 'get_suggested_products' then true
        when 'get_taxons' then true
        when 'amazon_catalog_search' then true
        when 'get_catalog_items' then true
        when 'listing_issues' then true
        when 'load_product_types' then true
        when 'revise_amazon_listing' then true
        when 'update_amazon_listing' then true
        else
          super
        end
      end

      def allowed_sort_attributes
        [:title]
      end

      def revise_active_listing
        # Log the listing params value
        Rails.logger.info "@@@@@@@@@@Listing params #{params}"
        case params.dig(:listing, :sale_channel, :brand).to_s.downcase
        when 'ebay'
          revise_active_ebay_listing
        when 'amazon'
          revise_amazon_active_listing
        when 'walmart'
          revise_walmart_active_listing
        when 'storefront'
          revise_active_storefront_listing
        else
          render json: {error: 'Incorrect parameter'}, status: :not_found
        end
        @resource.reload if @resource.present?
      end

      def permitted_resource_params
        params.require(:listing).permit(
          :sale_channel_id, :product_id, :status, :start_time, :end_time,
          :sku, :title, :description, :category_id, :category_name,
          :shipping_id, :payment_id, :return_id, :condition_id,
          :currency, :allow_offer, :minimum_offer_price, :autoaccept_offer_price,
          :id, :condition, :item_specifics, :image_files, :selected_remove_images, :shipping_category_id,
          :exclude_from_data_feed, :exclude_from_meta_data_feed
        ).tap { |p|
          p[:status] = convert_status(p[:status]) if p.key?(:status)
        }
      end

      def merge_base_url
        params[:base_url] = request.base_url
      end

      def update_filter_params
        params[:filter] ||= {}
        params[:filter][:status_in] = convert_statuses(params[:filter][:status_in]) if params[:filter].key?(:status_in)
        if frequently_bought_listing.present? && params[:filter][:sale_channel_id_in].blank?
          params[:filter][:sale_channel_id_in] = [frequently_bought_listing.sale_channel_id]
        end
      end

      def filter_by_product_id
        @collection = @collection.where(product_id: params[:filter][:product_id]) if params[:filter][:product_id].present?
      end

      def convert_status(status)
        status == 'draft' ? status : status.titleize
      end

      def convert_status_in(status)status_hash = Spree::Listing.statuses.map { |key, _| [key.downcase, key] }.to_h
                                   ret = status_hash[status.downcase]
                                   ret.presence || status
      end

      def convert_statuses(items)
        Array.wrap(items).compact_blank.map { |item| convert_status_in(item) }
      end

      def collection
        @collection ||= scope.ransack(params[:filter]).result.order(start_time: :desc, id: :desc)
      end

      def frequently_bought_listing
        @frequently_bought_listing ||= params[:frequently_bought_listing_id].present? ? Spree::Listing.find_by(id: params[:frequently_bought_listing_id]) : nil
      end

      # amazon private functions
      def get_attribute_value(listing, catalog_payload = nil)
        if catalog_payload.present?
          raw_attributes = catalog_payload.require(:attributes).permit!.to_h
          data = transform_params(raw_attributes)
        else
          data = listing.sale_channel_hash&.dig('attributes')
        end
        transformed_data = flatten_hash(data)
      end

      def transform_params(params)
        params.transform_values do |value|
          if value.is_a?(Hash)
            value.values
          else
            value
          end
        end
      end

      def flatten_hash(hash, prefix = nil)
        hash.each_with_object({}) do |(key, value), result|
          new_key = [prefix, key].compact.join('-')
          if value.is_a? Hash
            result.merge!(flatten_hash(value, new_key))
          elsif value.is_a? Array
            value.each do |item|
              next unless item.is_a?(Hash)
              result.merge!(flatten_hash(item, new_key))
            end
          else
            result[new_key] = value
          end
        end
      end

      def get_attributes_hash(sale_channel, category_id)
        listing = Spree::Listing.find_by(id: params[:id]) || Spree::Listing.find_by(id: params['id'])
        get_product_schema = Amazon::ProductTypeApis::GetDefinitionsProductType.new(current_store.id, sale_channel.oauth_application.id).get_definition(category_id.upcase)
        get_product_schema_url = get_product_schema.dig('schema', 'link', 'resource')
        data = URI.open(get_product_schema_url).read
        data = JSON.parse(data).with_indifferent_access
        structured_data = Amazon::GetProductSchema.new(get_product_schema_url).structure_data
        listing.update(sale_channel_hash: structured_data) if params['action'] == "update_amazon_listing" ||  params['action'] == "revise_amazon_listing" || listing.sale_channel_hash.blank?
        attributes = {}
        data.dig('properties').each do |prop_name, prop_hash|
          intermidiate_properties = []
          items = prop_hash.dig('items')
          items.dig('properties').each do |cp_name, cp_hash|
            if cp_hash.dig('properties').present? || cp_hash.dig('items', 'properties').present?
              new_key = prop_name + "-" + cp_name
              result = process_nested_data(cp_name, (cp_hash.dig('properties') || cp_hash.dig('items', 'properties')), new_key)
              attributes = attributes.merge(result)
            else
              new_key = prop_name + "-" + cp_name
              attributes = attributes.merge({new_key => cp_hash})
            end
          end
          attributes
        end
        unless attributes.keys.include?('package_contains_sku-sku')
          sku_fields = { "package_contains_sku-marketplace_id"=>{"$ref"=>"#/$defs/marketplace_id"}, "package_contains_sku-quantity"=>{"title"=>"Package Contains SKU Quantity", "description"=>"Provide the quantity of each unit, case, or pallet identified in Package Level.", "editable"=>true, "hidden"=>false, "examples"=>["1"], "type"=>"integer", "minimum"=>0, "maximum"=>250}, "package_contains_sku-sku"=>{"title"=>"Package Contains SKU Identifier", "description"=>"Provide the SKU identifier of each unit, case or pallet identified in Package Level.", "editable"=>true, "hidden"=>false, "examples"=>["ABC123"], "type"=>"string", "maxLength"=>100}}
          attributes = sku_fields.merge(attributes)
        end
        attributes
      end

      def process_nested_data(cp_name, data, new_key="" )
        response_data = {}
        updated_key = new_key
        data.each do |data_key, data_value|
          new_key = updated_key
          if data.dig('properties').present? || data.dig("#{data_key}", 'properties').present? || data.dig("#{data_key}", 'items', 'properties').present?
            properties = data.dig('properties') ||
              data.dig(data_key, 'properties') ||
              data.dig(data_key, 'items', 'properties')
              new_key = new_key + "-" + data_key
            response_data = process_nested_data(data_key, properties, new_key)
          else
            new_key = new_key + "-" + data_key
            response_data = response_data.merge({new_key => data_value})
          end
        end
        response_data
      end

      def get_amazon_product_type(taxon)
        if taxon
          taxon_parts = taxon.split('_')
          product_type = (taxon_parts - [taxon_parts.last]).join('_')
        else
          product_type = ''
        end
      end

      def item_specific_values(listing, params)
        item_specific_data = {}
        params['item_specifics'].each do |prop, value|
          item_specific_data = item_specific_data.merge({prop => value})
        end
        item_specific_data = get_amazon_images(item_specific_data, listing, params)
        item_specific_data
      end

      def get_amazon_images(item_specific_data, listing, params)
        return item_specific_data unless params['item_specifics_images'].present?

        params['item_specifics_images'].each do |image_key, image_value|
          new_filename = "#{image_key}-#{image_value.original_filename.to_s}"
          listing.image_files.attachments.map { |x| x.blob.filename_for_database }.each do |image_name|
            if (image_name.starts_with?(image_key))
              # delete image file of that attachment
              attachment = listing.image_files.attachments.find { |a| a.blob.filename_for_database == image_name }
              attachment.purge if attachment
            end
          end

          if listing.image_files.attachments.map { |x| x.blob.filename_for_database }.include?(new_filename)
            existing_image_attachment = listing.image_files.attachments.select { |image_file| image_file.url if image_file.filename == new_filename }
            existing_image_url = existing_image_attachment.last.url
          else
            listing.image_files.attach(io: params['item_specifics_images'][image_key], filename: new_filename, content_type: image_value.content_type)
            image_url = listing.image_files.attachments.last.url
          end
        end
        listing.image_files.attachments.each do |image_file|
          name = image_file.filename.to_s.split('-')
          item_specific_data[name[0...-1].join('-')] = image_file.url.split('?').first
        end
        item_specific_data
      end
      # end amazon private functions
    end
  end
end
