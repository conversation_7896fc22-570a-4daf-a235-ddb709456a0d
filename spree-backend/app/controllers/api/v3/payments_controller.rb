# frozen_string_literal: true

module Api
  module V3
    class PaymentsController < ResourceController
      def capture
        payment_event(:capture!)
      end

      def void
        payment_event(:void_transaction!)
      end

      private

      def model_class
        Spree::Payment
      end

      def payment_event(event)
        unless resource.payment_source
          render_error_payload({status: :not_acceptable})
          return
        end

        if resource.send(event)
          render_serialized_payload { serialize_resource(resource) }
        else
          render_error_payload({status: :cannot_perform_operation})
        end
      rescue Spree::Core::GatewayError => e
        render_error_payload(e.message.to_s)
      end
    end
  end
end
