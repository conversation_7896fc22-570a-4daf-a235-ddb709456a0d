# frozen_string_literal: true

module Api
  module V3
    class LocalesController < ResourceController
      def index
        render_serialized_payload { locales.to_json }
      end

      protected

      def model_class
        nil
      end

      def authorize_spree_user
      end

      def locales
        Spree.available_locales.map do |locale|
          name = if I18n.exists?('spree.i18n.this_file_language', locale: locale, fallback: false)
            Spree.t('i18n.this_file_language', locale: locale)
          elsif defined?(SpreeI18n::Locale) && (language_name = SpreeI18n::Locale.local_language_name(locale))
            "#{language_name} (#{locale})"
          elsif locale.to_s == 'en'
            'English (US)'
          else
            locale
          end
          {locale: locale.to_s, name: name}
        end
      end
    end
  end
end
