# frozen_string_literal: true

module Api
  module V3
    class EmailSettingsController < ResourceController
      protected

      def model_class
        Spree::EmailSetting
      end

      def resource
        email_setting_type = params.dig(:email_setting, :email_setting_type) || 'store_email'
        setting_type = email_setting_type == 'campaign_email' ? :campaign_email_setting : :email_setting
        @resource ||= current_store.public_send(setting_type) || current_store.public_send(:"build_#{setting_type}")
      end

      def permitted_resource_params
        params.require(:email_setting).permit(
          :email_from, :email_bcc, :intercept_email, :mail_delivery, :api_key, :email_setting_type,
          smtp: [
            :domain,
            :address,
            :port,
            :secure_connection_type,
            :authentication,
            :user_name,
            :password
          ]
        )
      end
    end
  end
end
