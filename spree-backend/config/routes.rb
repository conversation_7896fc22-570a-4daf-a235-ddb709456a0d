# frozen_string_literal: true

require 'sidekiq/web'
require 'sidekiq-scheduler/web'

class TenantAccess
  def matches?(_request)
    Apartment::Tenant.current == 'public' or Rails.env.test?
  end
end

Sidekiq::Web.use(Rack::Auth::Basic) do |username, password|
  username == Rails.application.secrets.sidekiq_username &&
    password == Rails.application.secrets.sidekiq_password
end

Rails.application.routes.draw do
  mount Spree::Core::Engine, at: '/'
  mount ActionCable.server => '/cable'
  mount Sidekiq::Web, at: '/old_admin/sidekiq'
  mount Rswag::Ui::Engine => '/api/v3/documentations'
  mount Rswag::Api::Engine => '/api/v3/documentations'

  get "old_admin/:id", to: "spree/admin/shortener/shortened_urls#show", as: :show

  get 'sitemaps/:code/sitemap.xml', to: 'sitemap#show', as: :sitemap

  namespace :api, defaults: { format: 'json' } do
    namespace :v3 do
      resources :email_confirmation, controller: :email_confirmations, only: [] do
        collection do
          post :send_code
          post :resend_code
          post :verify_code
        end
      end

      post "/auth/google", to: "accounts#oauth_login"
      post "/auth/apple", to: "accounts#oauth_login"
      resources :account, controller: :accounts, only: [] do
        collection do
          post :sign_up
          post :sign_in

          get :public_tenant_url
        end
      end

      resource :two_factor_auth, only: [:show] do
        post :verify_code
        post :disable
      end

      namespace :storefront do
        resource :homepage, only: [:show]
        resources :pages, only: [:show]
        resources :collections, only: [:show]
        resources :promotion_popups, only: [:index]
        resources :products, only: [:index, :show]
        resources :listings, only: [:index, :show] do
          collection do
            get :filters
          end
        end
        resources :featured_products, only: [:index]

        post "/auth/google", to: "account#oauth_login"
        post "/auth/apple", to: "account#oauth_login"

        resource :account, controller: :account, only: %i[show update] do
          collection do
            post :reset_password
            post :sign_up
            post :sign_in, to: "/doorkeeper/tokens#create"
            # post :logout, to: "/doorkeeper/tokens#revoke"
            post :logout
          end
        end

        namespace :account do
          resources :addresses, controller: :addresses
          resources :credit_cards, controller: :credit_cards, only: %i[index show destroy]
          resources :orders, controller: :orders, only: %i[index show]
        end

        resources :countries, only: [:index]
        get '/countries/:iso', to: 'countries#show', as: :country
        resources :menus, only: [:index, :show]

        namespace :subscribe_newsletters do
            post :create
          end

        resources :subscriptions do
          member do
            get :fetch_delivery_date
            get :fetch_delivery_intervals
            get :revise_status
            get :fetch_subscription_orders
          end
        end

        resource :checkout, controller: :checkout, only: %i[update] do
          patch :next
          patch :advance
          patch :complete
          post :create_payment
          post :add_store_credit
          post :remove_store_credit
          get :payment_methods
          get :shipping_rates
          patch :select_shipping_method
        end

        resources :shipping_methods do
          get :list_local_pickup_shipping_methods, on: :collection
        end

        resource :paypal, controller: :paypal, only: %i[create] do
          post :confirm
          post :cancel
        end

        namespace :stripe do
          post :confirm
          post :webhook
        end

        namespace :intents do
          post :create
          post :payment_confirmation_data
          post :handle_response
        end

        resources :blog_categories, only: [:index]
        resources :blog_posts, only: [:index, :show] do
          patch :like, on: :member
          patch :unlike, on: :member
        end

        resources :wishlists do
          get :default, on: :collection
          member do
            post :add_item
            patch 'set_item_quantity/:item_id', to: 'wishlists#set_item_quantity', as: :set_item_quantity
            delete 'remove_item/:item_id', to: 'wishlists#remove_item', as: :remove_item
          end
        end

        resource :cart, controller: :cart, only: %i[show create destroy] do
          post   :add_item
          patch  :empty
          delete 'remove_line_item/:line_item_id', to: 'cart#remove_line_item', as: :cart_remove_line_item
          patch  :set_quantity
          patch  :apply_coupon_code
          delete 'remove_coupon_code/:coupon_code', to: 'cart#remove_coupon_code', as: :cart_remove_coupon_code
          delete 'remove_coupon_code', to: 'cart#remove_coupon_code', as: :cart_remove_coupon_code_without_code
          get :estimate_shipping_rates
          patch :associate
          patch :change_currency
        end
        resources :variants, only: [] do
          resources :notifications, only: %i[create]
        end
        resources :volume_prices, only: [:index]
      end

      resources :blog_categories
      resources :blog_tags
      resources :blog_posts do
        collection do
          post :upload_images
          delete :remove_images
        end
      end

      resources :campaigns do
        collection do
          get :list_senders
        end

        member do
          post :clone
        end
      end

      resources :assets
      resources :ebay_policies, only: [:index]

      # Promotions API
      resources :promotions
      resources :promotion_actions
      resources :promotion_categories
      resources :promotion_rules
      resources :promotion_popups do
        collection do
          get :templates
        end
        member do
          post :clone
        end
      end

      # Returns API
      resources :customer_returns, only: [:index]
      resources :return_items, only: [:update]
      resources :return_authorizations do
        member do
          patch :add
          patch :cancel
          patch :receive
        end
      end

      # Product Catalog API
      resources :prototypes do
        member do
          get :select
        end

        collection do
          get :available
        end
      end

      get 'sale_channels/:sale_channel_id/disconnect', to: 'sale_channels#disconnect', as: :disconnect_sale_channel
      resources :sale_channels, only: [:show, :index, :create, :update] do
        member do
          get :fetch_walmart_package_type
        end

        collection do
          get :available_countries
          get :available_channels
          get :brand_channels
          get :rotate_lwa_credentials
          get :register_brand_account
          get :auth_code
          get :switch_store
          get :renew_token
        end
      end

      resources :listings do
        collection do
          get :sync
          get :load_item_specifics
          get :fetch_taxons
          get :upc_number_search
          get :fetch_product_with_epid
          get :get_category_features
          get :get_recommended_price
          post :bind
          get :get_suggested_products
          get :get_taxons
          get :amazon_catalog_search
          post :load_product_types
        end

        member do
          post :sync_listing_images
          get :listing_inventories
          get :listing_conditions
          get :end_item
          patch :quantity_price_quick_edit
          post :update_positions
          get :fetch_walmart_package_type
          get :get_catalog_items
          get :listing_issues
          patch :revise_amazon_listing
          patch :update_amazon_listing
        end

        resources :frequently_boughts, only: [:index, :destroy] do
          collection do
            post :batch, action: :batch_create
            patch :positions
          end
        end
      end

      namespace :walmart_specs do
        get :categories
        get :product_type_groups
        get :product_types
        get :item_spec
      end

      namespace :walmart do
        get :get_walmart_product_id
        get :search_walmart_catalog
      end

      resources :products do
        member do
          post :clone
        end

        resources :images
        resources :variants do
          collection do
            post :bulk_create
            patch :bulk_update
          end
        end
        resources :properties, controller: 'product_properties'
        get :stock, to: 'stock_items#index'
        get :prices, to: 'prices#index'

        # Match Its API
        resources :match_its, only: [:index, :destroy] do
          collection do
            post :batch, action: :batch_create
            put :positions
          end
        end
      end
      resources :variants, only: [:show] do
        collection do
          get :find
        end
      end

      resources :featured_products do
        collection do
          post :batch
        end
      end

      resources :barcodes, only: [] do
        post :scan, on: :member
      end

      resources :taxonomies
      resources :taxons do
        member do
          patch :reposition
        end
      end
      resources :classifications
      resources :properties
      resources :option_types do
        resources :values, controller: 'option_values'
      end

      # Return Authorization Reason API
      resources :return_authorization_reasons

      # Refund Reason API
      resources :refund_reasons

      # Return Reimbursement Type API
      resources :reimbursement_types

      # easypost Scan Form API
      resources :scan_forms, only: [:index, :create]

      # easypost Customer Shipments Tracking API(Tracking Search)
      resources :customer_shipments_tracking, only: [:index]

      # ImageUpload API
      resources :image_upload, only: [] do
        get :upload, on: :collection
        post :successful_upload, on: :collection
        delete :remove_image, on: :collection
      end

      # Order API
      resources :orders do
        collection do
          post :sync
        end

        member do
          patch :next
          patch :advance
          patch :approve
          patch :cancel
          patch :resume
          patch :empty
          patch :apply_coupon_code
          patch :complete
          patch :use_store_credit
          get :calculate_weight
          get :list_order_packages
          get :validate_stock_location
          post :resend
        end

        resources :items, controller: 'order_items'
        resources :adjustments do
          collection do
            patch :open
            patch :close
          end
        end
        resources :order_packages, only: [:index, :destroy]
        resources :state_changes, only: [:index]
        resources :tracker_logs, only: [:index]

        # Order Returns API
        resources :customer_returns, except: [:destroy] do
          member do
            put :refund
          end
        end
        resources :reimbursements, except: [:destroy] do
          member do
            post :perform
          end
        end
        resources :return_authorizations do
          member do
            patch :add
            patch :cancel
            patch :receive
            post :send_label
          end
        end
      end

      # Payments API
      resources :payment_methods do
        collection do
          get :providers
        end
      end
      resources :payments do
        member do
          patch :capture
          patch :void
          # patch :authorize
          # patch :purchase
          # patch :credit
        end
      end

      # Store Credit API
      resources :store_credits
      resources :store_credit_categories
      resources :store_credit_types

      # Geo API
      resources :zones
      resources :countries
      resources :states

      # Shipment API
      resources :easypost_settings
      resources :order_packages do
        member do
          get :shipments
          put :buy_postage
          patch :cancel_shipping_label
          get :attach_pdf
        end

        collection do
          patch :ship
          get :shipping_rate
          get :dimensions
          post :check_shipping_categories
        end
      end

      resources :shipments do
        member do
          patch :ready
          patch :ship
          patch :cancel
          patch :resume
          patch :pend

          put :shipping_rates
          put :buy_postage
          patch :delete_shipping_label

          patch :add_item
          patch :remove_item
          patch :transfer_to_location
          patch :transfer_to_shipment
          patch :update_selected_shiping_rate_cost
        end
      end

      # Tax API
      resource :taxjar_settings, only: [:show, :update] do
        put :refresh, on: :member
      end
      resource :stripe_tax_settings, only: [:show, :update]
      resources :tax_rates
      resources :tax_categories

      # Inventory API
      scope 'stock' do
        resources :transfers, controller: 'stock_transfers'
        resources :items, controller: 'stock_items' do
          resources :units, controller: 'stock_item_units', only: :index
        end

        resources :units, controller: 'stock_item_units' do
          collection do
            get :find
            get :pricecompare_info

            post :batch, action: :batch_create
            patch :batch, action: :batch_update
            patch :batch_lock, action: :batch_lock
            patch :batch_shipped, action: :batch_shipped
            post :divide_units
            post :verify_removable
          end
        end
      end
      resources :stock do
        resources :locations, controller: 'stock_locations'
        resources :movements, controller: 'stock_movements'

        collection do
          get :alerts
          get :pull
        end
      end

      # User and Auth API
      resources :tokens, only: [] do
        collection do
          post :introspect
        end
      end
      resources :users do
        collection do
          get :current
        end
        member do
          get :addresses
          get :items
          get :orders
        end
        resources :store_credits
      end
      resources :credit_cards
      resources :addresses
      resources :roles
      resources :activity_logs, only: [:index]
      resources :oauth_applications, only: [] do
        collection do
          post '/register', to: 'oauth_applications#register'
        end
      end

      # CMS
      resource :homepage, only: [:show, :update]
      resources :listing_collections
      resources :pages do
        resources :sections, controller: "page_sections"
      end

      # Menu API
      resources :menus do
        resources :items, controller: 'menu_items' do
          patch :reposition, on: :member
        end
      end

      # Wishlists API
      resources :wishlists
      resources :wished_items

      # Digitals API
      resources :digitals
      resources :digital_links do
        member do
          patch :reset
        end
      end

      # Store API
      resources :organisations, only: [:index, :show] do
        resources :subscriptions, only: [:index, :show, :create, :update], controller: 'organisation_subscriptions', path: 'subscriptions' do
          member do
            post :cancel
            post :reactivate
            post :portal, to: 'organisation_subscriptions#create_stripe_portal_session'
            get :payments, to: 'organisation_subscriptions#payments'
          end
        end
        get :features, to: 'plan_features#organisation_features_index'
        collection do
          post :update_two_factor
        end
      end
      resources :organisations, only: [:create, :update, :destroy], constraints: TenantAccess.new
      resources :stores do
        collection do
          get :current
        end
        member do
          put :default, action: :set_default
          put :publish
          put :disable
        end
      end
      resources :currencies, only: [:index]
      resources :locales, only: [:index]
      # Settings API
      resource :invoice_settings, only: [:show, :update]
      resource :email_settings, only: [:show, :update]
      resources :email_templates do
        member do
          post :test_mail
        end
      end
      resources :tags do
        collection do
          post :batch, action: :batch_create
          delete :batch, action: :batch_destroy
        end
      end

      # Data Feeds API
      resources :data_feeds

      # Configurations API
      resources :shipping_categories
      resources :shipping_methods

      resources :subscriptions, only: [:index, :show] do
        member do
          patch :revise_status
        end
      end

      # Webhooks API
      namespace :webhooks do
        resources :subscribers do
          collection do
            get :supported_events
          end
        end
        resources :events, only: :index
      end

      # DashBoard API
      resources :dashboards, only: [] do
        collection do
          get :todo_statistics
          get :payment_method_statistics
          get :financial_statistics
          get :announcements
        end
      end

      # Analysis API
      resources :analysis, only: [:index] do
        collection do
          get '/show', to: 'analysis#show'
          get :download
        end
      end

      # Gallery Item API
      resources :gallery_items do
        collection do
          get :space_statistics
          get :products, to: 'gallery_items#list_product'
          get :orders, to: 'gallery_items#list_order'
          get :listings, to: 'gallery_items#list_listing'
          get :get_product_object, to: 'gallery_items#get_product_object'
          get :get_order_object, to: 'gallery_items#get_order_object'
          get :get_listing_object, to: 'gallery_items#get_listing_object'
          delete 'remove_listing_image/:object_id/:image_id', to: 'gallery_items#remove_listing_image'
          delete 'remove_order_image/:object_number/:image_id', to: 'gallery_items#remove_order_image'
          get 'search/:scope/:query', to: 'gallery_items#search'
        end
      end

      # Import Log API
      resources :import_logs, only: [:index, :show] do
        get :import_sample, on: :collection
        post :clear, on: :collection
      end

      resources :recommended_prices, only: [:show, :update] do
        member do
          put :apply
          get :history
          get :initial_data
        end

        collection do
          post :token
          post :credentials
          put :auto_update
          get :export
        end
      end

      resources :subscribers, only: [:index, :show] do
        resources :subscriber_behaviors, only: [:index, :show]
      end

      # put 'recommended_price', to: 'recommended_prices#auto_update'

      # only get for non-public tenants
      resources :plans, only: [:index, :show] do
        resources :features, only: [:index, :show], controller: 'plan_features'
      end

      resources :plans, only: [:create, :update, :destroy], constraints: TenantAccess.new do
        resources :features, only: [:create, :update, :destroy], constraints: TenantAccess.new, controller: 'plan_features'
        collection do
          post :create_stripe_products, constraints: TenantAccess.new
        end
      end

      scope 'stripe' do
        post :webhook, constraints: TenantAccess.new, to: 'axel_stripe#webhook'
      end
    end
  end
end

Spree::Core::Engine.add_routes do
  namespace :admin, path: Spree.admin_path do
    delete 'orders/destroy_order_package'
    resources :qr_codes, only: :show
    resources :stocks, only: :index do
      collection do
        get :print
      end
    end
    resources :products do
      resources :stock_items, only: [] do
        collection do
          get :batch_new
          post :batch_create
        end
      end

      resources :stock_item_units, only: [:index] do
        collection do
          post :batch_create
          get :print
          put :batch_update
          get :batch_restore
          get :batch_lock
          post :divide_units
        end
        member do
          get :divide_pack_size
        end
      end

      resources :match_its, only: [:index, :destroy] do
        collection do
          post :batch_create
          post :update_positions
        end
      end

      member do
        put :set_active
        get :activity_log, to: "products#product_activity_log"
        get :action_log, to: 'products#product_action_log'
      end

      collection do
        post :import
        get :search
        get :find_oauth_list
        get :track_number_setting
        get :search_product_to_add_stock
        get :scan_vin_to_add_stock
        post :product_title_or_upc_search
        get :scan_and_fetch_upc
        post :fetch_upc_item_lookup
        post :populate_vin_field
        post :batch_create_stock_item
        post :batch_create_stock_item_units
        post :create_product
        get :compare_price
      end
    end
    resources :variants do
      resource :recommended_prices, only: [:show, :update] do
        get :settings
        post :settings
        post :refresh
        post :recalc
        put :apply
        put :ignore_item
        put :accept_item
        put :update_item
      end
    end

    resources :stock_locations do
      resources :sections, controller: :stock_location_sections
    end

    resources :section_items
    resources :options
    namespace :taxjar_settings do
      post :refresh
    end
    resources :import_logs, only: [:index, :show] do
      get :import_sample, on: :collection
      post :clear, on: :collection
    end

    resources :dashboards do
      collection do
        get :payment_method_chart
        get :financial_chart
      end
    end

    resource :email_settings

    resources :gallery_items do
      collection do
        get :products, to: 'gallery_items#list_product'
        get :orders, to: 'gallery_items#list_order'
        get :listings, to: 'gallery_items#list_listing'
        get :get_product_object, to: 'gallery_items#get_product_object'
        get :get_order_object, to: 'gallery_items#get_order_object'
        get :get_listing_object, to: 'gallery_items#get_listing_object'
        delete :remove_listing_image, to: 'gallery_items#remove_listing_image'
        delete :remove_order_image, to: 'gallery_items#remove_order_image'
        get :search, to: 'gallery_items#search'
      end
    end

    resources :orders do
      member do
        post :invoice_pdf
      end
      collection do
        get :sales_report
      end
    end

    get '/analysis/orders_report', to: 'analysis#orders_report'
    get '/analysis/print', to: 'analysis#print'
    get '/analysis/print_expiration', to: 'analysis#print_expiration'
    get '/analysis/download_report', to: 'analysis#download_report'
    get '/activity_logs', to: 'activity_logs#index'
    get '/metabase_reports/metabase_report', to: 'metabase_reports#metabase_report'
    resources :stores do
      member do
        put :switch_publish
      end
    end

    resources :featured_products, only: [:index, :destroy] do
      collection do
        post :batch_create
        post :update_positions
      end
    end

    resources :stock_alerts, only: [:index]
    resources :stock_item_units, only: [:show, :edit, :update, :destroy] do
      collection do
        get :check
        post :number_info
        get :pricecompare_info
        get :batch_shipped
      end
    end

    resources :email_templates do
      member do
        post :test_mail
        get :customer_email_body
      end
      collection do
        get :compose
        get :search
        post :test_new_mail
        post :send_mail
      end
    end

    resources :tags do
      collection do
        post :batch_create
        post :batch_delete
        get :search
      end
    end

    resources :organisations, constraints: TenantAccess.new
    resources :metabase_settings
    resources :metabase_report_settings
    resource :invoice_settings
    resource :webhook do
      post :easypost_hook
      post :amazon_hook
      post :amazon_lwa_rotation
      post :walmart_hook
      post :ebay_return_hook
    end

    resources :image_uploader, only: [:new, :create]
  end

  namespace :api, defaults: { format: 'json' } do
    namespace :v2 do
      namespace :storefront do
        post "webhook", to: "campaign_webhooks#webhook"
        resources :blog_categories, only: [:index]
        resources :blog_posts, only: [:index, :show] do
          patch :like, on: :member
          patch :unlike, on: :member
        end
        resources :subscriptions do
          member do
            get :fetch_delivery_date
            get :fetch_delivery_intervals
            get :revise_status
            get :fetch_subscription_orders
          end
        end
        resources :stores, only: [:index]
        resources :invoices, only: [:show]
        resources :shipping_methods do
          get :list_local_pickup_shipping_methods, on: :collection
        end
        resources :local_pickup_eligibility do
          get :cart_eligible_for_local_pickup, on: :collection
        end
        resources :featured_products, only: [:index]
        resource :account, controller: :account do
          get :resend_confirmation
        end
        namespace :account do
          resources :return_authorizations, only: [:create, :index, :show, :new]
          resources :return_authorization_reasons, only: [:index]
        end
        namespace :intents do
          post :create
        end
        namespace :stripe do
          post :confirm
          post :webhook
        end
        resources :image_upload, only: [] do
          get :upload, on: :collection
          post :successful_upload, on: :collection
          delete :remove_image, on: :collection
        end
        resources :volume_prices, only: [] do
          collection do
            get :fetch_volume_pricing
          end
        end
        namespace :subscribe_newsletters do
          post :create
        end
      end
      namespace :platform do
        resources :listings, only: [:index]
        resources :section_items, only: [:index]
        resources :stock_items
        resources :stock_item_units, only: [:show, :update, :destroy] do
          get :numbers, on: :collection
          put :printed_numbers, on: :collection
          put :number_tracking_setting, on: :collection
        end
        resources :shipments do
          put :shipping_rates, on: :member
          patch :delete_shipping_label, on: :member
          get :attach_pdf, on: :member
          get :attach_amazon_pdf, on: :member
          patch :update_selected_shiping_rate_cost, on: :member
        end
      end
      namespace :data_feeds do
        # meta data feed API
        get '/meta/:slug', to: 'meta#rss_feed'
      end
    end
  end
  spree_path = Rails.application.routes.url_helpers.try(:spree_path, trailing_slash: true) || '/'
  get Spree.admin_path, to: redirect((spree_path + Spree.admin_path + '/dashboards').gsub('//', '/')), as: :admin_index
end

Spree::Core::Engine.add_routes do
  namespace :admin, path: Spree.admin_path do
    resources :promotions do
      resources :promotion_rules
      resources :promotion_actions
      member do
        post :clone
      end
    end

    resources :promotion_categories, except: [:show]

    resources :zones

    resources :stores do
      member do
        put :set_default
      end
    end

    resources :countries do
      resources :states
    end
    resources :states
    resources :tax_categories

    resources :products do
      resources :product_properties do
        collection do
          post :update_positions
        end
      end
      resources :images do
        collection do
          post :update_positions
        end
      end
      member do
        post :clone
        get :stock
      end
      resources :variants do
        collection do
          post :update_positions
        end
      end
      resources :variants_including_master, only: [:update]
      resources :prices, only: [:index, :create]
      resources :digitals, only: [:index, :create, :destroy]
    end

    resources :option_types do
      collection do
        post :update_positions
        post :update_values_positions
      end
    end

    resources :oauth_applications

    resources :properties do
      collection do
        get :filtered
      end
    end

    resources :prototypes do
      member do
        get :select
      end

      collection do
        get :available
      end
    end

    resources :orders, except: [:show] do
      member do
        get :cart
        post :resend
        get :open_adjustments
        get :close_adjustments
        put :approve
        put :cancel
        put :resume
        get :channel
        put :set_channel
        get :reset_digitals
        put :remove_image
        get :calculate_weight
        get :validate_stock_location
      end

      resources :state_changes, only: [:index]
      resources :tracker_logs, only: [:index]
      resource :customer, controller: 'orders/customer_details'
      resources :customer_returns, only: [:index, :new, :edit, :create, :update] do
        member do
          put :refund
        end
      end

      resources :adjustments
      resources :return_authorizations do
        member do
          put :cancel
        end
      end
      resources :payments do
        member do
          put :fire
        end

        resources :log_entries
        resources :refunds, only: [:new, :create, :edit, :update]
      end

      resources :reimbursements, only: [:index, :create, :show, :edit, :update] do
        member do
          post :perform
        end
      end
    end

    resource :general_settings do
      collection do
        post :clear_cache
      end
    end

    resources :subscriptions do
      member do
        patch :revise_status
      end
    end

    resources :return_items, only: [:update]

    resources :taxonomies do
      collection do
        post :update_positions
      end
      resources :taxons do
        member do
          delete :remove_icon
        end
      end
    end

    resources :taxons, only: [:index, :show]

    resources :reports, only: [:index] do
      collection do
        get :sales_total
        post :sales_total
      end
    end

    resources :reimbursement_types
    resources :refund_reasons, except: :show
    resources :return_authorization_reasons, except: :show

    resources :shipping_methods do
      collection do
        get :load_states
      end
    end
    resources :shipping_categories
    resources :stock_transfers, only: [:index, :show, :new, :create]
    resources :stock_locations do
      resources :stock_movements, except: [:edit, :update, :destroy]
      collection do
        post :transfer_stock
      end
    end

    resources :stock_items, only: [:create, :update, :destroy] do
      member do
        get :units
      end
    end

    resources :store_credit_categories
    resources :tax_rates
    resources :payment_methods do
      collection do
        post :update_positions
      end
    end
    resources :roles
    resources :subscribers, only: [:index] do
      resources :subscriber_behaviors, only: [:index]
    end

    resources :users do
      member do
        get :addresses
        put :addresses
        get :items
        get :orders
      end
      resources :store_credits
    end

    resources :cms_pages do
      resources :cms_sections, except: :index
    end

    resources :menus do
      resources :menu_items, except: :index do
        member do
          delete :remove_icon
        end
      end
    end

    resources :webhooks_subscribers
  end
end
