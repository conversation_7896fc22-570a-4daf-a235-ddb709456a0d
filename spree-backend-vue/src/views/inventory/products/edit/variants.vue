<template>
  <div class="product-variant">
    <template v-if="state.productOptions.length">
      <!-- option types -->
      <div class="options page-card">
        <div class="d-flex align-start justify-space-between">
          <span class="page-subtitle">{{ $t('products.Option Types') }}</span>
          <v-spacer />
          <v-btn icon variant="text">
            <v-icon
              :icon="isShowOptions ? '$expand' : '$collapse'"
              class="cursor-pointer"
              @click="isShowOptions = !isShowOptions"
            />
          </v-btn>
        </div>

        <v-expand-transition>
          <div v-show="isShowOptions" class="options-wrap mt-6">
            <div ref="optionsEl" class="option-types">
              <div
                class="options-item d-flex align-center mb-6"
                v-for="item in state.productOptions"
                :key="item.id"
                :data-id="item.id"
              >
                <div class="sort-handle d-flex align-center h-100">
                  <svg-icon name="basic-drag" size="24" type="dark" cursor="move" class="d-none" />
                </div>
                <span class="fz-14 font-weight-bold mx-4" v-text="item.name"></span>
                <v-spacer />
                <v-btn icon variant="text" @click="handleEditOptionType(item)">
                  <svg-icon name="editor-edit-fill" size="20" type="dark" />
                </v-btn>
                <v-btn icon variant="text" class="d-none" @click="handleRemoveOptionType(item.id)">
                  <svg-icon name="basic-trash-can" size="20" type="dark" />
                </v-btn>
              </div>
            </div>
            <div class="d-flex-end">
              <v-btn
                rounded="lg"
                prepend-icon="$plus"
                color="grey-darken-4"
                class="text-none"
                :text="$t('products.Add Option Type')"
                @click="handleOpenCreateCard"
              />
            </div>
          </div>
        </v-expand-transition>
      </div>

      <!-- variants -->
      <div class="variants page-card mt-4">
        <div class="d-flex-between">
          <span class="page-subtitle">{{ $t('products.Variants') }}</span>

          <v-btn icon variant="text">
            <v-icon
              :icon="isShowVariants ? '$expand' : '$collapse'"
              class="cursor-pointer"
              @click="isShowVariants = !isShowVariants"
            />
          </v-btn>
        </div>

        <v-expand-transition>
          <div v-show="isShowVariants" class="variants-list">
            <div class="d-flex-start overflow-x-auto scrollbar scrollbar-x mt-4 pb-2">
              <v-btn color="#F7F7F7" min-width="100" height="40" class="text-none" @click="handleClearFilterVariant">
                <span class="text" v-text="$t('All')"></span>
              </v-btn>

              <template v-for="item in state.productOptions" :key="item.id">
                <v-autocomplete
                  chips
                  multiple
                  hide-no-data
                  hide-details
                  v-model="state.filterVariant[item.id]"
                  density="compact"
                  variant="solo"
                  item-value="id"
                  item-title="name"
                  class="ml-2"
                  menu-icon="$expand"
                  bg-color="#F7F7F7"
                  min-width="150"
                  :label="item.name"
                  :items="item.option_values"
                  :placeholder="$t('products.Filter')"
                />
              </template>
            </div>

            <v-data-table
              hover
              class="axel-table mt-4"
              style="--v-border-color: transparent"
              :loading="loading && !isShowLibraryCard"
              :headers="headers"
              :items="availableVariants"
              :items-per-page="state.pageSize"
            >
              <template #item="{ item }">
                <tr class="v-data-table__tr">
                  <td class="v-data-table__td">
                    <span class="fz-14 font-weight-bold" v-text="item.name"></span>
                  </td>
                  <td class="v-data-table__td">
                    <div class="text-field rounded px-4 py-2" v-text="item.price"></div>
                  </td>
                  <td class="v-data-table__td">
                    <div class="text-field rounded px-4 py-2" v-text="item.compare_at_price"></div>
                  </td>
                  <td class="v-data-table__td">
                    <div class="text-field rounded px-4 py-2" v-text="item.compare_to_price"></div>
                  </td>
                  <td class="v-data-table__td">
                    <div class="text-field rounded px-4 py-2" v-text="item.sku"></div>
                  </td>
                  <td class="v-data-table__td">
                    <div class="d-flex-end">
                      <v-btn icon variant="text" @click="handleEditVariant(item)">
                        <svg-icon name="editor-edit-fill" size="20" type="dark" />
                      </v-btn>
                      <v-btn
                        icon
                        variant="text"
                        class="d-none"
                        :class="{ 'visibility-hidden': item.is_master }"
                        @click="handleRemoveVariant(item.id)"
                      >
                        <svg-icon name="basic-trash-can" size="20" type="dark" />
                      </v-btn>
                    </div>
                  </td>
                </tr>
              </template>

              <template #bottom>
                <axel-pagination
                  v-model:page="state.currentPage"
                  v-model:size="state.pageSize"
                  :total="state.totalCount"
                  @update="getProductVariants"
                />
              </template>
            </v-data-table>
          </div>
        </v-expand-transition>
      </div>
    </template>

    <!-- setup option -->
    <div v-else class="setup-option page-card">
      <div class="d-flex align-start justify-space-between mb-6">
        <span class="page-subtitle">{{ $t('products.Option Types') }}</span>
        <v-spacer />
        <div class="d-flex align-start fz-14">
          <span class="font-weight-bold mr-1" v-text="$t('products.Please note')"></span>
          <span v-html="$t('products.Please note desc')"></span>
        </div>
      </div>

      <div class="fz-14">
        <p class="mb-9" v-text="$t('products.Setup option desc 1')"></p>
        <p class="mb-9" v-text="$t('products.Setup option desc 2')"></p>
        <div class="d-flex justify-center">
          <v-btn
            rounded="lg"
            color="grey-darken-4"
            class="text-none"
            width="300"
            :text="$t('products.Setup option')"
            @click="handleToggleOptionCard(true)"
          />
        </div>
      </div>
    </div>

    <!-- create option -->
    <v-overlay no-click-animation scroll-strategy="block" class="d-flex-start-end" v-model="isShowOptionCard">
      <div class="side-modal page-form scrollbar">
        <v-toolbar color="#fff">
          <v-toolbar-title :text="optionCardTitle" />
          <v-spacer></v-spacer>
          <v-btn icon="$close" @click="handleToggleOptionCard(false)" />
        </v-toolbar>

        <v-card-item v-show="!isEditOption">
          <div class="d-flex align-center justify-space-between mb-6">
            <span class="fz-16 font-weight-bold" v-text="$t('products.Options')"></span>

            <v-btn
              rounded="lg"
              prepend-icon="$plus"
              color="grey-darken-4"
              class="text-none"
              :text="$t('products.New Option Type')"
              @click="handleCreateOptionType"
            />
          </div>

          <template v-if="isShowPresetText">
            <div class="fz-14 color-black mb-6">
              <p class="mb-2" v-text="$t('products.New option type desc 1')"></p>
              <p v-text="$t('products.New option type desc 2')"></p>
            </div>
          </template>

          <div v-show="availablePresetOptions.length" class="preset-option">
            <span class="fz-16">{{ $t('products.Select preset Option Type') }}</span>
            <v-select
              active
              chips
              multiple
              hide-details
              v-model="state.presetIds"
              density="compact"
              variant="outlined"
              item-value="id"
              item-title="name"
              class="preset-select my-4"
              :label="$t('products.Option Type')"
              :items="availablePresetOptions"
              :menu-props="{ modelValue: isShowPresetMenu, maxWidth: '50vw' }"
              @update:menu="handleTogglePresetMenu"
            >
              <template #item="{ props, item, index }">
                <v-list-item v-bind="props" :variant="index % 2 ? 'text' : 'tonal'">
                  <template #prepend="{ isActive }">
                    <v-list-item-action start>
                      <v-checkbox-btn :model-value="isActive"></v-checkbox-btn>
                    </v-list-item-action>
                  </template>

                  <template #title>
                    <div class="d-flex align-center">
                      <span class="flex-1-1 mr-3" v-text="item.raw.name"></span>
                      <span class="mx-1" v-for="i in item.raw.option_values" :key="i.id" v-text="i.presentation"></span>
                    </div>
                  </template>
                </v-list-item>
              </template>

              <template #append-item>
                <div class="d-flex justify-end pa-2">
                  <v-btn
                    width="130"
                    color="grey-darken-4"
                    class="text-none"
                    :text="$t('Apply')"
                    @click="handleSelectPresetOption"
                  ></v-btn>
                </div>
              </template>
            </v-select>
          </div>
        </v-card-item>

        <v-card-item>
          <template v-if="state.temp.length">
            <v-card variant="outlined" class="mb-4 px-4" v-for="item in state.temp">
              <v-toolbar color="#fff">
                <span class="fz-16 font-weight-bold" v-text="item.title"></span>
                <v-spacer />
                <v-btn icon v-show="!isEditOption" @click="handleRemoveOptionType(item.id)">
                  <v-icon icon="$close" />
                </v-btn>
              </v-toolbar>

              <!-- new options -->
              <template v-if="item.isNew">
                <v-text-field
                  active
                  v-model.trim="item.text"
                  variant="outlined"
                  density="compact"
                  :label="$t('products.Option Type')"
                >
                </v-text-field>
                <div class="options-list">
                  <template v-for="i in item.optionValues" :key="i.id">
                    <div class="d-flex-start mb-4">
                      <v-text-field
                        active
                        hide-details
                        v-model.trim="i.name"
                        variant="outlined"
                        density="compact"
                        class="mr-1"
                        :label="$t('products.Name')"
                        :data-id="i.id"
                        @keyup.enter="handleInitAddOptionValue(item)"
                      >
                        <template #prepend>
                          <svg-icon name="basic-drag" class="sort-handle" size="24" type="dark" cursor="move" />
                        </template>
                      </v-text-field>

                      <v-text-field
                        active
                        hide-details
                        v-model.trim="i.presentation"
                        variant="outlined"
                        density="compact"
                        class="ml-1 mr-2"
                        :label="$t('products.Presentation')"
                        :data-id="i.id"
                        @keyup.enter="handleInitAddOptionValue(item)"
                      />

                      <v-btn icon variant="text" @click="handleRemoveOptionValue(item, i.id)">
                        <v-icon icon="mdi-trash-can" color="#0a0a0a" />
                      </v-btn>
                    </div>
                  </template>
                </div>
              </template>
              <!-- preset options -->
              <template v-else>
                <div class="d-flex align-center flex-wrap">
                  <div class="d-flex align-center w-50 mb-4" v-for="i in item.optionValues">
                    <v-checkbox-btn v-model="i.selected" :disabled="i.disabled" :label="i.presentation || i.name" />
                  </div>
                </div>

                <div v-show="item.isShowAddField" class="d-flex-start mb-4 mt-2">
                  <v-text-field
                    active
                    hide-details
                    v-model.trim="item.name"
                    variant="outlined"
                    density="compact"
                    class="mr-1"
                    :label="$t('products.Name')"
                  />

                  <v-text-field
                    active
                    hide-details
                    v-model.trim="item.presentation"
                    variant="outlined"
                    density="compact"
                    class="ml-1 mr-2"
                    :label="$t('products.Presentation')"
                  />
                </div>
              </template>

              <div class="d-flex align-center justify-space-between mb-4">
                <v-btn
                  class="text-none"
                  variant="text"
                  prepend-icon="$plus"
                  :text="$t('products.Add Option Value')"
                  @click="handleInitAddOptionValue(item)"
                />
                <v-btn
                  v-show="item.isShowAddField"
                  color="#EAEAEA"
                  class="text-none mr-2"
                  :text="$t('products.Update Option Type')"
                  :disabled="!item.name || !item.presentation"
                  @click="handleAddOptionValue(item)"
                />
              </div>
            </v-card>

            <div v-show="state.temp.length" class="d-flex justify-end mb-4">
              <v-btn
                rounded="lg"
                color="grey-darken-4"
                class="text-none"
                size="large"
                :loading="loading"
                :disabled="!state.temp.length"
                :text="$t('products.Save Options for Product')"
                @click="handleSaveOptionTypes"
              />
            </div>
          </template>
        </v-card-item>
      </div>
    </v-overlay>

    <!-- create variant -->
    <v-overlay no-click-animation scroll-strategy="block" class="d-flex-start-end" v-model="isShowVariantCard">
      <div class="side-modal page-form scrollbar">
        <v-toolbar color="#fff">
          <v-toolbar-title :text="state.variant?.name" />
          <v-spacer></v-spacer>
          <v-btn icon @click="handleToggleVariantCard(false)">
            <v-icon icon="$close" />
          </v-btn>
        </v-toolbar>

        <template v-if="state.variant">
          <v-form validate-on="blur" class="px-4" :disabled="loading" @submit.prevent="handleSubmitVariant">
            <v-row>
              <v-col cols="12">
                <div class="d-flex-end mt-4">
                  <v-btn
                    color="#F7F7F7"
                    variant="elevated"
                    class="text-none"
                    :disabled="loading"
                    :text="$t('products.Track Inventory')"
                    @click="state.variant.track_inventory = !state.variant.track_inventory"
                  >
                    <template #prepend>
                      <v-icon :icon="`$checkbox${state.variant.track_inventory ? 'On' : 'Off'}`" />
                    </template>
                  </v-btn>
                </div>
              </v-col>
              <v-col cols="12">
                <div class="images pa-2">
                  <div class="images-title">
                    <span v-text="$t('products.Media')"></span>
                  </div>
                  <div class="images-list d-flex flex-wrap">
                    <div v-for="item in varinatImages" :key="item.id" class="images-item rounded d-flex-center">
                      <div class="axel-image">
                        <img :src="item.styles[2]?.url" width="100" height="100" alt="variant image" />
                      </div>
                      <svg-icon
                        name="basic-trash-can"
                        type="dark"
                        size="20"
                        cursor="pointer"
                        @click="handleRemoveVariantImage(item.id)"
                      />
                    </div>
                    <div class="is-additional images-item rounded d-flex-center">
                      <v-btn
                        flat
                        size="small"
                        color="#F7F7F7"
                        variant="elevated"
                        class="text-none"
                        :disabled="loading"
                        :text="$t('products.Add image')"
                        @click="fileEl.click()"
                      />
                    </div>
                    <div
                      v-show="state.variant.id != state.masterVariantId && masterVariantImages.length"
                      class="is-additional images-item rounded d-flex-center"
                    >
                      <v-btn
                        flat
                        size="small"
                        color="#1035A3"
                        variant="text"
                        class="text-none"
                        :disabled="loading"
                        :text="$t('products.Choose exiting')"
                        @click="handleToggleLibraryCard(true)"
                      />
                    </div>
                  </div>
                </div>
              </v-col>
              <v-col cols="6">
                <div class="text-h6 mb-6">
                  <span v-text="$t('products.Price')"></span>
                </div>

                <!-- price -->
                <div class="form-item mb-2">
                  <v-text-field
                    active
                    hide-spin-buttons
                    v-model.trim="state.variant.price"
                    min="0"
                    type="number"
                    density="compact"
                    variant="outlined"
                    :rules="[rules.required]"
                  >
                    <template #label>
                      <span v-text="$t('products.Price')"></span>
                      <svg-icon name="basic-asterik" size="12" />
                    </template>
                    <template #prepend-inner>
                      <svg-icon name="shopping-money" type="dark" />
                    </template>
                  </v-text-field>
                </div>
                <!-- compare at price -->
                <div class="form-item mb-2">
                  <v-text-field
                    active
                    hide-spin-buttons
                    v-model.trim="state.variant.compare_at_price"
                    min="0"
                    type="number"
                    density="compact"
                    variant="outlined"
                    :label="$t('products.Regular Price')"
                  >
                    <template #prepend-inner>
                      <svg-icon name="shopping-money" type="dark" />
                    </template>
                  </v-text-field>
                </div>
                <!-- compare to price -->
                <div class="form-item mb-2">
                  <v-text-field
                    active
                    hide-spin-buttons
                    v-model.trim="state.variant.compare_to_price"
                    min="0"
                    type="number"
                    density="compact"
                    variant="outlined"
                    :label="$t('products.Compare To')"
                  >
                    <template #prepend-inner>
                      <svg-icon name="shopping-money" type="dark" />
                    </template>
                  </v-text-field>
                </div>
                <!-- discontinue_on -->
                <div class="form-item mb-2">
                  <v-date-input
                    active
                    clearable
                    show-adjacent-months
                    v-model="state.variant.discontinue_on"
                    :label="$t('products.Discontinue On')"
                    variant="outlined"
                    density="compact"
                    prepend-icon=""
                    append-inner-icon="$calendar"
                    @keydown.prevent
                    @click:clear="state.variant.discontinue_on = null"
                  />
                </div>
                <!-- tax category -->
                <div class="form-item mb-2">
                  <v-autocomplete
                    active
                    clearable
                    v-model="state.variant.tax_category_id"
                    density="compact"
                    variant="outlined"
                    item-value="id"
                    item-title="name"
                    :label="$t('products.Tax Category')"
                    :items="state.taxCategories"
                  />
                </div>
              </v-col>
              <v-col cols="6">
                <div class="text-h6 mb-6">
                  <span v-text="$t('products.Shipping')"></span>
                </div>
                <!-- sku -->
                <div class="form-item mb-2">
                  <v-text-field
                    active
                    v-model.trim="state.variant.sku"
                    density="compact"
                    variant="outlined"
                    :label="$t('products.SKU')"
                  />
                </div>
                <!-- upc -->
                <div class="form-item mb-2">
                  <v-text-field
                    active
                    v-model.trim="state.variant.upc"
                    density="compact"
                    variant="outlined"
                    :label="$t('products.Upc')"
                  />
                </div>
                <!-- weight -->
                <div class="form-item mb-2 d-flex-start">
                  <v-text-field
                    active
                    v-model.trim="state.variant.lbs"
                    min="0"
                    density="compact"
                    variant="outlined"
                    :label="$t('products.Weight')"
                  >
                    <template #append-inner>
                      <span class="text-body-1">lbs</span>
                    </template>
                  </v-text-field>

                  <svg-icon name="basic-plus" type="dark" size="16" class="mx-1" />

                  <v-text-field active v-model.trim="state.variant.oz" min="0" density="compact" variant="outlined">
                    <template #append-inner>
                      <span class="text-body-1">oz</span>
                    </template>
                  </v-text-field>
                </div>
                <!-- height/width/depth -->
                <div class="form-item mb-2">
                  <div class="d-flex-start">
                    <v-text-field
                      active
                      v-model.trim="state.variant.height"
                      density="compact"
                      variant="outlined"
                      :label="$t('products.Height')"
                    />
                    <v-text-field
                      active
                      v-model.trim="state.variant.width"
                      density="compact"
                      variant="outlined"
                      class="mx-2"
                      :label="$t('products.Width')"
                    />
                    <v-text-field
                      active
                      v-model.trim="state.variant.depth"
                      density="compact"
                      variant="outlined"
                      :label="$t('products.Depth')"
                    />
                  </div>
                </div>
              </v-col>
              <!-- submit -->
              <v-col cols="12">
                <div class="form-item d-flex-end">
                  <v-btn
                    rounded="lg"
                    color="grey-darken-4"
                    class="text-none"
                    size="large"
                    type="submit"
                    :loading="loading && !isShowLibraryCard"
                    :text="$t('products.Save Variant')"
                  />
                </div>
              </v-col>
            </v-row>
          </v-form>
        </template>
      </div>
    </v-overlay>

    <!-- choose image from library -->
    <v-dialog persistent v-model="isShowLibraryCard" width="auto">
      <v-card width="650" max-width="90vw">
        <template #loader>
          <v-progress-linear :active="loading" color="success" indeterminate />
        </template>

        <v-toolbar color="#0A0A0A">
          <v-toolbar-title :text="$t('products.Upload variant image')" />
          <v-spacer></v-spacer>
          <v-btn icon :disabled="loading" @click="handleToggleLibraryCard(false)">
            <v-icon icon="$close" />
          </v-btn>
        </v-toolbar>

        <v-card-text>
          <div class="text-body-1">You can choose images from images library linked to product.</div>

          <div class="images my-4 ml-n2 border-none">
            <div class="images-list d-flex-start scrollbar overflow-x-auto">
              <div class="d-none images-item is-additional is-large rounded d-flex-center">
                <v-btn
                  flat
                  size="small"
                  color="#F7F7F7"
                  variant="elevated"
                  class="text-none"
                  :disabled="loading"
                  :text="$t('products.Add image')"
                  @click="fileEl.click()"
                />
              </div>

              <div
                v-for="item in masterVariantImages"
                :key="item.id"
                class="images-item is-large cursor-pointer rounded d-flex-center"
                @click="item.selected = !item.selected"
              >
                <div class="axel-image">
                  <img :src="item.styles[2]?.url" width="160" height="160" alt="variant image" />
                </div>

                <svg-icon type="dark" size="20" :name="item.selected ? 'basic-check-square-fill' : 'basic-square'" />
              </div>
            </div>
          </div>
        </v-card-text>

        <v-card-actions class="px-6 py-4">
          <v-btn
            variant="elevated"
            color="#EAEAEA"
            class="text-none"
            :text="$t('Cancel')"
            :disabled="loading"
            @click="handleToggleLibraryCard(false)"
          />
          <v-btn
            variant="elevated"
            color="#0A0A0A"
            class="text-none ml-4"
            :text="$t('Upload image')"
            :loading="loading"
            :disabled="!masterVariantImages.find((i) => i.selected)"
            @click="handleChooseVariantImage"
          />
        </v-card-actions>
      </v-card>
    </v-dialog>

    <input ref="fileEl" type="file" multiple accept="image/*" class="d-none" @change="handelUploadImage" />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { ref, reactive, readonly, computed, inject, onBeforeMount } from 'vue'
import { VDateInput } from 'vuetify/labs/VDateInput'
import { createSortHandler } from '@/utils/sort'
import { useNotificationStore, useConfirmStore } from '@/stores'
import AxelPagination from '@/components/pagination'

const i18n = useI18n()
const route = useRoute()
const fetch = inject('$fetch')
const rules = inject('rules')

const { sendNotification } = useNotificationStore()
const { showConfirmDialog } = useConfirmStore()

const fileEl = ref(null)
const optionsEl = ref(null)

const loading = ref(false)
const isEditOption = ref(false)
const isShowOptionCard = ref(false)
const isShowVariantCard = ref(false)
const isShowLibraryCard = ref(false)
const isShowPresetText = ref(true)
const isShowPresetMenu = ref(false)
const isShowOptions = ref(true)
const isShowVariants = ref(true)

const headers = readonly([
  { title: i18n.t('products.Variants'), key: 'name', sortable: false },
  { title: i18n.t('products.Price'), key: 'price', sortable: false },
  { title: i18n.t('products.Regular Price'), key: 'compare_at_price', sortable: false },
  { title: i18n.t('products.Compare To'), key: 'compare_to_price', sortable: false },
  { title: i18n.t('products.SKU'), key: 'sku', sortable: false },
  { title: '', key: 'actions', width: 120, sortable: false }
])

const state = reactive({
  product: null,
  // all option types
  optionTypes: [],
  // product option types
  productOptions: [],
  // product variants
  variants: [],
  masterVariantId: null,
  // product images
  images: [],
  // tax categories
  taxCategories: [],
  // edit variant
  variant: null,
  filterVariant: {},
  // page params
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  // for create option types
  presetIds: [],
  temp: []
})

const optionCardTitle = computed(() => {
  if (isEditOption.value) {
    const { title } = state.temp[0] || {}

    return i18n.t('products.Option edit', { title })
  }

  return i18n.t('products.Option creation')
})

const availablePresetOptions = computed(() =>
  state.optionTypes.filter((i) => !state.productOptions.find(({ id }) => i.id == id))
)

const availableVariants = computed(() => {
  let variants = state.variants

  Object.values(state.filterVariant).forEach((ids) => {
    if (ids.length) {
      variants = variants.filter(({ option_value_ids }) => ids.find((id) => option_value_ids.includes(id)))
    }
  })

  return variants
})

const varinatImages = computed(() => {
  const id = state.variant?.id

  return state.images.filter((i) => i.variant_id == id)
})

const masterVariantImages = computed(() => state.images.filter((i) => i.variant_id == state.masterVariantId))

const handleToggleOptionCard = (value) => {
  isEditOption.value = false
  isShowOptionCard.value = value
  isShowPresetText.value = true

  state.temp.length = state.presetIds.length = 0
}

const handleToggleVariantCard = (value) => {
  isShowVariantCard.value = value
}

const handleToggleLibraryCard = (value) => {
  if (value) {
    state.images = state.images.map((i) => Object.assign(i, { selected: false }))
  } else if (state.variant) {
    handleToggleVariantCard(true)
  }

  isShowLibraryCard.value = value
}

const handleOpenCreateCard = () => {
  isShowOptionCard.value = true
  isShowPresetText.value = false

  state.presetIds.length = 0
  state.temp.length = 0
}

const handleTogglePresetMenu = (value) => {
  if (!value) {
    handleSelectPresetOption()
  }

  isShowPresetMenu.value = value
}

// click apply button
const handleSelectPresetOption = () => {
  state.presetIds.forEach((id) => {
    const item = state.optionTypes.find((i) => i.id == id)
    const isAdded = state.temp.find((i) => i.id == id)

    if (!isAdded) {
      state.temp.push({
        id,
        title: item.name,
        name: '',
        presentation: '',
        isShowAddField: false,
        optionValues: item.option_values.map(({ id, name, presentation }) => ({
          id,
          name: presentation || name,
          selected: true,
          disabled: false
        }))
      })
    }
  })

  isShowPresetText.value = false

  isShowPresetMenu.value = false

  // force preset select to lose focus
  setTimeout(() => {
    document.querySelector('.preset-select input')?.blur()
  }, 100)
}

// create new option type
const handleCreateOptionType = () => {
  const data = {
    isNew: true,
    id: window.getRandomString(),
    title: i18n.t('products.New Option Type'),
    text: '',
    isShowAddField: false,
    optionValues: [
      {
        id: window.getRandomString(),
        name: '',
        presentation: '',
        selected: true
      }
    ]
  }

  state.temp.push(data)

  setTimeout(initSortNewOptions, 3000, data.id)
}

const handleInitAddOptionValue = (item) => {
  if (item.isNew) {
    item.optionValues.push({
      id: window.getRandomString(),
      name: '',
      presentation: '',
      selected: true,
      draft: true
    })
  } else {
    if (item.name && item.presentation) {
      handleAddOptionValue(item)
    }

    item.isShowAddField = true
  }
}

const handleAddOptionValue = (item) => {
  const { name, presentation } = item

  item.name = item.presentation = ''
  item.isShowAddField = false

  item.optionValues.push({
    id: window.getRandomString(),
    name,
    presentation,
    selected: true,
    draft: true
  })
}

const handleRemoveOptionValue = (item, id) => {
  item.optionValues = item.optionValues.filter((i) => i.id != id)
}

// click save option button - OPTIMIZED FOR BULK OPERATIONS
const handleSaveOptionTypes = () => {
  loading.value = true

  const promises = state.temp
    .map(({ id, title, text, optionValues, isNew }) => {
      let promise = null
      const selected = optionValues.filter((i) => (isNew ? i.name && i.presentation : i.selected))

      if (!selected.length || (isNew && !text)) {
        return false
      }

      if (isNew) {
        // add option type
        promise = fetch.post('/option_types', {
          option_type: {
            name: text,
            presentation: text,
            filterable: true,
            option_values_attributes: selected.map(({ name, presentation }) => ({ name, presentation }))
          }
        })
      } else {
        // update option type
        const newItems = []
        const presetItems = []

        selected.filter((i) => {
          if (i.draft) {
            newItems.push(i)
          } else {
            presetItems.push(i)
          }
        })

        if (newItems.length) {
          promise = fetch
            .patch(`/option_types/${id}`, {
              option_type: {
                option_values_attributes: newItems.map(({ name, presentation }) => ({ name, presentation }))
              }
            })
            .then((data) => {
              data.option_values = presetItems.concat(
                data.option_values.filter((i) =>
                  newItems.find(({ name, presentation }) => i.name == name && i.presentation == presentation)
                )
              )

              return data
            })
        } else {
          promise = Promise.resolve({ id, name: title, option_values: selected })
        }
      }

      return promise.then(({ id, name, option_values: optionValues }) => ({ id, name, optionValues }))
    })
    .filter(Boolean)

  const promise = Promise.all(promises)
    .then((data) => {
      if (!data.length) {
        return false
      }

      let promises = []
      const { id: productId } = state.product
      const generateVariants = (data) => {
        const len = data.length

        if (len == 0) return []
        if (len == 1) return data[0].optionValues.map((i) => i.id)

        const result = []
        const restCombinations = generateVariants(data.slice(1))

        data[0].optionValues.forEach(({ id }) => {
          restCombinations.forEach((item) => {
            result.push([].concat(id, item))
          })
        })

        return result
      }

      if (isEditOption.value) {
        // create new variants using bulk API
        const [{ id, optionValues }] = data
        const options = state.productOptions
          .map((item) => {
            if (item.id == id) {
              return Object.assign({}, item, {
                optionValues: optionValues.filter(
                  ({ id }) => !state.variants.find(({ option_value_ids: ids }) => ids.includes(id))
                )
              })
            }

            const values = item.option_values.filter(({ id }) =>
              state.variants.find(({ option_value_ids: ids }) => ids.includes(id))
            )

            return (
              values.length &&
              Object.assign({}, item, {
                optionValues: values
              })
            )
          })
          .filter(Boolean)

        const variantCombinations = generateVariants(options)
        if (variantCombinations.length > 0) {
          const variantsData = variantCombinations.map((ids) => ({
            option_value_ids: [].concat(ids)
          }))

          console.log('Bulk creating variants (edit option):', variantsData.length, 'variants')

          promises = [
            fetch.post(`/products/${productId}/variants/bulk_create`, {
              variants: variantsData
            })
          ]
        } else {
          promises = []
        }
      } else {
        // create variants using bulk API
        if (state.variants.length <= 1) {
          const variantCombinations = generateVariants(data)
          if (variantCombinations.length > 0) {
            const variantsData = variantCombinations.map((ids) => ({
              option_value_ids: [].concat(ids)
            }))

            console.log('Bulk creating variants (new product):', variantsData.length, 'variants')

            promises = [
              fetch.post(`/products/${productId}/variants/bulk_create`, {
                variants: variantsData
              })
            ]
          } else {
            promises = []
          }
        } else {
          promises = []

          const updateIndexes = []
          const options = state.productOptions.map((item) => {
            return Object.assign({}, item, {
              optionValues: item.option_values.filter(({ id }) =>
                state.variants.find(({ option_value_ids: ids }) => ids.includes(id))
              )
            })
          })
          const variants = generateVariants(options.concat(data))

          // Prepare bulk update data
          const variantsToUpdate = []
          const variantsToCreate = []

          state.variants.forEach(({ id, is_master, option_value_ids: ids }) => {
            if (is_master) {
              return false
            }

            const index = variants.findIndex((item) => ids.every((id) => item.includes(id)))

            if (index >= 0) {
              // Collect variants to update
              variantsToUpdate.push({
                id: id,
                option_value_ids: variants[index]
              })
              updateIndexes.push(index)
            }
          })

          // Prepare new variants for creation
          variants.forEach((option_value_ids, index) => {
            if (!updateIndexes.includes(index)) {
              variantsToCreate.push({ option_value_ids })
            }
          })

          // Use bulk operations
          if (variantsToUpdate.length > 0) {
            console.log('Bulk updating variants:', variantsToUpdate.length, 'variants')
            promises.push(
              fetch.patch(`/products/${productId}/variants/bulk_update`, {
                variants: variantsToUpdate
              })
            )
          }

          if (variantsToCreate.length > 0) {
            console.log('Bulk creating variants (mixed operation):', variantsToCreate.length, 'variants')
            promises.push(
              fetch.post(`/products/${productId}/variants/bulk_create`, {
                variants: variantsToCreate
              })
            )
          }
        }

        // update product option types
        state.productOptions.push(...data)

        const promise = fetch
          .patch(`/products/${route.params.id}`, {
            product: {
              option_type_ids: state.productOptions.map(({ id }) => id)
            }
          })
          .then((data) => {
            state.product = data
          })

        promises.push(promise)
      }

      promises.push(getOptionTypes())

      return Promise.all(promises).then(getProductVariants)
    })
    .then(getProduct)
    .finally(() => {
      getProductOptions()

      isEditOption.value = false
      loading.value = false

      state.temp.length = state.presetIds.length = 0
    })

  isShowOptionCard.value = false

  window.showLoading(promise)
}

// click edit option type button
const handleEditOptionType = (item) => {
  isEditOption.value = true
  isShowOptionCard.value = true
  isShowPresetText.value = true

  state.presetIds = [item.id]
  state.temp = [
    {
      id: item.id,
      title: item.name,
      name: '',
      presentation: '',
      isShowAddField: false,
      optionValues: item.option_values.map((item) => {
        const isExist = !!state.variants.find(({ option_value_ids: ids }) => ids.includes(item.id))

        return Object.assign(
          {
            selected: isExist,
            disabled: isExist
          },
          item
        )
      })
    }
  ]
}

// remove option type
const handleRemoveOptionType = (id) => {
  if (state.temp.length) {
    // remove option from create card
    state.temp = state.temp.filter((i) => i.id != id)

    state.presetIds = state.presetIds.filter((i) => i != id)

    if (!state.temp.length) {
      isShowPresetText.value = true
    }
  } else {
    // remove option from list
    showConfirmDialog({
      title: 'Warning',
      message: 'Are you sure you want to delete it?',
      confirmButtonText: 'Remove',
      onConfirm() {
        const ids = []

        state.productOptions.forEach((i) => {
          if (i.id != id) {
            ids.push(i.id)
          }
        })

        return fetch
          .patch(`/products/${route.params.id}`, {
            product: {
              option_type_ids: ids
            }
          })
          .then(() => {
            state.productOptions = state.productOptions.filter((i) => i.id != id)

            sendNotification({
              message: 'Option Type has been successfully removed!',
              type: 'success'
            })
          })
      }
    })
  }
}

// edit variant
const handleEditVariant = (item) => {
  const {
    id,
    name,
    price,
    compare_at_price,
    compare_to_price,
    sku,
    upc,
    weight = 0,
    height,
    width,
    depth,
    discontinue_on,
    tax_category_id,
    option_value_ids,
    track_inventory
  } = item

  state.variant = {
    id,
    name,
    option_value_ids,
    price,
    compare_at_price,
    compare_to_price,
    discontinue_on,
    tax_category_id,
    sku,
    upc,
    width,
    height,
    depth,
    track_inventory,
    lbs: parseInt(weight / 16),
    oz: weight % 16
  }

  isShowVariantCard.value = true
}

// remove variant
const handleRemoveVariant = (id) => {
  showConfirmDialog({
    title: 'Warning',
    message: 'Are you sure you want to delete it?',
    confirmButtonText: 'Remove',
    onConfirm() {
      return fetch.delete(`/products/${route.params.id}/variants/${id}`).then(() => {
        state.variants = state.variants.filter((i) => i.id != id)

        sendNotification({
          message: 'Delete successfully!',
          type: 'success'
        })
      })
    }
  })
}

// filter variant
const handleClearFilterVariant = () => {
  state.filterVariant = {}
}

// submit create/edit variant
const handleSubmitVariant = async (evt) => {
  const { valid } = await evt.then()

  if (!valid) {
    return false
  }

  let weight = 0
  const { id, lbs, oz } = state.variant

  if (lbs) {
    weight = lbs * 16
  }

  if (oz) {
    weight += +oz
  }

  loading.value = true

  fetch({
    url: `/products/${route.params.id}/variants/${id ?? ''}`,
    method: id ? 'PATCH' : 'POST',
    data: {
      variant: { weight, ...state.variant }
    }
  })
    .then(getProductVariants)
    .then(() => {
      isShowVariantCard.value = false

      sendNotification({
        type: 'success',
        message: i18n.t(id ? 'Updated successfully' : 'Created successfully')
      })
    })
    .finally(() => {
      loading.value = false
      state.variant = null
    })
}

// upload variant image
const handelUploadImage = (evt) => {
  const { id: productId } = route.params
  const { id: variantId } = state.variant

  const promises = Array.from(evt.target.files).map((file) => {
    file.id = window.getRandomString()

    return fetch.post(
      `/products/${productId}/images`,
      {
        image: {
          variant_id: variantId,
          attachment: file
        }
      },
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    )
  })
  const promise = Promise.all(promises).then(getProductImages)

  window.showLoading(promise)

  evt.target.value = ''
}

// remove variant image
const handleRemoveVariantImage = (id) => {
  showConfirmDialog({
    title: 'Warning',
    message: 'Are you sure you want to delete it?',
    confirmButtonText: 'Remove',
    onConfirm() {
      const { id: productId } = route.params

      return fetch.delete(`/products/${productId}/images/${id}`).then(() => {
        state.images = state.images.filter((i) => i.id != id)

        sendNotification({
          message: i18n.t('Delete successfully'),
          type: 'success'
        })
      })
    }
  })
}

// handle choose image from master variant images
const handleChooseVariantImage = () => {
  const { id: productId } = route.params
  const { id: variantId } = state.variant
  const images = state.images
    .filter((i) => i.selected)
    .map(({ id }) => {
      return fetch.patch(`/products/${productId}/images/${id}`, { image: { variant_id: variantId } })
    })

  loading.value = true

  Promise.all(images)
    .then(getProductImages)
    .then(() => handleToggleLibraryCard(false))
    .finally(() => {
      loading.value = false
    })
}

const getProduct = () => {
  return fetch.get(`/products/${route.params.id}`).then((data) => {
    state.product = data
  })
}

const getOptionTypes = () => {
  return fetch.get('/option_types').then((data) => {
    state.optionTypes = data

    return data
  })
}

const getProductVariants = () => {
  return fetch
    .get(`/products/${route.params.id}/variants`, {
      params: {
        page: state.currentPage,
        per_page: state.pageSize
      }
    })
    .then(({ totalCount, data }) => {
      const variants = []

      data.forEach((item) => {
        if (item.is_master) {
          item.name = ''

          variants.unshift(item)

          state.masterVariantId = item.id
        } else {
          item.name = item.option_values
            .sort((a, b) => a.option_type_id - b.option_type_id)
            .map(({ presentation, name }) => presentation || name)
            .join('/')

          variants.push(item)
        }
      })

      if (data.length > 1) {
        data = data.filter((item) => !item.is_master)
      }

      state.variants = data
      state.totalCount = totalCount
    })
}

const getProductImages = () => {
  const { id: productId } = route.params

  return fetch.get(`/products/${productId}/images`).then((data) => {
    state.images = data.map((i) => Object.assign({ selected: false }, i))
  })
}

const getTaxCategories = () => {
  return fetch.get('/tax_categories').then((data) => {
    state.taxCategories = data
  })
}

const getProductOptions = () => {
  const { option_type_ids: ids } = state.product

  state.productOptions = ids.map((id) => state.optionTypes.find((i) => i.id == id)).filter(Boolean)
}

const initSortNewOptions = (id) => {
  const els = Array.from(document.querySelectorAll('.options-list'))

  els.forEach((el) => {
    if (!el.dataset.sortable) {
      createSortHandler(el, {
        getData() {
          const data = state.temp.find((i) => i.id == id)

          return data?.optionValues
        },
        forceFallback: true,
        callback(id, position) {}
      })

      el.dataset.id = id
      el.dataset.sortable = true
    }
  })
}

onBeforeMount(() => {
  const promise = Promise.all([getProduct(), getOptionTypes(), getProductVariants()]).then(getProductOptions)

  getTaxCategories()
  getProductImages()

  window.showLoading(promise)
})

/*
// The sorting function has no practical effect here
onMounted(() => {
  const initSortOptions = () => {
    createSortHandler(optionsEl.value, {
      getData() {
        return state.productOptions
      },
      forceFallback: false,
      callback(id, position) {
        console.log(id, position)
      }
    })
  }

  if (state.productOptions.length) {
    initSortOptions()
  } else {
    const unwatch = watch(
      state.productOptions,
      (value) => {
        if (value.length) {
          unwatch()

          initSortOptions()
        }
      },
      { flush: 'post' }
    )
  }
})
*/
</script>

<style lang="less" scoped>
.setup-option {
  width: 600px;
  max-width: 90vw;
}

.options {
  & &-item {
    height: 48px;
    padding: 8px 24px;
    border-radius: 8px;
    background-color: #f7f7f7;
  }
}

.variants {
  & &-bar {
    .text {
      color: #1035a3;
    }
  }
}

.images {
  border-radius: 8px;
  border: 1px solid #cfcfcf;
  user-select: none;

  & &-title {
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    margin-bottom: 8px;
  }

  & &-item {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 4px;

    &.is-large {
      flex: 0 0 160px;
      width: 160px;
      height: 160px;
      margin: 8px;
    }

    &.is-additional {
      border: 1px dashed #cfcfcf;
      background-color: transparent;
    }

    .svg-icon {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      padding: 4px;
      border-radius: 2px;
      background-color: #fff;
    }
  }
}

.text-field {
  min-height: 40px;
  color: #a1a1a1;
  border: 1px solid #b8b8b8;
}
</style>
